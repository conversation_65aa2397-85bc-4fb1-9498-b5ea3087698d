2025-07-19 12:39:55,620 - __main__ - INFO - Connecting to MongoDB...
2025-07-19 12:40:08,860 - config.database - INFO - Successfully connected to MongoDB: referral_bot
2025-07-19 12:40:10,505 - config.database - INFO - Database collections and indexes initialized successfully
2025-07-19 12:40:10,506 - __main__ - INFO - Initializing Telegram bot...
2025-07-19 12:40:11,059 - __main__ - INFO - All handlers registered successfully
2025-07-19 12:40:15,949 - services.admin_service - INFO - Initialized admin settings for 4 admins
2025-07-19 12:40:15,994 - __main__ - INFO - Bot info updated: @demoxxyyzzbot (INST DEMO)
2025-07-19 12:40:15,995 - __main__ - INFO - Admin settings and level rewards system initialized
2025-07-19 12:40:15,996 - __main__ - INFO - Bot initialization completed successfully
2025-07-19 12:40:15,996 - __main__ - INFO - Starting bot polling...
2025-07-19 12:40:16,218 - apscheduler.scheduler - INFO - Scheduler started
2025-07-19 12:40:16,440 - __main__ - INFO - Bot is now running and polling for updates...
2025-07-19 12:40:22,005 - services.force_subscription_service - ERROR - Error checking main channel subscription: Member list is inaccessible
2025-07-19 12:41:05,951 - services.force_subscription_service - ERROR - Error checking main channel subscription: Member list is inaccessible
2025-07-19 12:41:16,221 - services.force_subscription_service - ERROR - Error checking main channel subscription: Member list is inaccessible
2025-07-19 12:46:35,780 - __main__ - INFO - Received signal 2, initiating shutdown...
2025-07-19 12:46:35,780 - __main__ - INFO - Shutting down bot...
2025-07-19 12:46:36,388 - apscheduler.scheduler - INFO - Scheduler has been shut down
2025-07-19 12:46:36,433 - config.database - INFO - Disconnected from MongoDB
2025-07-19 12:46:36,434 - __main__ - INFO - Bot shutdown completed
2025-07-19 12:46:43,127 - __main__ - INFO - Connecting to MongoDB...
2025-07-19 12:46:56,237 - config.database - INFO - Successfully connected to MongoDB: referral_bot
2025-07-19 12:46:58,179 - config.database - INFO - Database collections and indexes initialized successfully
2025-07-19 12:46:58,179 - __main__ - INFO - Initializing Telegram bot...
2025-07-19 12:46:59,193 - __main__ - INFO - All handlers registered successfully
2025-07-19 12:47:00,134 - services.admin_service - INFO - Initialized admin settings for 4 admins
2025-07-19 12:47:00,187 - __main__ - INFO - Bot info updated: @demoxxyyzzbot (INST DEMO)
2025-07-19 12:47:00,187 - __main__ - INFO - Admin settings and level rewards system initialized
2025-07-19 12:47:00,187 - __main__ - INFO - Bot initialization completed successfully
2025-07-19 12:47:00,188 - __main__ - INFO - Starting bot polling...
2025-07-19 12:47:00,385 - apscheduler.scheduler - INFO - Scheduler started
2025-07-19 12:47:00,583 - __main__ - INFO - Bot is now running and polling for updates...
2025-07-19 13:13:36,188 - __main__ - INFO - Received signal 2, initiating shutdown...
2025-07-19 13:13:36,190 - __main__ - INFO - Shutting down bot...
2025-07-19 13:13:39,844 - apscheduler.scheduler - INFO - Scheduler has been shut down
2025-07-19 13:13:39,901 - config.database - INFO - Disconnected from MongoDB
2025-07-19 13:13:39,902 - __main__ - INFO - Bot shutdown completed
2025-07-19 13:13:46,986 - __main__ - INFO - Connecting to MongoDB...
2025-07-19 13:14:00,048 - config.database - INFO - Successfully connected to MongoDB: referral_bot
2025-07-19 13:14:01,796 - config.database - INFO - Database collections and indexes initialized successfully
2025-07-19 13:14:01,796 - __main__ - INFO - Initializing Telegram bot...
2025-07-19 13:14:02,358 - __main__ - INFO - All handlers registered successfully
2025-07-19 13:14:03,253 - services.admin_service - INFO - Initialized admin settings for 4 admins
2025-07-19 13:14:03,299 - __main__ - INFO - Bot info updated: @demoxxyyzzbot (INST DEMO)
2025-07-19 13:14:03,300 - __main__ - INFO - Admin settings and level rewards system initialized
2025-07-19 13:14:03,301 - __main__ - INFO - Bot initialization completed successfully
2025-07-19 13:14:03,302 - __main__ - INFO - Starting bot polling...
2025-07-19 13:14:03,506 - apscheduler.scheduler - INFO - Scheduler started
2025-07-19 13:14:03,712 - __main__ - INFO - Bot is now running and polling for updates...
2025-07-19 13:39:55,931 - handlers.callback_handlers - ERROR - Error in callback handler for data 'extraRewards': Query is too old and response timeout expired or query id is invalid
2025-07-19 13:39:56,302 - __main__ - ERROR - Error in callback handler: Query is too old and response timeout expired or query id is invalid
2025-07-19 13:39:57,051 - handlers.callback_handlers - ERROR - Error in callback handler for data 'extraRewards': Query is too old and response timeout expired or query id is invalid
2025-07-19 13:39:57,435 - __main__ - ERROR - Error in callback handler: Query is too old and response timeout expired or query id is invalid
2025-07-19 13:39:58,175 - handlers.callback_handlers - ERROR - Error in callback handler for data 'extraRewards': Query is too old and response timeout expired or query id is invalid
2025-07-19 13:39:58,552 - __main__ - ERROR - Error in callback handler: Query is too old and response timeout expired or query id is invalid
2025-07-19 13:40:01,200 - handlers.callback_handlers - ERROR - Error in callback handler for data 'joined': Query is too old and response timeout expired or query id is invalid
2025-07-19 13:40:01,579 - __main__ - ERROR - Error in callback handler: Query is too old and response timeout expired or query id is invalid
2025-07-19 13:49:34,816 - __main__ - INFO - Received signal 2, initiating shutdown...
2025-07-19 13:49:34,816 - __main__ - INFO - Shutting down bot...
2025-07-19 13:49:38,546 - apscheduler.scheduler - INFO - Scheduler has been shut down
2025-07-19 13:49:38,596 - config.database - INFO - Disconnected from MongoDB
2025-07-19 13:49:38,597 - __main__ - INFO - Bot shutdown completed
2025-07-19 13:49:52,959 - __main__ - INFO - Connecting to MongoDB...
2025-07-19 13:50:06,094 - config.database - INFO - Successfully connected to MongoDB: referral_bot
2025-07-19 13:50:07,911 - config.database - INFO - Database collections and indexes initialized successfully
2025-07-19 13:50:07,912 - __main__ - INFO - Initializing Telegram bot...
2025-07-19 13:50:09,118 - __main__ - INFO - All handlers registered successfully
2025-07-19 13:50:10,293 - services.admin_service - INFO - Initialized admin settings for 4 admins
2025-07-19 13:50:10,338 - __main__ - INFO - Bot info updated: @demoxxyyzzbot (INST DEMO)
2025-07-19 13:50:10,339 - __main__ - INFO - Admin settings and level rewards system initialized
2025-07-19 13:50:10,340 - __main__ - INFO - Bot initialization completed successfully
2025-07-19 13:50:10,341 - __main__ - INFO - Starting bot polling...
2025-07-19 13:50:10,547 - apscheduler.scheduler - INFO - Scheduler started
2025-07-19 13:50:10,753 - __main__ - INFO - Bot is now running and polling for updates...
2025-07-19 15:21:43,999 - __main__ - INFO - Received signal 2, initiating shutdown...
2025-07-19 15:21:44,011 - __main__ - INFO - Shutting down bot...
2025-07-19 15:21:44,631 - apscheduler.scheduler - INFO - Scheduler has been shut down
2025-07-19 15:21:44,691 - config.database - INFO - Disconnected from MongoDB
2025-07-19 15:21:44,691 - __main__ - INFO - Bot shutdown completed
2025-07-19 15:21:54,836 - __main__ - INFO - Connecting to MongoDB...
2025-07-19 15:22:07,614 - config.database - INFO - Successfully connected to MongoDB: referral_bot
2025-07-19 15:22:09,291 - config.database - INFO - Database collections and indexes initialized successfully
2025-07-19 15:22:09,293 - __main__ - INFO - Initializing Telegram bot...
2025-07-19 15:22:09,805 - __main__ - INFO - All handlers registered successfully
2025-07-19 15:22:10,675 - services.admin_service - INFO - Initialized admin settings for 4 admins
2025-07-19 15:22:10,721 - __main__ - INFO - Bot info updated: @demoxxyyzzbot (INST DEMO)
2025-07-19 15:22:10,721 - __main__ - INFO - Admin settings and level rewards system initialized
2025-07-19 15:22:10,722 - __main__ - INFO - Bot initialization completed successfully
2025-07-19 15:22:10,723 - __main__ - INFO - Starting bot polling...
2025-07-19 15:22:10,927 - apscheduler.scheduler - INFO - Scheduler started
2025-07-19 15:22:11,135 - __main__ - INFO - Bot is now running and polling for updates...
2025-07-19 15:30:37,400 - handlers.callback_handlers - ERROR - Error in callback handler for data 'promotionReport': Query is too old and response timeout expired or query id is invalid
2025-07-19 15:30:37,773 - __main__ - ERROR - Error in callback handler: Query is too old and response timeout expired or query id is invalid
2025-07-19 15:30:38,519 - handlers.callback_handlers - ERROR - Error in callback handler for data 'promotionReport': Query is too old and response timeout expired or query id is invalid
2025-07-19 15:30:38,896 - __main__ - ERROR - Error in callback handler: Query is too old and response timeout expired or query id is invalid
2025-07-19 15:30:39,756 - handlers.callback_handlers - ERROR - Error in callback handler for data 'promotionReport': Query is too old and response timeout expired or query id is invalid
2025-07-19 15:30:40,149 - __main__ - ERROR - Error in callback handler: Query is too old and response timeout expired or query id is invalid
2025-07-19 15:30:40,893 - handlers.callback_handlers - ERROR - Error in callback handler for data 'withdrawalRecord': Query is too old and response timeout expired or query id is invalid
2025-07-19 15:30:41,300 - __main__ - ERROR - Error in callback handler: Query is too old and response timeout expired or query id is invalid
2025-07-19 15:30:42,201 - handlers.callback_handlers - ERROR - Error in callback handler for data 'promotionReport': Query is too old and response timeout expired or query id is invalid
2025-07-19 15:30:42,571 - __main__ - ERROR - Error in callback handler: Query is too old and response timeout expired or query id is invalid
2025-07-19 15:48:33,703 - __main__ - INFO - Received signal 2, initiating shutdown...
2025-07-19 15:48:33,707 - __main__ - INFO - Shutting down bot...
2025-07-19 15:48:34,502 - apscheduler.scheduler - INFO - Scheduler has been shut down
2025-07-19 15:48:34,542 - config.database - INFO - Disconnected from MongoDB
2025-07-19 15:48:34,542 - __main__ - INFO - Bot shutdown completed
2025-07-20 10:20:29,500 - __main__ - INFO - Connecting to MongoDB...
2025-07-20 10:20:42,217 - config.database - INFO - Successfully connected to MongoDB: referral_bot
2025-07-20 10:20:43,870 - config.database - INFO - Database collections and indexes initialized successfully
2025-07-20 10:20:43,871 - __main__ - INFO - Initializing Telegram bot...
2025-07-20 10:20:44,410 - __main__ - INFO - All handlers registered successfully
2025-07-20 10:20:45,304 - services.admin_service - INFO - Initialized admin settings for 4 admins
2025-07-20 10:20:45,345 - __main__ - INFO - Bot info updated: @demoxxyyzzbot (INST DEMO)
2025-07-20 10:20:45,345 - __main__ - INFO - Admin settings and level rewards system initialized
2025-07-20 10:20:45,346 - __main__ - INFO - Bot initialization completed successfully
2025-07-20 10:20:45,346 - __main__ - INFO - Starting bot polling...
2025-07-20 10:20:45,546 - apscheduler.scheduler - INFO - Scheduler started
2025-07-20 10:20:45,744 - __main__ - INFO - Bot is now running and polling for updates...
2025-07-20 10:21:13,911 - handlers.admin_handlers - ERROR - Error in handle_overall_statistics: Message is not modified: specified new message content and reply markup are exactly the same as a current content and reply markup of the message
2025-07-20 10:23:58,346 - __main__ - INFO - Received signal 2, initiating shutdown...
2025-07-20 10:23:58,346 - __main__ - INFO - Shutting down bot...
2025-07-20 10:23:59,000 - apscheduler.scheduler - INFO - Scheduler has been shut down
2025-07-20 10:23:59,046 - config.database - INFO - Disconnected from MongoDB
2025-07-20 10:23:59,047 - __main__ - INFO - Bot shutdown completed
2025-07-20 10:24:04,819 - __main__ - INFO - Connecting to MongoDB...
2025-07-20 10:24:18,524 - config.database - INFO - Successfully connected to MongoDB: referral_bot
2025-07-20 10:24:20,736 - config.database - INFO - Database collections and indexes initialized successfully
2025-07-20 10:24:20,736 - __main__ - INFO - Initializing Telegram bot...
2025-07-20 10:24:21,726 - __main__ - INFO - All handlers registered successfully
2025-07-20 10:24:22,813 - services.admin_service - INFO - Initialized admin settings for 4 admins
2025-07-20 10:24:22,857 - __main__ - INFO - Bot info updated: @demoxxyyzzbot (INST DEMO)
2025-07-20 10:24:22,858 - __main__ - INFO - Admin settings and level rewards system initialized
2025-07-20 10:24:22,858 - __main__ - INFO - Bot initialization completed successfully
2025-07-20 10:24:22,858 - __main__ - INFO - Starting bot polling...
2025-07-20 10:24:23,059 - apscheduler.scheduler - INFO - Scheduler started
2025-07-20 10:24:23,258 - __main__ - INFO - Bot is now running and polling for updates...
2025-07-20 10:24:24,268 - handlers.callback_handlers - ERROR - Error in callback handler for data 'bot_statistics': Query is too old and response timeout expired or query id is invalid
2025-07-20 10:24:24,638 - __main__ - ERROR - Error in callback handler: Query is too old and response timeout expired or query id is invalid
2025-07-20 10:25:36,220 - handlers.admin_handlers - ERROR - Error in handle_top_inviters: Message is not modified: specified new message content and reply markup are exactly the same as a current content and reply markup of the message
2025-07-20 10:29:45,179 - __main__ - INFO - Received signal 2, initiating shutdown...
2025-07-20 10:29:45,179 - __main__ - INFO - Shutting down bot...
2025-07-20 10:29:45,767 - apscheduler.scheduler - INFO - Scheduler has been shut down
2025-07-20 10:29:45,814 - config.database - INFO - Disconnected from MongoDB
2025-07-20 10:29:45,814 - __main__ - INFO - Bot shutdown completed
2025-07-20 10:29:51,573 - __main__ - INFO - Connecting to MongoDB...
2025-07-20 10:30:04,911 - config.database - INFO - Successfully connected to MongoDB: referral_bot
2025-07-20 10:30:06,734 - config.database - INFO - Database collections and indexes initialized successfully
2025-07-20 10:30:06,734 - __main__ - INFO - Initializing Telegram bot...
2025-07-20 10:30:07,157 - __main__ - INFO - All handlers registered successfully
2025-07-20 10:30:08,405 - services.admin_service - INFO - Initialized admin settings for 4 admins
2025-07-20 10:30:08,446 - __main__ - INFO - Bot info updated: @demoxxyyzzbot (INST DEMO)
2025-07-20 10:30:08,447 - __main__ - INFO - Admin settings and level rewards system initialized
2025-07-20 10:30:08,447 - __main__ - INFO - Bot initialization completed successfully
2025-07-20 10:30:08,447 - __main__ - INFO - Starting bot polling...
2025-07-20 10:30:08,870 - apscheduler.scheduler - INFO - Scheduler started
2025-07-20 10:30:09,069 - __main__ - INFO - Bot is now running and polling for updates...
2025-07-20 10:30:12,185 - handlers.callback_handlers - ERROR - Error in callback handler for data 'top_inviters': Query is too old and response timeout expired or query id is invalid
2025-07-20 10:30:12,544 - __main__ - ERROR - Error in callback handler: Query is too old and response timeout expired or query id is invalid
2025-07-20 10:30:15,169 - handlers.admin_handlers - ERROR - Error in handle_bot_statistics: Message is not modified: specified new message content and reply markup are exactly the same as a current content and reply markup of the message
2025-07-20 10:40:27,906 - __main__ - INFO - Received signal 2, initiating shutdown...
2025-07-20 10:40:27,906 - __main__ - INFO - Shutting down bot...
2025-07-20 10:40:31,696 - apscheduler.scheduler - INFO - Scheduler has been shut down
2025-07-20 10:40:31,740 - config.database - INFO - Disconnected from MongoDB
2025-07-20 10:40:31,741 - __main__ - INFO - Bot shutdown completed
2025-07-20 10:40:42,009 - __main__ - INFO - Connecting to MongoDB...
2025-07-20 10:40:55,771 - config.database - INFO - Successfully connected to MongoDB: referral_bot
2025-07-20 10:40:57,457 - config.database - INFO - Database collections and indexes initialized successfully
2025-07-20 10:40:57,457 - __main__ - INFO - Initializing Telegram bot...
2025-07-20 10:40:57,918 - __main__ - INFO - All handlers registered successfully
2025-07-20 10:40:58,883 - services.admin_service - INFO - Initialized admin settings for 4 admins
2025-07-20 10:40:58,929 - __main__ - INFO - Bot info updated: @demoxxyyzzbot (INST DEMO)
2025-07-20 10:40:58,929 - __main__ - INFO - Admin settings and level rewards system initialized
2025-07-20 10:40:58,931 - __main__ - INFO - Bot initialization completed successfully
2025-07-20 10:40:58,931 - __main__ - INFO - Starting bot polling...
2025-07-20 10:40:59,145 - apscheduler.scheduler - INFO - Scheduler started
2025-07-20 10:40:59,360 - __main__ - INFO - Bot is now running and polling for updates...
2025-07-20 10:50:02,945 - handlers.session_handlers - INFO - === SESSION MESSAGE HANDLER for user **********, step: set_main_channel ===
2025-07-20 10:50:02,945 - handlers.session_handlers - INFO - Session data: {'_id': ObjectId('687c7bea7104627ee20348af'), 'user_id': **********, 'created_at': 1752988650, 'data': {}, 'step': 'set_main_channel', 'updated_at': 1752988650}
2025-07-20 10:50:02,945 - handlers.session_handlers - INFO - Message has text: True
2025-07-20 10:50:02,945 - handlers.session_handlers - INFO - Message has photo: False
2025-07-20 10:50:02,945 - handlers.session_handlers - INFO - Message has video: False
2025-07-20 10:50:02,945 - handlers.session_handlers - INFO - Message has document: False
2025-07-20 10:50:02,945 - handlers.session_handlers - INFO - Message text: '@InstantoPay'
2025-07-20 10:50:54,177 - handlers.session_handlers - INFO - === SESSION MESSAGE HANDLER for user **********, step: add_forcesub_channel ===
2025-07-20 10:50:54,177 - handlers.session_handlers - INFO - Session data: {'_id': ObjectId('687c7c8d7104627ee20348b0'), 'user_id': **********, 'created_at': 1752988813, 'data': {}, 'step': 'add_forcesub_channel', 'updated_at': 1752988813}
2025-07-20 10:50:54,177 - handlers.session_handlers - INFO - Message has text: True
2025-07-20 10:50:54,177 - handlers.session_handlers - INFO - Message has photo: False
2025-07-20 10:50:54,177 - handlers.session_handlers - INFO - Message has video: False
2025-07-20 10:50:54,178 - handlers.session_handlers - INFO - Message has document: False
2025-07-20 10:50:54,178 - handlers.session_handlers - INFO - Message text: '🚧 InstantoPay Update in Progress

We’re currently upgrading the bot to improve performance and user safety.
Please do not use the InstantoPay bot right now.
Using it during this update may lead to errors or loss of your wallet balance.

Kindly wait until the official update is completed.
We’ll notify you once the bot is fully ready to use again.

Thank you for your patience and support.
- Team Instanto'
2025-07-20 10:51:10,530 - handlers.session_handlers - INFO - === SESSION MESSAGE HANDLER for user **********, step: add_forcesub_channel ===
2025-07-20 10:51:10,531 - handlers.session_handlers - INFO - Session data: {'_id': ObjectId('687c7cbb7104627ee20348b1'), 'user_id': **********, 'created_at': 1752988859, 'data': {}, 'step': 'add_forcesub_channel', 'updated_at': 1752988859}
2025-07-20 10:51:10,531 - handlers.session_handlers - INFO - Message has text: True
2025-07-20 10:51:10,532 - handlers.session_handlers - INFO - Message has photo: False
2025-07-20 10:51:10,533 - handlers.session_handlers - INFO - Message has video: False
2025-07-20 10:51:10,533 - handlers.session_handlers - INFO - Message has document: False
2025-07-20 10:51:10,533 - handlers.session_handlers - INFO - Message text: '🚧 InstantoPay Update in Progress

We’re currently upgrading the bot to improve performance and user safety.
Please do not use the InstantoPay bot right now.
Using it during this update may lead to errors or loss of your wallet balance.

Kindly wait until the official update is completed.
We’ll notify you once the bot is fully ready to use again.

Thank you for your patience and support.
- Team Instanto'
2025-07-20 10:51:30,013 - handlers.session_handlers - INFO - === SESSION MESSAGE HANDLER for user **********, step: add_forcesub_channel ===
2025-07-20 10:51:30,013 - handlers.session_handlers - INFO - Session data: {'_id': ObjectId('687c7ccc7104627ee20348b2'), 'user_id': **********, 'created_at': 1752988876, 'data': {}, 'step': 'add_forcesub_channel', 'updated_at': 1752988876}
2025-07-20 10:51:30,015 - handlers.session_handlers - INFO - Message has text: True
2025-07-20 10:51:30,015 - handlers.session_handlers - INFO - Message has photo: False
2025-07-20 10:51:30,015 - handlers.session_handlers - INFO - Message has video: False
2025-07-20 10:51:30,015 - handlers.session_handlers - INFO - Message has document: False
2025-07-20 10:51:30,015 - handlers.session_handlers - INFO - Message text: '🚧 InstantoPay Update in Progress

We’re currently upgrading the bot to improve performance and user safety.
Please do not use the InstantoPay bot right now.
Using it during this update may lead to errors or loss of your wallet balance.

Kindly wait until the official update is completed.
We’ll notify you once the bot is fully ready to use again.

Thank you for your patience and support.
- Team Instanto'
2025-07-20 10:51:41,731 - handlers.session_handlers - INFO - === SESSION MESSAGE HANDLER for user **********, step: add_forcesub_channel ===
2025-07-20 10:51:41,732 - handlers.session_handlers - INFO - Session data: {'_id': ObjectId('687c7cde7104627ee20348b3'), 'user_id': **********, 'created_at': 1752988894, 'data': {}, 'step': 'add_forcesub_channel', 'updated_at': 1752988894}
2025-07-20 10:51:41,732 - handlers.session_handlers - INFO - Message has text: True
2025-07-20 10:51:41,732 - handlers.session_handlers - INFO - Message has photo: False
2025-07-20 10:51:41,732 - handlers.session_handlers - INFO - Message has video: False
2025-07-20 10:51:41,732 - handlers.session_handlers - INFO - Message has document: False
2025-07-20 10:51:41,733 - handlers.session_handlers - INFO - Message text: '🚧 InstantoPay Update in Progress

We’re currently upgrading the bot to improve performance and user safety.
Please do not use the InstantoPay bot right now.
Using it during this update may lead to errors or loss of your wallet balance.

Kindly wait until the official update is completed.
We’ll notify you once the bot is fully ready to use again.

Thank you for your patience and support.
- Team Instanto'
2025-07-20 10:52:24,799 - __main__ - INFO - Received signal 2, initiating shutdown...
2025-07-20 10:52:24,800 - __main__ - INFO - Shutting down bot...
2025-07-20 10:52:25,386 - apscheduler.scheduler - INFO - Scheduler has been shut down
2025-07-20 10:52:25,430 - config.database - INFO - Disconnected from MongoDB
2025-07-20 10:52:25,431 - __main__ - INFO - Bot shutdown completed
2025-07-20 10:52:32,193 - __main__ - INFO - Connecting to MongoDB...
2025-07-20 10:52:45,288 - config.database - INFO - Successfully connected to MongoDB: referral_bot
2025-07-20 10:52:46,955 - config.database - INFO - Database collections and indexes initialized successfully
2025-07-20 10:52:46,956 - __main__ - INFO - Initializing Telegram bot...
2025-07-20 10:52:47,626 - __main__ - INFO - All handlers registered successfully
2025-07-20 10:52:48,492 - services.admin_service - INFO - Initialized admin settings for 4 admins
2025-07-20 10:52:48,535 - __main__ - INFO - Bot info updated: @demoxxyyzzbot (INST DEMO)
2025-07-20 10:52:48,535 - __main__ - INFO - Admin settings and level rewards system initialized
2025-07-20 10:52:48,536 - __main__ - INFO - Bot initialization completed successfully
2025-07-20 10:52:48,536 - __main__ - INFO - Starting bot polling...
2025-07-20 10:52:48,738 - apscheduler.scheduler - INFO - Scheduler started
2025-07-20 10:52:48,939 - __main__ - INFO - Bot is now running and polling for updates...
2025-07-20 10:53:07,140 - handlers.session_handlers - INFO - === SESSION MESSAGE HANDLER for user **********, step: set_new_user_bonus ===
2025-07-20 10:53:07,140 - handlers.session_handlers - INFO - Session data: {'_id': ObjectId('687c7d2c7104627ee20348b4'), 'user_id': **********, 'created_at': 1752988972, 'data': {}, 'step': 'set_new_user_bonus', 'updated_at': 1752988972}
2025-07-20 10:53:07,140 - handlers.session_handlers - INFO - Message has text: True
2025-07-20 10:53:07,141 - handlers.session_handlers - INFO - Message has photo: False
2025-07-20 10:53:07,141 - handlers.session_handlers - INFO - Message has video: False
2025-07-20 10:53:07,141 - handlers.session_handlers - INFO - Message has document: False
2025-07-20 10:53:07,141 - handlers.session_handlers - INFO - Message text: '49-59'
2025-07-20 10:53:07,141 - services.admin_service - INFO - Updating admin setting: field=joining_bonus_amount_range, value=49-59, admin_id=**********
2025-07-20 10:53:07,141 - services.admin_service - INFO - Got admin_settings collection
2025-07-20 10:53:07,141 - services.admin_service - INFO - Ensuring admin settings exist...
2025-07-20 10:53:07,184 - services.admin_service - INFO - Admin settings exist: True
2025-07-20 10:53:07,185 - services.admin_service - INFO - Executing update query for admin_id=**********
2025-07-20 10:53:07,248 - services.admin_service - INFO - Update result: matched_count=1, modified_count=1
2025-07-20 10:53:07,249 - services.admin_service - INFO - Update success: True
2025-07-20 10:54:00,159 - handlers.admin_handlers - ERROR - Error in handle_manage_withdrawals: Message is not modified: specified new message content and reply markup are exactly the same as a current content and reply markup of the message
2025-07-20 10:54:00,526 - handlers.callback_handlers - ERROR - Error in callback handler for data 'manage_withdrawals': Query is too old and response timeout expired or query id is invalid
2025-07-20 10:54:00,897 - __main__ - ERROR - Error in callback handler: Query is too old and response timeout expired or query id is invalid
2025-07-20 10:54:16,408 - handlers.admin_handlers - ERROR - Error in handle_view_pending_withdrawals: Can't parse entities: unsupported start tag "\" at byte offset 577
2025-07-20 10:54:16,790 - handlers.callback_handlers - ERROR - Error in callback handler for data 'viewPendingWithdrawals': Query is too old and response timeout expired or query id is invalid
2025-07-20 10:54:17,164 - __main__ - ERROR - Error in callback handler: Query is too old and response timeout expired or query id is invalid
2025-07-20 10:55:06,523 - handlers.admin_handlers - ERROR - Error in handle_view_pending_withdrawals: Can't parse entities: unsupported start tag "\" at byte offset 577
2025-07-20 11:08:55,024 - handlers.admin_handlers - ERROR - Error in handle_view_pending_withdrawals: Can't parse entities: unsupported start tag "\" at byte offset 577
2025-07-20 11:09:15,893 - handlers.admin_handlers - ERROR - Error in handle_view_pending_withdrawals: Can't parse entities: unsupported start tag "\" at byte offset 577
2025-07-20 11:10:05,088 - handlers.callback_handlers - ERROR - Error in callback handler for data 'set_tax_percentage': 'AdminHandlers' object has no attribute 'handle_set_tax_type'
2025-07-20 11:10:14,985 - handlers.callback_handlers - ERROR - Error in callback handler for data 'set_tax_percentage': 'AdminHandlers' object has no attribute 'handle_set_tax_type'
2025-07-20 11:10:21,623 - handlers.callback_handlers - ERROR - Error in callback handler for data 'set_tax_none': 'AdminHandlers' object has no attribute 'handle_set_tax_type'
2025-07-20 11:10:26,373 - handlers.callback_handlers - ERROR - Error in callback handler for data 'set_tax_none': 'AdminHandlers' object has no attribute 'handle_set_tax_type'
2025-07-20 11:10:28,280 - handlers.callback_handlers - ERROR - Error in callback handler for data 'set_tax_fixed': 'AdminHandlers' object has no attribute 'handle_set_tax_type'
2025-07-20 11:10:38,598 - handlers.callback_handlers - ERROR - Error in callback handler for data 'set_tax_fixed': 'AdminHandlers' object has no attribute 'handle_set_tax_type'
2025-07-20 11:10:50,536 - services.admin_service - INFO - Updating admin setting: field=withdrawal_settings, value={'enabled': False, 'tax_type': 'none', 'tax_amount': 0, 'tax_percentage': 0}, admin_id=**********
2025-07-20 11:10:50,537 - services.admin_service - INFO - Got admin_settings collection
2025-07-20 11:10:50,537 - services.admin_service - INFO - Ensuring admin settings exist...
2025-07-20 11:10:50,579 - services.admin_service - INFO - Admin settings exist: True
2025-07-20 11:10:50,580 - services.admin_service - INFO - Executing update query for admin_id=**********
2025-07-20 11:10:50,637 - services.admin_service - INFO - Update result: matched_count=1, modified_count=1
2025-07-20 11:10:50,637 - services.admin_service - INFO - Update success: True
2025-07-20 11:10:57,392 - services.admin_service - INFO - Updating admin setting: field=withdrawal_settings, value={'enabled': True, 'tax_type': 'none', 'tax_amount': 0, 'tax_percentage': 0}, admin_id=**********
2025-07-20 11:10:57,392 - services.admin_service - INFO - Got admin_settings collection
2025-07-20 11:10:57,393 - services.admin_service - INFO - Ensuring admin settings exist...
2025-07-20 11:10:57,448 - services.admin_service - INFO - Admin settings exist: True
2025-07-20 11:10:57,448 - services.admin_service - INFO - Executing update query for admin_id=**********
2025-07-20 11:10:57,507 - services.admin_service - INFO - Update result: matched_count=1, modified_count=1
2025-07-20 11:10:57,507 - services.admin_service - INFO - Update success: True
2025-07-20 11:11:05,934 - handlers.callback_handlers - ERROR - Error in callback handler for data 'set_tax_percentage': 'AdminHandlers' object has no attribute 'handle_set_tax_type'
2025-07-20 11:11:09,390 - handlers.callback_handlers - ERROR - Error in callback handler for data 'set_tax_percentage': 'AdminHandlers' object has no attribute 'handle_set_tax_type'
2025-07-20 11:12:08,352 - handlers.callback_handlers - ERROR - Error in callback handler for data 'set_tax_percentage': 'AdminHandlers' object has no attribute 'handle_set_tax_type'
2025-07-20 11:12:55,605 - handlers.admin_handlers - ERROR - Error in handle_view_pending_withdrawals: Can't parse entities: unsupported start tag "\" at byte offset 577
2025-07-20 11:14:34,788 - handlers.callback_handlers - ERROR - Error in callback handler for data 'set_tax_percentage': 'AdminHandlers' object has no attribute 'handle_set_tax_type'
2025-07-20 11:15:06,120 - handlers.admin_handlers - ERROR - Error in handle_manage_withdrawals: Message is not modified: specified new message content and reply markup are exactly the same as a current content and reply markup of the message
2025-07-20 11:21:16,475 - __main__ - INFO - Received signal 2, initiating shutdown...
2025-07-20 11:21:16,475 - __main__ - INFO - Shutting down bot...
2025-07-20 11:21:17,131 - apscheduler.scheduler - INFO - Scheduler has been shut down
2025-07-20 11:21:17,178 - config.database - INFO - Disconnected from MongoDB
2025-07-20 11:21:17,178 - __main__ - INFO - Bot shutdown completed
2025-07-20 11:21:18,430 - __main__ - INFO - Connecting to MongoDB...
2025-07-20 11:21:31,194 - config.database - INFO - Successfully connected to MongoDB: referral_bot
2025-07-20 11:21:32,920 - config.database - INFO - Database collections and indexes initialized successfully
2025-07-20 11:21:32,921 - __main__ - INFO - Initializing Telegram bot...
2025-07-20 11:21:33,289 - __main__ - INFO - All handlers registered successfully
2025-07-20 11:21:34,169 - services.admin_service - INFO - Initialized admin settings for 4 admins
2025-07-20 11:21:34,214 - __main__ - INFO - Bot info updated: @demoxxyyzzbot (INST DEMO)
2025-07-20 11:21:34,214 - __main__ - INFO - Admin settings and level rewards system initialized
2025-07-20 11:21:34,214 - __main__ - INFO - Bot initialization completed successfully
2025-07-20 11:21:34,215 - __main__ - INFO - Starting bot polling...
2025-07-20 11:21:34,416 - apscheduler.scheduler - INFO - Scheduler started
2025-07-20 11:21:34,616 - __main__ - INFO - Bot is now running and polling for updates...
2025-07-20 11:23:09,896 - __main__ - INFO - Received signal 2, initiating shutdown...
2025-07-20 11:23:09,897 - __main__ - INFO - Shutting down bot...
2025-07-20 11:23:10,525 - apscheduler.scheduler - INFO - Scheduler has been shut down
2025-07-20 11:23:10,571 - config.database - INFO - Disconnected from MongoDB
2025-07-20 11:23:10,572 - __main__ - INFO - Bot shutdown completed
2025-07-20 11:23:10,920 - __main__ - INFO - Received signal 2, initiating shutdown...
2025-07-20 11:23:28,789 - __main__ - INFO - Connecting to MongoDB...
2025-07-20 11:23:42,454 - config.database - INFO - Successfully connected to MongoDB: referral_bot
2025-07-20 11:23:44,104 - config.database - INFO - Database collections and indexes initialized successfully
2025-07-20 11:23:44,104 - __main__ - INFO - Initializing Telegram bot...
2025-07-20 11:23:44,495 - __main__ - INFO - All handlers registered successfully
2025-07-20 11:23:45,392 - services.admin_service - INFO - Initialized admin settings for 4 admins
2025-07-20 11:23:45,434 - __main__ - INFO - Bot info updated: @demoxxyyzzbot (INST DEMO)
2025-07-20 11:23:45,434 - __main__ - INFO - Admin settings and level rewards system initialized
2025-07-20 11:23:45,434 - __main__ - INFO - Bot initialization completed successfully
2025-07-20 11:23:45,434 - __main__ - INFO - Starting bot polling...
2025-07-20 11:23:45,635 - apscheduler.scheduler - INFO - Scheduler started
2025-07-20 11:23:45,833 - __main__ - INFO - Bot is now running and polling for updates...
2025-07-20 11:23:47,507 - __main__ - INFO - Received signal 2, initiating shutdown...
2025-07-20 11:23:47,509 - __main__ - INFO - Shutting down bot...
2025-07-20 11:23:51,130 - apscheduler.scheduler - INFO - Scheduler has been shut down
2025-07-20 11:23:51,174 - config.database - INFO - Disconnected from MongoDB
2025-07-20 11:23:51,174 - __main__ - INFO - Bot shutdown completed
2025-07-20 11:26:20,502 - __main__ - INFO - Connecting to MongoDB...
2025-07-20 11:26:33,488 - config.database - INFO - Successfully connected to MongoDB: referral_bot
2025-07-20 11:26:35,188 - config.database - INFO - Database collections and indexes initialized successfully
2025-07-20 11:26:35,188 - __main__ - INFO - Initializing Telegram bot...
2025-07-20 11:26:35,649 - __main__ - INFO - All handlers registered successfully
2025-07-20 11:26:36,640 - services.admin_service - INFO - Initialized admin settings for 4 admins
2025-07-20 11:26:36,685 - __main__ - INFO - Bot info updated: @demoxxyyzzbot (INST DEMO)
2025-07-20 11:26:36,685 - __main__ - INFO - Admin settings and level rewards system initialized
2025-07-20 11:26:36,685 - __main__ - INFO - Bot initialization completed successfully
2025-07-20 11:26:36,686 - __main__ - INFO - Starting bot polling...
2025-07-20 11:26:36,875 - apscheduler.scheduler - INFO - Scheduler started
2025-07-20 11:26:37,069 - __main__ - INFO - Bot is now running and polling for updates...
2025-07-20 11:27:16,085 - __main__ - INFO - Received signal 2, initiating shutdown...
2025-07-20 11:27:16,085 - __main__ - INFO - Shutting down bot...
2025-07-20 11:27:16,688 - apscheduler.scheduler - INFO - Scheduler has been shut down
2025-07-20 11:27:16,731 - config.database - INFO - Disconnected from MongoDB
2025-07-20 11:27:16,731 - __main__ - INFO - Bot shutdown completed
2025-07-20 11:27:21,947 - __main__ - INFO - Connecting to MongoDB...
2025-07-20 11:27:34,694 - config.database - INFO - Successfully connected to MongoDB: referral_bot
2025-07-20 11:27:36,416 - config.database - INFO - Database collections and indexes initialized successfully
2025-07-20 11:27:36,417 - __main__ - INFO - Initializing Telegram bot...
2025-07-20 11:27:36,822 - __main__ - INFO - All handlers registered successfully
2025-07-20 11:27:37,700 - services.admin_service - INFO - Initialized admin settings for 4 admins
2025-07-20 11:27:37,744 - __main__ - INFO - Bot info updated: @demoxxyyzzbot (INST DEMO)
2025-07-20 11:27:37,744 - __main__ - INFO - Admin settings and level rewards system initialized
2025-07-20 11:27:37,744 - __main__ - INFO - Bot initialization completed successfully
2025-07-20 11:27:37,744 - __main__ - INFO - Starting bot polling...
2025-07-20 11:27:37,944 - apscheduler.scheduler - INFO - Scheduler started
2025-07-20 11:27:38,146 - __main__ - INFO - Bot is now running and polling for updates...
2025-07-20 11:29:30,187 - handlers.admin_handlers - INFO - Loaded 10 pending withdrawals in 0.464s
2025-07-20 11:29:30,564 - handlers.admin_handlers - INFO - handle_view_pending_withdrawals completed in 0.841s
2025-07-20 11:29:49,713 - handlers.admin_handlers - INFO - Loaded 10 pending withdrawals for approval in 0.585s
2025-07-20 11:29:57,411 - utils.helpers - ERROR - Failed to send message to 6060202006: Chat not found
2025-07-20 11:29:57,412 - services.withdrawal_service - INFO - Withdrawal approved: User 6060202006, Amount ₹400
2025-07-20 11:30:27,708 - handlers.admin_handlers - INFO - Loaded 9 pending withdrawals in 0.389s
2025-07-20 11:30:28,101 - handlers.admin_handlers - INFO - handle_view_pending_withdrawals completed in 0.782s
2025-07-20 11:30:32,488 - handlers.admin_handlers - INFO - Loaded 9 pending withdrawals for approval in 0.397s
2025-07-20 11:30:39,149 - handlers.admin_handlers - INFO - Loaded 9 pending withdrawals for approval in 0.418s
2025-07-20 11:34:57,291 - handlers.session_handlers - INFO - === SESSION MESSAGE HANDLER for user **********, step: set_percent_tax ===
2025-07-20 11:34:57,291 - handlers.session_handlers - INFO - Session data: {'_id': ObjectId('687c80b77104627ee20348b5'), 'user_id': **********, 'created_at': 1752991495, 'data': {}, 'step': 'set_percent_tax', 'updated_at': 1752991495}
2025-07-20 11:34:57,291 - handlers.session_handlers - INFO - Message has text: True
2025-07-20 11:34:57,291 - handlers.session_handlers - INFO - Message has photo: False
2025-07-20 11:34:57,293 - handlers.session_handlers - INFO - Message has video: False
2025-07-20 11:34:57,293 - handlers.session_handlers - INFO - Message has document: False
2025-07-20 11:34:57,293 - handlers.session_handlers - INFO - Message text: '10'
2025-07-20 11:34:57,325 - services.admin_service - INFO - Updating admin setting: field=withdrawal_settings, value={'enabled': True, 'tax_type': 'percentage', 'tax_amount': 0, 'tax_percentage': 10.0}, admin_id=**********
2025-07-20 11:34:57,327 - services.admin_service - INFO - Got admin_settings collection
2025-07-20 11:34:57,327 - services.admin_service - INFO - Ensuring admin settings exist...
2025-07-20 11:34:57,360 - services.admin_service - INFO - Admin settings exist: True
2025-07-20 11:34:57,360 - services.admin_service - INFO - Executing update query for admin_id=**********
2025-07-20 11:34:57,396 - services.admin_service - INFO - Update result: matched_count=1, modified_count=1
2025-07-20 11:34:57,396 - services.admin_service - INFO - Update success: True
2025-07-20 11:35:31,178 - handlers.admin_handlers - INFO - Loaded 9 pending withdrawals in 0.442s
2025-07-20 11:35:31,576 - handlers.admin_handlers - INFO - handle_view_pending_withdrawals completed in 0.840s
2025-07-20 11:35:57,636 - handlers.admin_handlers - INFO - Loaded 9 pending withdrawals in 0.434s
2025-07-20 11:35:58,077 - handlers.admin_handlers - INFO - handle_view_pending_withdrawals completed in 0.875s
2025-07-20 11:36:33,119 - handlers.admin_handlers - INFO - Loaded 9 pending withdrawals in 0.451s
2025-07-20 11:36:33,509 - handlers.admin_handlers - INFO - handle_view_pending_withdrawals completed in 0.840s
2025-07-20 11:36:50,632 - utils.helpers - ERROR - Failed to send message to **********: Chat not found
2025-07-20 11:36:50,634 - services.withdrawal_service - INFO - Withdrawal rejected: User **********, Amount ₹100
2025-07-20 11:37:41,475 - handlers.callback_handlers - INFO - Message contains media, sending new text message instead of editing
2025-07-20 11:57:00,908 - __main__ - INFO - Received signal 2, initiating shutdown...
2025-07-20 11:57:00,908 - __main__ - INFO - Shutting down bot...
2025-07-20 11:57:01,527 - apscheduler.scheduler - INFO - Scheduler has been shut down
2025-07-20 11:57:01,569 - config.database - INFO - Disconnected from MongoDB
2025-07-20 11:57:01,570 - __main__ - INFO - Bot shutdown completed
2025-07-20 11:57:06,761 - __main__ - INFO - Connecting to MongoDB...
2025-07-20 11:57:20,394 - config.database - INFO - Successfully connected to MongoDB: referral_bot
2025-07-20 11:57:22,042 - config.database - INFO - Database collections and indexes initialized successfully
2025-07-20 11:57:22,042 - __main__ - INFO - Initializing Telegram bot...
2025-07-20 11:57:22,436 - __main__ - INFO - All handlers registered successfully
2025-07-20 11:57:23,301 - services.admin_service - INFO - Initialized admin settings for 4 admins
2025-07-20 11:57:23,344 - __main__ - INFO - Bot info updated: @demoxxyyzzbot (INST DEMO)
2025-07-20 11:57:23,345 - __main__ - INFO - Admin settings and level rewards system initialized
2025-07-20 11:57:23,345 - __main__ - INFO - Bot initialization completed successfully
2025-07-20 11:57:23,345 - __main__ - INFO - Starting bot polling...
2025-07-20 11:57:23,544 - apscheduler.scheduler - INFO - Scheduler started
2025-07-20 11:57:23,745 - __main__ - INFO - Bot is now running and polling for updates...
2025-07-20 15:03:06,777 - __main__ - INFO - Connecting to MongoDB...
2025-07-20 15:03:20,561 - config.database - INFO - Successfully connected to MongoDB: referral_bot
2025-07-20 15:03:22,274 - config.database - INFO - Database collections and indexes initialized successfully
2025-07-20 15:03:22,274 - __main__ - INFO - Initializing Telegram bot...
2025-07-20 15:03:22,693 - __main__ - INFO - All handlers registered successfully
2025-07-20 15:03:23,629 - services.admin_service - INFO - Initialized admin settings for 4 admins
2025-07-20 15:03:23,673 - __main__ - INFO - Bot info updated: @demoxxyyzzbot (INST DEMO)
2025-07-20 15:03:23,674 - __main__ - INFO - Admin settings and level rewards system initialized
2025-07-20 15:03:23,674 - __main__ - INFO - Bot initialization completed successfully
2025-07-20 15:03:23,674 - __main__ - INFO - Starting bot polling...
2025-07-20 15:03:23,873 - apscheduler.scheduler - INFO - Scheduler started
2025-07-20 15:03:24,067 - __main__ - INFO - Bot is now running and polling for updates...
2025-07-20 15:07:30,723 - __main__ - INFO - Connecting to MongoDB...
2025-07-20 15:07:43,590 - config.database - INFO - Successfully connected to MongoDB: referral_bot
2025-07-20 15:07:45,317 - config.database - INFO - Database collections and indexes initialized successfully
2025-07-20 15:07:45,319 - __main__ - INFO - Initializing Telegram bot...
2025-07-20 15:07:45,708 - __main__ - INFO - All handlers registered successfully
2025-07-20 15:07:46,599 - services.admin_service - INFO - Initialized admin settings for 4 admins
2025-07-20 15:07:46,646 - __main__ - INFO - Bot info updated: @demoxxyyzzbot (INST DEMO)
2025-07-20 15:07:46,646 - __main__ - INFO - Admin settings and level rewards system initialized
2025-07-20 15:07:46,647 - __main__ - INFO - Bot initialization completed successfully
2025-07-20 15:07:46,647 - __main__ - INFO - Starting bot polling...
2025-07-20 15:07:46,843 - apscheduler.scheduler - INFO - Scheduler started
2025-07-20 15:07:47,049 - __main__ - INFO - Bot is now running and polling for updates...
2025-07-20 15:07:47,623 - telegram.ext.Updater - ERROR - Error while getting Updates: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-20 15:07:47,624 - telegram.ext.Updater - ERROR - Exception happened while polling for updates.
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_updater.py", line 715, in _network_loop_retry
    if not await action_cb():
           ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_updater.py", line 384, in polling_action_cb
    raise exc
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_updater.py", line 373, in polling_action_cb
    updates = await self.bot.get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_extbot.py", line 618, in get_updates
    updates = await super().get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 542, in decorator
    result = await func(self, *args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 4164, in get_updates
    await self._post(
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 630, in _post
    return await self._do_post(
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_extbot.py", line 347, in _do_post
    return await super()._do_post(
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 658, in _do_post
    return await request.post(
           ^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\request\_baserequest.py", line 200, in post
    result = await self._request_wrapper(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\request\_baserequest.py", line 381, in _request_wrapper
    raise Conflict(message)
telegram.error.Conflict: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-20 15:07:52,328 - telegram.ext.Updater - ERROR - Error while getting Updates: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-20 15:07:52,328 - telegram.ext.Updater - ERROR - Exception happened while polling for updates.
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_updater.py", line 715, in _network_loop_retry
    if not await action_cb():
           ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_updater.py", line 384, in polling_action_cb
    raise exc
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_updater.py", line 373, in polling_action_cb
    updates = await self.bot.get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_extbot.py", line 618, in get_updates
    updates = await super().get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 542, in decorator
    result = await func(self, *args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 4164, in get_updates
    await self._post(
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 630, in _post
    return await self._do_post(
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_extbot.py", line 347, in _do_post
    return await super()._do_post(
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 658, in _do_post
    return await request.post(
           ^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\request\_baserequest.py", line 200, in post
    result = await self._request_wrapper(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\request\_baserequest.py", line 381, in _request_wrapper
    raise Conflict(message)
telegram.error.Conflict: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-20 15:07:54,022 - telegram.ext.Updater - ERROR - Error while getting Updates: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-20 15:07:54,023 - telegram.ext.Updater - ERROR - Exception happened while polling for updates.
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_updater.py", line 715, in _network_loop_retry
    if not await action_cb():
           ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_updater.py", line 384, in polling_action_cb
    raise exc
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_updater.py", line 373, in polling_action_cb
    updates = await self.bot.get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_extbot.py", line 618, in get_updates
    updates = await super().get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 542, in decorator
    result = await func(self, *args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 4164, in get_updates
    await self._post(
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 630, in _post
    return await self._do_post(
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_extbot.py", line 347, in _do_post
    return await super()._do_post(
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 658, in _do_post
    return await request.post(
           ^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\request\_baserequest.py", line 200, in post
    result = await self._request_wrapper(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\request\_baserequest.py", line 381, in _request_wrapper
    raise Conflict(message)
telegram.error.Conflict: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-20 15:07:59,478 - telegram.ext.Updater - ERROR - Error while getting Updates: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-20 15:07:59,480 - telegram.ext.Updater - ERROR - Exception happened while polling for updates.
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_updater.py", line 715, in _network_loop_retry
    if not await action_cb():
           ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_updater.py", line 384, in polling_action_cb
    raise exc
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_updater.py", line 373, in polling_action_cb
    updates = await self.bot.get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_extbot.py", line 618, in get_updates
    updates = await super().get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 542, in decorator
    result = await func(self, *args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 4164, in get_updates
    await self._post(
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 630, in _post
    return await self._do_post(
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_extbot.py", line 347, in _do_post
    return await super()._do_post(
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 658, in _do_post
    return await request.post(
           ^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\request\_baserequest.py", line 200, in post
    result = await self._request_wrapper(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\request\_baserequest.py", line 381, in _request_wrapper
    raise Conflict(message)
telegram.error.Conflict: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-20 15:08:01,931 - telegram.ext.Updater - ERROR - Error while getting Updates: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-20 15:08:01,932 - telegram.ext.Updater - ERROR - Exception happened while polling for updates.
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_updater.py", line 715, in _network_loop_retry
    if not await action_cb():
           ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_updater.py", line 384, in polling_action_cb
    raise exc
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_updater.py", line 373, in polling_action_cb
    updates = await self.bot.get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_extbot.py", line 618, in get_updates
    updates = await super().get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 542, in decorator
    result = await func(self, *args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 4164, in get_updates
    await self._post(
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 630, in _post
    return await self._do_post(
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_extbot.py", line 347, in _do_post
    return await super()._do_post(
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 658, in _do_post
    return await request.post(
           ^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\request\_baserequest.py", line 200, in post
    result = await self._request_wrapper(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\request\_baserequest.py", line 381, in _request_wrapper
    raise Conflict(message)
telegram.error.Conflict: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-20 15:08:05,507 - telegram.ext.Updater - ERROR - Error while getting Updates: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-20 15:08:05,508 - telegram.ext.Updater - ERROR - Exception happened while polling for updates.
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_updater.py", line 715, in _network_loop_retry
    if not await action_cb():
           ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_updater.py", line 384, in polling_action_cb
    raise exc
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_updater.py", line 373, in polling_action_cb
    updates = await self.bot.get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_extbot.py", line 618, in get_updates
    updates = await super().get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 542, in decorator
    result = await func(self, *args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 4164, in get_updates
    await self._post(
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 630, in _post
    return await self._do_post(
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_extbot.py", line 347, in _do_post
    return await super()._do_post(
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 658, in _do_post
    return await request.post(
           ^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\request\_baserequest.py", line 200, in post
    result = await self._request_wrapper(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\request\_baserequest.py", line 381, in _request_wrapper
    raise Conflict(message)
telegram.error.Conflict: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-20 15:08:09,081 - telegram.ext.Updater - ERROR - Error while getting Updates: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-20 15:08:09,081 - telegram.ext.Updater - ERROR - Exception happened while polling for updates.
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_updater.py", line 715, in _network_loop_retry
    if not await action_cb():
           ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_updater.py", line 384, in polling_action_cb
    raise exc
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_updater.py", line 373, in polling_action_cb
    updates = await self.bot.get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_extbot.py", line 618, in get_updates
    updates = await super().get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 542, in decorator
    result = await func(self, *args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 4164, in get_updates
    await self._post(
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 630, in _post
    return await self._do_post(
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_extbot.py", line 347, in _do_post
    return await super()._do_post(
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 658, in _do_post
    return await request.post(
           ^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\request\_baserequest.py", line 200, in post
    result = await self._request_wrapper(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\request\_baserequest.py", line 381, in _request_wrapper
    raise Conflict(message)
telegram.error.Conflict: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-20 15:08:14,794 - telegram.ext.Updater - ERROR - Error while getting Updates: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-20 15:08:14,794 - telegram.ext.Updater - ERROR - Exception happened while polling for updates.
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_updater.py", line 715, in _network_loop_retry
    if not await action_cb():
           ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_updater.py", line 384, in polling_action_cb
    raise exc
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_updater.py", line 373, in polling_action_cb
    updates = await self.bot.get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_extbot.py", line 618, in get_updates
    updates = await super().get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 542, in decorator
    result = await func(self, *args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 4164, in get_updates
    await self._post(
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 630, in _post
    return await self._do_post(
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_extbot.py", line 347, in _do_post
    return await super()._do_post(
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 658, in _do_post
    return await request.post(
           ^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\request\_baserequest.py", line 200, in post
    result = await self._request_wrapper(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\request\_baserequest.py", line 381, in _request_wrapper
    raise Conflict(message)
telegram.error.Conflict: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-20 15:08:20,460 - telegram.ext.Updater - ERROR - Error while getting Updates: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-20 15:08:20,460 - telegram.ext.Updater - ERROR - Exception happened while polling for updates.
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_updater.py", line 715, in _network_loop_retry
    if not await action_cb():
           ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_updater.py", line 384, in polling_action_cb
    raise exc
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_updater.py", line 373, in polling_action_cb
    updates = await self.bot.get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_extbot.py", line 618, in get_updates
    updates = await super().get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 542, in decorator
    result = await func(self, *args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 4164, in get_updates
    await self._post(
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 630, in _post
    return await self._do_post(
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_extbot.py", line 347, in _do_post
    return await super()._do_post(
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 658, in _do_post
    return await request.post(
           ^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\request\_baserequest.py", line 200, in post
    result = await self._request_wrapper(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\request\_baserequest.py", line 381, in _request_wrapper
    raise Conflict(message)
telegram.error.Conflict: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-20 15:08:28,639 - telegram.ext.Updater - ERROR - Error while getting Updates: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-20 15:08:28,640 - telegram.ext.Updater - ERROR - Exception happened while polling for updates.
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_updater.py", line 715, in _network_loop_retry
    if not await action_cb():
           ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_updater.py", line 384, in polling_action_cb
    raise exc
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_updater.py", line 373, in polling_action_cb
    updates = await self.bot.get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_extbot.py", line 618, in get_updates
    updates = await super().get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 542, in decorator
    result = await func(self, *args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 4164, in get_updates
    await self._post(
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 630, in _post
    return await self._do_post(
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_extbot.py", line 347, in _do_post
    return await super()._do_post(
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 658, in _do_post
    return await request.post(
           ^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\request\_baserequest.py", line 200, in post
    result = await self._request_wrapper(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\request\_baserequest.py", line 381, in _request_wrapper
    raise Conflict(message)
telegram.error.Conflict: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-20 15:08:36,814 - telegram.ext.Updater - ERROR - Error while getting Updates: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-20 15:08:36,814 - telegram.ext.Updater - ERROR - Exception happened while polling for updates.
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_updater.py", line 715, in _network_loop_retry
    if not await action_cb():
           ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_updater.py", line 384, in polling_action_cb
    raise exc
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_updater.py", line 373, in polling_action_cb
    updates = await self.bot.get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_extbot.py", line 618, in get_updates
    updates = await super().get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 542, in decorator
    result = await func(self, *args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 4164, in get_updates
    await self._post(
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 630, in _post
    return await self._do_post(
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_extbot.py", line 347, in _do_post
    return await super()._do_post(
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 658, in _do_post
    return await request.post(
           ^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\request\_baserequest.py", line 200, in post
    result = await self._request_wrapper(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\request\_baserequest.py", line 381, in _request_wrapper
    raise Conflict(message)
telegram.error.Conflict: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-20 15:08:48,820 - telegram.ext.Updater - ERROR - Error while getting Updates: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-20 15:08:48,822 - telegram.ext.Updater - ERROR - Exception happened while polling for updates.
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_updater.py", line 715, in _network_loop_retry
    if not await action_cb():
           ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_updater.py", line 384, in polling_action_cb
    raise exc
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_updater.py", line 373, in polling_action_cb
    updates = await self.bot.get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_extbot.py", line 618, in get_updates
    updates = await super().get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 542, in decorator
    result = await func(self, *args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 4164, in get_updates
    await self._post(
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 630, in _post
    return await self._do_post(
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_extbot.py", line 347, in _do_post
    return await super()._do_post(
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 658, in _do_post
    return await request.post(
           ^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\request\_baserequest.py", line 200, in post
    result = await self._request_wrapper(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\request\_baserequest.py", line 381, in _request_wrapper
    raise Conflict(message)
telegram.error.Conflict: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-20 15:08:53,530 - telegram.ext.Updater - ERROR - Error while getting Updates: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-20 15:08:53,530 - telegram.ext.Updater - ERROR - Exception happened while polling for updates.
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_updater.py", line 715, in _network_loop_retry
    if not await action_cb():
           ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_updater.py", line 384, in polling_action_cb
    raise exc
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_updater.py", line 373, in polling_action_cb
    updates = await self.bot.get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_extbot.py", line 618, in get_updates
    updates = await super().get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 542, in decorator
    result = await func(self, *args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 4164, in get_updates
    await self._post(
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 630, in _post
    return await self._do_post(
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_extbot.py", line 347, in _do_post
    return await super()._do_post(
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 658, in _do_post
    return await request.post(
           ^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\request\_baserequest.py", line 200, in post
    result = await self._request_wrapper(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\request\_baserequest.py", line 381, in _request_wrapper
    raise Conflict(message)
telegram.error.Conflict: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-20 15:09:11,205 - telegram.ext.Updater - ERROR - Error while getting Updates: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-20 15:09:11,205 - telegram.ext.Updater - ERROR - Exception happened while polling for updates.
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_updater.py", line 715, in _network_loop_retry
    if not await action_cb():
           ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_updater.py", line 384, in polling_action_cb
    raise exc
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_updater.py", line 373, in polling_action_cb
    updates = await self.bot.get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_extbot.py", line 618, in get_updates
    updates = await super().get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 542, in decorator
    result = await func(self, *args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 4164, in get_updates
    await self._post(
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 630, in _post
    return await self._do_post(
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_extbot.py", line 347, in _do_post
    return await super()._do_post(
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 658, in _do_post
    return await request.post(
           ^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\request\_baserequest.py", line 200, in post
    result = await self._request_wrapper(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\request\_baserequest.py", line 381, in _request_wrapper
    raise Conflict(message)
telegram.error.Conflict: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-20 15:09:15,903 - telegram.ext.Updater - ERROR - Error while getting Updates: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-20 15:09:15,904 - telegram.ext.Updater - ERROR - Exception happened while polling for updates.
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_updater.py", line 715, in _network_loop_retry
    if not await action_cb():
           ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_updater.py", line 384, in polling_action_cb
    raise exc
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_updater.py", line 373, in polling_action_cb
    updates = await self.bot.get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_extbot.py", line 618, in get_updates
    updates = await super().get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 542, in decorator
    result = await func(self, *args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 4164, in get_updates
    await self._post(
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 630, in _post
    return await self._do_post(
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_extbot.py", line 347, in _do_post
    return await super()._do_post(
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 658, in _do_post
    return await request.post(
           ^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\request\_baserequest.py", line 200, in post
    result = await self._request_wrapper(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\request\_baserequest.py", line 381, in _request_wrapper
    raise Conflict(message)
telegram.error.Conflict: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-20 15:09:45,898 - __main__ - INFO - Received signal 2, initiating shutdown...
2025-07-20 15:09:45,898 - __main__ - INFO - Shutting down bot...
2025-07-20 15:09:46,108 - apscheduler.scheduler - INFO - Scheduler has been shut down
2025-07-20 15:09:46,168 - config.database - INFO - Disconnected from MongoDB
2025-07-20 15:09:46,168 - __main__ - INFO - Bot shutdown completed
2025-07-20 15:11:10,590 - __main__ - INFO - Connecting to MongoDB...
2025-07-20 15:11:24,205 - config.database - INFO - Successfully connected to MongoDB: referral_bot
2025-07-20 15:11:25,927 - config.database - INFO - Database collections and indexes initialized successfully
2025-07-20 15:11:25,930 - __main__ - INFO - Initializing Telegram bot...
2025-07-20 15:11:28,708 - __main__ - INFO - All handlers registered successfully
2025-07-20 15:11:29,624 - services.admin_service - INFO - Initialized admin settings for 4 admins
2025-07-20 15:11:29,660 - __main__ - INFO - Bot info updated: @InstantoPayBot (InstantoPay)
2025-07-20 15:11:29,661 - __main__ - INFO - Admin settings and level rewards system initialized
2025-07-20 15:11:29,662 - __main__ - INFO - Bot initialization completed successfully
2025-07-20 15:11:29,663 - __main__ - INFO - Starting bot polling...
2025-07-20 15:11:29,864 - apscheduler.scheduler - INFO - Scheduler started
2025-07-20 15:11:30,061 - __main__ - INFO - Bot is now running and polling for updates...
2025-07-20 15:11:35,062 - utils.helpers - WARNING - Sync is_admin() called from async context for user 6055750878 - consider using is_admin_async()
2025-07-20 15:11:35,072 - services.admin_service - ERROR - Error getting admin settings for **********: Task <Task pending name='Task-8' coro=<_check_admin_in_new_loop() running at C:\Users\<USER>\Desktop\referbot\python\utils\helpers.py:528> cb=[_run_until_complete_cb() at C:\Python312\Lib\asyncio\base_events.py:182]> got Future <Future pending cb=[_chain_future.<locals>._call_check_cancel() at C:\Python312\Lib\asyncio\futures.py:391]> attached to a different loop
2025-07-20 15:11:35,653 - handlers.user_handlers - INFO - Custom referral parameter used: from_channel -> User ID: **********
2025-07-20 15:11:39,784 - handlers.user_handlers - INFO - Custom referral parameter used: from_channel -> User ID: **********
2025-07-20 15:11:43,442 - handlers.user_handlers - INFO - Custom referral parameter used: from_channel -> User ID: **********
2025-07-20 15:11:47,290 - services.user_service - INFO - Created new user: 6669941851 (Rimpi)
2025-07-20 15:11:50,139 - handlers.user_handlers - ERROR - Error sending new user notification: Forbidden: bot is not a member of the channel chat
2025-07-20 15:11:52,961 - handlers.user_handlers - INFO - Notification sent to referrer ********** about new user Rimpi (ID: 6669941851): success
2025-07-20 15:11:56,274 - handlers.user_handlers - INFO - Custom referral parameter used: from_channel -> User ID: **********
2025-07-20 15:11:59,478 - handlers.callback_handlers - ERROR - Error in callback handler for data 'submitTask_task_1752995334.fd08d118': Query is too old and response timeout expired or query id is invalid
2025-07-20 15:11:59,841 - __main__ - ERROR - Error in callback handler: Query is too old and response timeout expired or query id is invalid
2025-07-20 15:12:00,646 - handlers.callback_handlers - ERROR - Error in callback handler for data 'submitTask_task_1752995334.fd08d118': Query is too old and response timeout expired or query id is invalid
2025-07-20 15:12:01,011 - __main__ - ERROR - Error in callback handler: Query is too old and response timeout expired or query id is invalid
2025-07-20 15:12:01,756 - handlers.callback_handlers - ERROR - Error in callback handler for data 'submitTask_task_1752995334.fd08d118': Query is too old and response timeout expired or query id is invalid
2025-07-20 15:12:02,118 - __main__ - ERROR - Error in callback handler: Query is too old and response timeout expired or query id is invalid
2025-07-20 15:12:02,843 - handlers.callback_handlers - ERROR - Error in callback handler for data 'taskRewards': Query is too old and response timeout expired or query id is invalid
2025-07-20 15:12:03,207 - __main__ - ERROR - Error in callback handler: Query is too old and response timeout expired or query id is invalid
2025-07-20 15:12:03,964 - handlers.callback_handlers - ERROR - Error in callback handler for data 'taskRewards': Query is too old and response timeout expired or query id is invalid
2025-07-20 15:12:04,330 - __main__ - ERROR - Error in callback handler: Query is too old and response timeout expired or query id is invalid
2025-07-20 15:12:05,073 - handlers.callback_handlers - ERROR - Error in callback handler for data 'taskRewards': Query is too old and response timeout expired or query id is invalid
2025-07-20 15:12:05,469 - __main__ - ERROR - Error in callback handler: Query is too old and response timeout expired or query id is invalid
2025-07-20 15:12:06,215 - handlers.callback_handlers - ERROR - Error in callback handler for data 'joined': Query is too old and response timeout expired or query id is invalid
2025-07-20 15:12:06,573 - __main__ - ERROR - Error in callback handler: Query is too old and response timeout expired or query id is invalid
2025-07-20 15:12:07,256 - services.user_service - INFO - Created new user: 6441575752 (Islam)
2025-07-20 15:12:09,632 - handlers.user_handlers - ERROR - Error sending new user notification: Forbidden: bot is not a member of the channel chat
2025-07-20 15:12:11,863 - handlers.user_handlers - INFO - Notification sent to referrer ********** about new user Islam (ID: 6441575752): success
2025-07-20 15:12:17,671 - handlers.user_handlers - INFO - Custom referral parameter used: from_channel -> User ID: **********
2025-07-20 15:12:21,048 - handlers.callback_handlers - ERROR - Error in callback handler for data 'joined': Query is too old and response timeout expired or query id is invalid
2025-07-20 15:12:21,425 - __main__ - ERROR - Error in callback handler: Query is too old and response timeout expired or query id is invalid
2025-07-20 15:12:22,180 - handlers.callback_handlers - ERROR - Error in callback handler for data 'submitTask_task_1752995334.fd08d118': Query is too old and response timeout expired or query id is invalid
2025-07-20 15:12:22,571 - __main__ - ERROR - Error in callback handler: Query is too old and response timeout expired or query id is invalid
2025-07-20 15:12:23,284 - services.user_service - INFO - Created new user: 6893318408 (Muhammed)
2025-07-20 15:12:25,786 - handlers.user_handlers - ERROR - Error sending new user notification: Forbidden: bot is not a member of the channel chat
2025-07-20 15:12:28,379 - handlers.user_handlers - INFO - Notification sent to referrer ********** about new user Muhammed (ID: 6893318408): success
2025-07-20 15:12:32,321 - handlers.callback_handlers - ERROR - Error in callback handler for data 'joined': Query is too old and response timeout expired or query id is invalid
2025-07-20 15:12:32,695 - __main__ - ERROR - Error in callback handler: Query is too old and response timeout expired or query id is invalid
2025-07-20 15:12:33,440 - handlers.callback_handlers - ERROR - Error in callback handler for data 'joined': Query is too old and response timeout expired or query id is invalid
2025-07-20 15:12:33,813 - __main__ - ERROR - Error in callback handler: Query is too old and response timeout expired or query id is invalid
2025-07-20 15:12:34,535 - handlers.callback_handlers - ERROR - Error in callback handler for data 'joined': Query is too old and response timeout expired or query id is invalid
2025-07-20 15:12:34,893 - __main__ - ERROR - Error in callback handler: Query is too old and response timeout expired or query id is invalid
2025-07-20 15:12:35,616 - handlers.callback_handlers - ERROR - Error in callback handler for data 'joined': Query is too old and response timeout expired or query id is invalid
2025-07-20 15:12:35,974 - __main__ - ERROR - Error in callback handler: Query is too old and response timeout expired or query id is invalid
2025-07-20 15:12:36,726 - handlers.callback_handlers - ERROR - Error in callback handler for data 'joined': Query is too old and response timeout expired or query id is invalid
2025-07-20 15:12:37,097 - __main__ - ERROR - Error in callback handler: Query is too old and response timeout expired or query id is invalid
2025-07-20 15:12:40,612 - services.user_service - INFO - Created new user: 6846519651 (Anil)
2025-07-20 15:12:42,477 - handlers.user_handlers - ERROR - Error sending new user notification: Forbidden: bot is not a member of the channel chat
2025-07-20 15:12:44,558 - handlers.user_handlers - INFO - Notification sent to referrer 7869436918 about new user Anil (ID: 6846519651): success
2025-07-20 15:12:50,156 - handlers.callback_handlers - ERROR - Error in callback handler for data 'joined': Query is too old and response timeout expired or query id is invalid
2025-07-20 15:12:50,517 - __main__ - ERROR - Error in callback handler: Query is too old and response timeout expired or query id is invalid
2025-07-20 15:12:55,609 - services.user_service - INFO - Created new user: 7960072082 (Ajit)
2025-07-20 15:12:57,524 - handlers.user_handlers - ERROR - Error sending new user notification: Forbidden: bot is not a member of the channel chat
2025-07-20 15:12:59,408 - handlers.user_handlers - INFO - Notification sent to referrer ********** about new user Ajit (ID: 7960072082): success
2025-07-20 15:13:02,295 - services.user_service - INFO - Created new user: 7834724139 (KK)
2025-07-20 15:13:04,225 - handlers.user_handlers - ERROR - Error sending new user notification: Forbidden: bot is not a member of the channel chat
2025-07-20 15:13:06,378 - handlers.user_handlers - INFO - Notification sent to referrer ********** about new user KK (ID: 7834724139): success
2025-07-20 15:13:08,727 - handlers.user_handlers - ERROR - Error in handle_start_command: Forbidden: bot was blocked by the user
2025-07-20 15:13:09,094 - __main__ - ERROR - Error in start handler: Forbidden: bot was blocked by the user
2025-07-20 15:13:09,820 - handlers.callback_handlers - ERROR - Error in callback handler for data 'myWallet': Query is too old and response timeout expired or query id is invalid
2025-07-20 15:13:10,181 - __main__ - ERROR - Error in callback handler: Query is too old and response timeout expired or query id is invalid
2025-07-20 15:13:13,087 - handlers.callback_handlers - ERROR - Error in callback handler for data 'joined': Query is too old and response timeout expired or query id is invalid
2025-07-20 15:13:13,451 - __main__ - ERROR - Error in callback handler: Query is too old and response timeout expired or query id is invalid
2025-07-20 15:13:14,171 - handlers.callback_handlers - ERROR - Error in callback handler for data 'joined': Query is too old and response timeout expired or query id is invalid
2025-07-20 15:13:14,530 - __main__ - ERROR - Error in callback handler: Query is too old and response timeout expired or query id is invalid
2025-07-20 15:13:15,251 - handlers.callback_handlers - ERROR - Error in callback handler for data 'joined': Query is too old and response timeout expired or query id is invalid
2025-07-20 15:13:15,613 - __main__ - ERROR - Error in callback handler: Query is too old and response timeout expired or query id is invalid
2025-07-20 15:13:16,337 - handlers.callback_handlers - ERROR - Error in callback handler for data 'joined': Query is too old and response timeout expired or query id is invalid
2025-07-20 15:13:16,704 - __main__ - ERROR - Error in callback handler: Query is too old and response timeout expired or query id is invalid
2025-07-20 15:13:17,587 - handlers.callback_handlers - ERROR - Error in callback handler for data 'joined': Query is too old and response timeout expired or query id is invalid
2025-07-20 15:13:17,950 - __main__ - ERROR - Error in callback handler: Query is too old and response timeout expired or query id is invalid
2025-07-20 15:13:18,668 - handlers.callback_handlers - ERROR - Error in callback handler for data 'joined': Query is too old and response timeout expired or query id is invalid
2025-07-20 15:13:19,032 - __main__ - ERROR - Error in callback handler: Query is too old and response timeout expired or query id is invalid
2025-07-20 15:13:19,757 - handlers.callback_handlers - ERROR - Error in callback handler for data 'joined': Query is too old and response timeout expired or query id is invalid
2025-07-20 15:13:20,122 - __main__ - ERROR - Error in callback handler: Query is too old and response timeout expired or query id is invalid
2025-07-20 15:13:23,313 - handlers.user_handlers - INFO - Custom referral parameter used: from_channel -> User ID: **********
2025-07-20 15:13:25,386 - handlers.callback_handlers - ERROR - Error in callback handler for data 'joined': Query is too old and response timeout expired or query id is invalid
2025-07-20 15:13:25,746 - __main__ - ERROR - Error in callback handler: Query is too old and response timeout expired or query id is invalid
2025-07-20 15:13:28,051 - handlers.user_handlers - INFO - Custom referral parameter used: from_channel -> User ID: **********
2025-07-20 15:13:30,044 - handlers.callback_handlers - ERROR - Error in callback handler for data 'joined': Query is too old and response timeout expired or query id is invalid
2025-07-20 15:13:30,400 - __main__ - ERROR - Error in callback handler: Query is too old and response timeout expired or query id is invalid
2025-07-20 15:13:31,124 - handlers.callback_handlers - ERROR - Error in callback handler for data 'joined': Query is too old and response timeout expired or query id is invalid
2025-07-20 15:13:31,485 - __main__ - ERROR - Error in callback handler: Query is too old and response timeout expired or query id is invalid
2025-07-20 15:13:32,197 - handlers.callback_handlers - ERROR - Error in callback handler for data 'joined': Query is too old and response timeout expired or query id is invalid
2025-07-20 15:13:32,670 - __main__ - ERROR - Error in callback handler: Query is too old and response timeout expired or query id is invalid
2025-07-20 15:13:36,256 - handlers.user_handlers - ERROR - Error in handle_joined_channel: Query is too old and response timeout expired or query id is invalid
2025-07-20 15:13:39,608 - services.user_service - INFO - Created new user: 7459753426 (Akash)
2025-07-20 15:13:41,067 - handlers.user_handlers - ERROR - Error sending new user notification: Forbidden: bot is not a member of the channel chat
2025-07-20 15:13:42,419 - handlers.user_handlers - INFO - Notification sent to referrer ********** about new user Akash (ID: 7459753426): success
2025-07-20 15:13:44,354 - handlers.callback_handlers - ERROR - Error in callback handler for data 'joined': Query is too old and response timeout expired or query id is invalid
2025-07-20 15:13:44,712 - __main__ - ERROR - Error in callback handler: Query is too old and response timeout expired or query id is invalid
2025-07-20 15:13:53,620 - handlers.user_handlers - ERROR - Error in handle_joined_channel: Query is too old and response timeout expired or query id is invalid
2025-07-20 15:13:56,862 - handlers.user_handlers - ERROR - Error in handle_joined_channel: Query is too old and response timeout expired or query id is invalid
2025-07-20 15:13:57,225 - handlers.callback_handlers - ERROR - Error in callback handler for data 'joined': Message is not modified: specified new message content and reply markup are exactly the same as a current content and reply markup of the message
2025-07-20 15:13:57,591 - __main__ - ERROR - Error in callback handler: Query is too old and response timeout expired or query id is invalid
2025-07-20 15:13:58,314 - handlers.callback_handlers - ERROR - Error in callback handler for data 'joined': Query is too old and response timeout expired or query id is invalid
2025-07-20 15:13:58,677 - __main__ - ERROR - Error in callback handler: Query is too old and response timeout expired or query id is invalid
2025-07-20 15:13:59,197 - handlers.user_handlers - INFO - Custom referral parameter used: Gift -> User ID: **********
2025-07-20 15:14:05,130 - services.user_service - INFO - Created new user: 7771242062 (Sl)
2025-07-20 15:14:06,484 - handlers.user_handlers - ERROR - Error sending new user notification: Forbidden: bot is not a member of the channel chat
2025-07-20 15:14:08,129 - handlers.user_handlers - INFO - Notification sent to referrer 6292670556 about new user Sl (ID: 7771242062): success
2025-07-20 15:14:10,503 - services.user_service - INFO - Created new user: 7383204282 (Attitude)
2025-07-20 15:14:11,989 - handlers.user_handlers - ERROR - Error sending new user notification: Forbidden: bot is not a member of the channel chat
2025-07-20 15:14:13,487 - handlers.user_handlers - INFO - Notification sent to referrer ********** about new user Attitude (ID: 7383204282): success
2025-07-20 15:14:16,995 - services.user_service - INFO - Created new user: 7771288631 (Vedant)
2025-07-20 15:14:18,438 - handlers.user_handlers - ERROR - Error sending new user notification: Forbidden: bot is not a member of the channel chat
2025-07-20 15:14:20,095 - handlers.user_handlers - INFO - Notification sent to referrer 6223150481 about new user Vedant (ID: 7771288631): success
2025-07-20 15:14:26,019 - services.referral_service - INFO - Referral reward processed: ********** earned ₹5 for referring Muhammed
2025-07-20 15:14:35,711 - handlers.callback_handlers - ERROR - Error in callback handler for data 'cashOut': Query is too old and response timeout expired or query id is invalid
2025-07-20 15:14:36,083 - __main__ - ERROR - Error in callback handler: Query is too old and response timeout expired or query id is invalid
2025-07-20 15:14:36,604 - handlers.user_handlers - INFO - Custom referral parameter used: Gift -> User ID: **********
2025-07-20 15:14:44,740 - handlers.user_handlers - ERROR - Error showing main menu: Message to edit not found
2025-07-20 15:14:44,946 - handlers.callback_handlers - ERROR - Error in _handle_check_subscription: Message to edit not found
2025-07-20 15:14:51,312 - handlers.callback_handlers - ERROR - Error in callback handler for data 'myWallet': Query is too old and response timeout expired or query id is invalid
2025-07-20 15:14:51,686 - __main__ - ERROR - Error in callback handler: Query is too old and response timeout expired or query id is invalid
2025-07-20 15:14:52,424 - handlers.callback_handlers - ERROR - Error in callback handler for data 'joined': Query is too old and response timeout expired or query id is invalid
2025-07-20 15:14:52,798 - __main__ - ERROR - Error in callback handler: Query is too old and response timeout expired or query id is invalid
2025-07-20 15:14:57,134 - services.referral_service - INFO - Referral reward processed: 6292670556 earned ₹2 for referring Sl
2025-07-20 15:15:02,956 - handlers.callback_handlers - ERROR - Error in callback handler for data 'redeemGiftCode': Query is too old and response timeout expired or query id is invalid
2025-07-20 15:15:03,316 - __main__ - ERROR - Error in callback handler: Query is too old and response timeout expired or query id is invalid
2025-07-20 15:15:03,927 - services.user_service - INFO - Created new user: 6997218811 (Om)
2025-07-20 15:15:05,089 - handlers.user_handlers - ERROR - Error sending new user notification: Forbidden: bot is not a member of the channel chat
2025-07-20 15:15:06,492 - handlers.user_handlers - INFO - Notification sent to referrer ********** about new user Om (ID: 6997218811): success
2025-07-20 15:15:08,330 - handlers.callback_handlers - ERROR - Error in callback handler for data 'joined': Query is too old and response timeout expired or query id is invalid
2025-07-20 15:15:08,700 - __main__ - ERROR - Error in callback handler: Query is too old and response timeout expired or query id is invalid
2025-07-20 15:15:09,438 - handlers.callback_handlers - ERROR - Error in callback handler for data 'joined': Query is too old and response timeout expired or query id is invalid
2025-07-20 15:15:10,210 - __main__ - ERROR - Error in callback handler: Query is too old and response timeout expired or query id is invalid
2025-07-20 15:15:10,948 - handlers.callback_handlers - ERROR - Error in callback handler for data 'joined': Query is too old and response timeout expired or query id is invalid
2025-07-20 15:15:11,316 - __main__ - ERROR - Error in callback handler: Query is too old and response timeout expired or query id is invalid
2025-07-20 15:15:12,047 - handlers.callback_handlers - ERROR - Error in callback handler for data 'myWallet': Query is too old and response timeout expired or query id is invalid
2025-07-20 15:15:12,419 - __main__ - ERROR - Error in callback handler: Query is too old and response timeout expired or query id is invalid
2025-07-20 15:15:19,461 - services.referral_service - INFO - Referral reward processed: 6223150481 earned ₹1 for referring Vedant
2025-07-20 15:15:23,535 - services.referral_service - INFO - Referral reward processed: ********** earned ₹1 for referring Akash
2025-07-20 15:15:24,108 - handlers.session_handlers - INFO - === SESSION MESSAGE HANDLER for user 6055750878, step: redeem_gift_code ===
2025-07-20 15:15:24,108 - handlers.session_handlers - INFO - Session data: {'_id': ObjectId('687cbaa97104627ee20348f2'), 'user_id': 6055750878, 'created_at': 1753004714, 'data': {}, 'step': 'redeem_gift_code', 'updated_at': 1753004714}
2025-07-20 15:15:24,108 - handlers.session_handlers - INFO - Message has text: True
2025-07-20 15:15:24,108 - handlers.session_handlers - INFO - Message has photo: False
2025-07-20 15:15:24,108 - handlers.session_handlers - INFO - Message has video: False
2025-07-20 15:15:24,108 - handlers.session_handlers - INFO - Message has document: False
2025-07-20 15:15:24,108 - handlers.session_handlers - INFO - Message text: '7N1M0DVM5ZTT05M7C4ZL'
2025-07-20 15:15:28,480 - handlers.callback_handlers - ERROR - Error in smart navigation: Message is not modified: specified new message content and reply markup are exactly the same as a current content and reply markup of the message
2025-07-20 15:15:30,545 - handlers.callback_handlers - ERROR - Error in smart navigation: Message is not modified: specified new message content and reply markup are exactly the same as a current content and reply markup of the message
2025-07-20 15:15:35,792 - services.user_service - INFO - Created new user: 8153297249 (Bhug)
2025-07-20 15:15:37,496 - handlers.user_handlers - ERROR - Error sending new user notification: Forbidden: bot is not a member of the channel chat
2025-07-20 15:15:39,111 - handlers.user_handlers - INFO - Notification sent to referrer ********** about new user Bhug (ID: 8153297249): success
2025-07-20 15:16:01,428 - utils.helpers - WARNING - Sync is_admin() called from async context for user 6055750878 - consider using is_admin_async()
2025-07-20 15:16:01,431 - services.admin_service - ERROR - Error getting admin settings for **********: Task <Task pending name='Task-50' coro=<_check_admin_in_new_loop() running at C:\Users\<USER>\Desktop\referbot\python\utils\helpers.py:528> cb=[_run_until_complete_cb() at C:\Python312\Lib\asyncio\base_events.py:182]> got Future <Future pending cb=[_chain_future.<locals>._call_check_cancel() at C:\Python312\Lib\asyncio\futures.py:391]> attached to a different loop
2025-07-20 15:16:12,384 - handlers.user_handlers - ERROR - Error showing main menu: Message to edit not found
2025-07-20 15:16:12,580 - handlers.callback_handlers - ERROR - Error in _handle_check_subscription: Message to edit not found
2025-07-20 15:16:16,880 - services.referral_service - INFO - Referral reward processed: 5986113877 earned ₹4 for referring Debika
2025-07-20 15:16:23,777 - services.referral_service - INFO - Referral reward processed: ********** earned ₹5 for referring Bhug
2025-07-20 15:16:30,792 - services.referral_service - INFO - Referral reward processed: ********** earned ₹4 for referring Om
2025-07-20 15:16:53,001 - services.user_service - INFO - Created new user: 7907982044 (Prakash)
2025-07-20 15:16:54,447 - handlers.user_handlers - ERROR - Error sending new user notification: Forbidden: bot is not a member of the channel chat
2025-07-20 15:16:55,913 - handlers.user_handlers - INFO - Notification sent to referrer 8153297249 about new user Prakash (ID: 7907982044): success
2025-07-20 15:17:21,880 - services.referral_service - INFO - Referral reward processed: 8153297249 earned ₹5 for referring Prakash
2025-07-20 15:17:26,835 - handlers.callback_handlers - ERROR - Error in smart navigation: Message is not modified: specified new message content and reply markup are exactly the same as a current content and reply markup of the message
2025-07-20 15:17:32,258 - services.user_service - INFO - Created new user: 7831697248 (Issac)
2025-07-20 15:17:34,222 - handlers.user_handlers - ERROR - Error sending new user notification: Forbidden: bot is not a member of the channel chat
2025-07-20 15:17:35,909 - handlers.user_handlers - INFO - Notification sent to referrer ********** about new user Issac (ID: 7831697248): success
2025-07-20 15:17:41,781 - handlers.callback_handlers - ERROR - Error in callback handler for data 'joined': Query is too old and response timeout expired or query id is invalid
2025-07-20 15:17:42,130 - __main__ - ERROR - Error in callback handler: Query is too old and response timeout expired or query id is invalid
2025-07-20 15:17:42,828 - handlers.callback_handlers - ERROR - Error in callback handler for data 'cashOut': Query is too old and response timeout expired or query id is invalid
2025-07-20 15:17:43,180 - __main__ - ERROR - Error in callback handler: Query is too old and response timeout expired or query id is invalid
2025-07-20 15:17:48,062 - handlers.callback_handlers - INFO - Message contains media, sending new text message instead of editing
2025-07-20 15:17:51,072 - handlers.callback_handlers - INFO - Message contains media, sending new text message instead of editing
2025-07-20 15:17:53,510 - handlers.callback_handlers - ERROR - Error in smart navigation: Message is not modified: specified new message content and reply markup are exactly the same as a current content and reply markup of the message
2025-07-20 15:17:59,376 - handlers.callback_handlers - ERROR - Error in smart navigation: Message is not modified: specified new message content and reply markup are exactly the same as a current content and reply markup of the message
2025-07-20 15:17:59,741 - handlers.callback_handlers - ERROR - Error in _handle_cash_out: Query is too old and response timeout expired or query id is invalid
2025-07-20 15:18:00,104 - handlers.callback_handlers - ERROR - Error in callback handler for data 'cashOut': Query is too old and response timeout expired or query id is invalid
2025-07-20 15:18:00,466 - __main__ - ERROR - Error in callback handler: Query is too old and response timeout expired or query id is invalid
2025-07-20 15:18:01,198 - handlers.callback_handlers - ERROR - Error in callback handler for data 'cashOut': Query is too old and response timeout expired or query id is invalid
2025-07-20 15:18:01,558 - __main__ - ERROR - Error in callback handler: Query is too old and response timeout expired or query id is invalid
2025-07-20 15:18:01,964 - handlers.session_handlers - INFO - === SESSION MESSAGE HANDLER for user 6055750878, step: submit_task_screenshot ===
2025-07-20 15:18:01,965 - handlers.session_handlers - INFO - Session data: {'_id': ObjectId('687cbb447104627ee20348f3'), 'user_id': 6055750878, 'created_at': 1753004868, 'data': {'task_id': 'task_1752995334.fd08d118'}, 'step': 'submit_task_screenshot', 'updated_at': 1753004868}
2025-07-20 15:18:01,965 - handlers.session_handlers - INFO - Message has text: False
2025-07-20 15:18:01,965 - handlers.session_handlers - INFO - Message has photo: True
2025-07-20 15:18:01,965 - handlers.session_handlers - INFO - Message has video: False
2025-07-20 15:18:01,966 - handlers.session_handlers - INFO - Message has document: False
2025-07-20 15:18:01,966 - handlers.session_handlers - INFO - Message text: 'None'
2025-07-20 15:18:03,583 - services.task_service - INFO - Created new task submission: submission_1753004883.d3f3fae8
2025-07-20 15:18:26,984 - services.referral_service - INFO - Referral reward processed: ********** earned ₹2 for referring Issac
2025-07-20 15:18:27,690 - handlers.user_handlers - INFO - Custom referral parameter used: from_channel -> User ID: **********
2025-07-20 15:18:29,730 - services.user_service - INFO - Created new user: 7880499409 (Mamatha)
2025-07-20 15:18:31,144 - handlers.user_handlers - ERROR - Error sending new user notification: Forbidden: bot is not a member of the channel chat
2025-07-20 15:18:32,787 - handlers.user_handlers - INFO - Notification sent to referrer ********** about new user Mamatha (ID: 7880499409): success
2025-07-20 15:18:41,331 - handlers.user_handlers - INFO - Custom referral parameter used: from_channel -> User ID: **********
2025-07-20 15:18:59,900 - handlers.user_handlers - ERROR - Error showing main menu: Message to edit not found
2025-07-20 15:19:00,095 - handlers.callback_handlers - ERROR - Error in _handle_check_subscription: Message to edit not found
2025-07-20 15:19:15,918 - services.referral_service - INFO - Referral reward processed: ********** earned ₹3 for referring Mamatha
2025-07-20 15:19:31,441 - handlers.callback_handlers - INFO - Message contains media, sending new text message instead of editing
2025-07-20 15:19:41,018 - services.user_service - INFO - Created new user: 1650817487 (Jay)
2025-07-20 15:19:43,430 - handlers.user_handlers - ERROR - Error sending new user notification: Forbidden: bot is not a member of the channel chat
2025-07-20 15:19:45,978 - handlers.user_handlers - INFO - Notification sent to referrer ********** about new user Jay (ID: 1650817487): success
2025-07-20 15:19:52,361 - handlers.callback_handlers - INFO - Message contains media, sending new text message instead of editing
2025-07-20 15:19:57,064 - services.user_service - INFO - Created new user: 6186838010 (Ritul)
2025-07-20 15:19:58,787 - handlers.user_handlers - ERROR - Error sending new user notification: Forbidden: bot is not a member of the channel chat
2025-07-20 15:20:00,893 - handlers.user_handlers - INFO - Notification sent to referrer ********** about new user Ritul (ID: 6186838010): success
2025-07-20 15:20:12,151 - handlers.user_handlers - INFO - Custom referral parameter used: from_channel -> User ID: **********
2025-07-20 15:20:41,304 - handlers.callback_handlers - INFO - Message contains media, sending new text message instead of editing
2025-07-20 15:20:44,479 - services.user_service - INFO - Created new user: 7606366488 (Ashveer)
2025-07-20 15:20:45,778 - handlers.user_handlers - ERROR - Error sending new user notification: Forbidden: bot is not a member of the channel chat
2025-07-20 15:20:47,198 - handlers.user_handlers - INFO - Notification sent to referrer ********** about new user Ashveer (ID: 7606366488): success
2025-07-20 15:21:00,703 - handlers.user_handlers - ERROR - Error showing main menu: Message to edit not found
2025-07-20 15:21:00,903 - handlers.callback_handlers - ERROR - Error in _handle_check_subscription: Message to edit not found
2025-07-20 15:21:09,475 - handlers.user_handlers - INFO - Custom referral parameter used: Gift -> User ID: **********
2025-07-20 15:21:12,786 - handlers.user_handlers - INFO - Custom referral parameter used: from_channel -> User ID: **********
2025-07-20 15:21:18,128 - handlers.user_handlers - ERROR - Error showing main menu: Message to edit not found
2025-07-20 15:21:18,325 - handlers.callback_handlers - ERROR - Error in _handle_check_subscription: Message to edit not found
2025-07-20 15:21:23,552 - handlers.callback_handlers - ERROR - Error in _handle_check_subscription: Query is too old and response timeout expired or query id is invalid
2025-07-20 15:21:23,917 - handlers.callback_handlers - ERROR - Error in callback handler for data 'checkSubscription': Query is too old and response timeout expired or query id is invalid
2025-07-20 15:21:24,289 - __main__ - ERROR - Error in callback handler: Query is too old and response timeout expired or query id is invalid
2025-07-20 15:21:24,789 - handlers.user_handlers - INFO - Custom referral parameter used: Gift -> User ID: **********
2025-07-20 15:21:34,114 - services.user_service - INFO - Created new user: 7561288991 (SOHOM)
2025-07-20 15:21:35,291 - handlers.user_handlers - ERROR - Error sending new user notification: Forbidden: bot is not a member of the channel chat
2025-07-20 15:21:36,579 - handlers.user_handlers - INFO - Notification sent to referrer ********** about new user SOHOM (ID: 7561288991): success
2025-07-20 15:21:37,661 - handlers.user_handlers - ERROR - Error in handle_start_command: Forbidden: bot was blocked by the user
2025-07-20 15:21:38,029 - __main__ - ERROR - Error in start handler: Forbidden: bot was blocked by the user
2025-07-20 15:21:39,864 - handlers.user_handlers - ERROR - Error in handle_start_command: Forbidden: bot was blocked by the user
2025-07-20 15:21:40,234 - __main__ - ERROR - Error in start handler: Forbidden: bot was blocked by the user
2025-07-20 15:21:40,737 - handlers.user_handlers - INFO - Custom referral parameter used: Gift -> User ID: **********
2025-07-20 15:21:48,372 - services.referral_service - INFO - Referral reward processed: ********** earned ₹3 for referring Ashveer
2025-07-20 15:21:53,844 - handlers.callback_handlers - INFO - Message contains media, sending new text message instead of editing
2025-07-20 15:22:06,104 - handlers.session_handlers - INFO - === SESSION MESSAGE HANDLER for user 7880499409, step: submit_task_screenshot ===
2025-07-20 15:22:06,105 - handlers.session_handlers - INFO - Session data: {'_id': ObjectId('687cbc3a7104627ee20348f6'), 'user_id': 7880499409, 'created_at': 1753005114, 'data': {'task_id': 'task_1752995334.fd08d118'}, 'step': 'submit_task_screenshot', 'updated_at': 1753005114}
2025-07-20 15:22:06,105 - handlers.session_handlers - INFO - Message has text: False
2025-07-20 15:22:06,105 - handlers.session_handlers - INFO - Message has photo: True
2025-07-20 15:22:06,105 - handlers.session_handlers - INFO - Message has video: False
2025-07-20 15:22:06,105 - handlers.session_handlers - INFO - Message has document: False
2025-07-20 15:22:06,106 - handlers.session_handlers - INFO - Message text: 'None'
2025-07-20 15:22:10,617 - services.task_service - INFO - Created new task submission: submission_1753005130.2c04961b
2025-07-20 15:22:12,023 - handlers.user_handlers - INFO - Custom referral parameter used: from_channel -> User ID: **********
2025-07-20 15:22:15,125 - handlers.user_handlers - INFO - Custom referral parameter used: from_channel -> User ID: **********
2025-07-20 15:22:25,050 - services.referral_service - INFO - Referral reward processed: ********** earned ₹1 for referring Jay
2025-07-20 15:22:29,872 - services.referral_service - INFO - Referral reward processed: ********** earned ₹5 for referring Nani
2025-07-20 15:22:41,932 - handlers.callback_handlers - ERROR - Error in smart navigation: Message is not modified: specified new message content and reply markup are exactly the same as a current content and reply markup of the message
2025-07-20 15:22:45,815 - handlers.callback_handlers - ERROR - Error in smart navigation: Message is not modified: specified new message content and reply markup are exactly the same as a current content and reply markup of the message
2025-07-20 15:22:47,841 - handlers.callback_handlers - ERROR - Error in smart navigation: Message is not modified: specified new message content and reply markup are exactly the same as a current content and reply markup of the message
2025-07-20 15:22:52,485 - handlers.callback_handlers - ERROR - Error in smart navigation: Message is not modified: specified new message content and reply markup are exactly the same as a current content and reply markup of the message
2025-07-20 15:23:37,862 - services.user_service - INFO - Created new user: 7786867899 (n a o r i s 🐧)
2025-07-20 15:23:39,032 - handlers.user_handlers - ERROR - Error sending new user notification: Forbidden: bot is not a member of the channel chat
2025-07-20 15:23:40,266 - handlers.user_handlers - INFO - Notification sent to referrer 7585459513 about new user n a o r i s 🐧 (ID: 7786867899): success
2025-07-20 15:23:47,535 - handlers.callback_handlers - ERROR - Error in smart navigation: Message is not modified: specified new message content and reply markup are exactly the same as a current content and reply markup of the message
2025-07-20 15:23:49,380 - handlers.callback_handlers - INFO - Message contains media, sending new text message instead of editing
2025-07-20 15:24:23,083 - services.user_service - INFO - Created new user: 1495061080 (Muhammed)
2025-07-20 15:24:24,423 - handlers.user_handlers - ERROR - Error sending new user notification: Forbidden: bot is not a member of the channel chat
2025-07-20 15:24:25,936 - handlers.user_handlers - INFO - Notification sent to referrer 6893318408 about new user Muhammed (ID: 1495061080): success
2025-07-20 15:24:30,682 - services.withdrawal_service - INFO - Withdrawal record saved for user **********: withdrawal_1753005270.57ef848b
2025-07-20 15:24:36,341 - services.referral_service - INFO - Referral reward processed: 7585459513 earned ₹3 for referring n a o r i s 🐧
2025-07-20 15:24:36,904 - handlers.session_handlers - INFO - === SESSION MESSAGE HANDLER for user 7602539789, step: redeem_gift_code ===
2025-07-20 15:24:36,905 - handlers.session_handlers - INFO - Session data: {'_id': ObjectId('687cbcb47104627ee20348f9'), 'user_id': 7602539789, 'created_at': 1753005236, 'data': {}, 'step': 'redeem_gift_code', 'updated_at': 1753005236}
2025-07-20 15:24:36,906 - handlers.session_handlers - INFO - Message has text: True
2025-07-20 15:24:36,906 - handlers.session_handlers - INFO - Message has photo: False
2025-07-20 15:24:36,906 - handlers.session_handlers - INFO - Message has video: False
2025-07-20 15:24:36,907 - handlers.session_handlers - INFO - Message has document: False
2025-07-20 15:24:36,907 - handlers.session_handlers - INFO - Message text: 'B7XKQ9E2T6LZ1FVC34YM'
2025-07-20 15:25:04,107 - services.referral_service - INFO - Referral reward processed: 6893318408 earned ₹3 for referring Muhammed
2025-07-20 15:25:09,280 - services.user_service - INFO - Created new user: 1871907952 (Luffy)
2025-07-20 15:25:10,744 - handlers.user_handlers - ERROR - Error sending new user notification: Forbidden: bot is not a member of the channel chat
2025-07-20 15:25:12,168 - handlers.user_handlers - INFO - Notification sent to referrer ********** about new user Luffy (ID: 1871907952): success
2025-07-20 15:25:37,350 - handlers.user_handlers - INFO - Custom referral parameter used: Gift -> User ID: **********
2025-07-20 15:25:45,369 - services.referral_service - INFO - Referral reward processed: ********** earned ₹5 for referring Pradeep
2025-07-20 15:25:56,703 - handlers.user_handlers - ERROR - Error showing main menu: Message to edit not found
2025-07-20 15:25:56,894 - handlers.callback_handlers - ERROR - Error in _handle_check_subscription: Message to edit not found
2025-07-20 15:26:01,422 - services.referral_service - INFO - Referral reward processed: ********** earned ₹2 for referring Luffy
2025-07-20 15:26:02,240 - services.user_service - INFO - Created new user: 1515620067 (Robi Islam)
2025-07-20 15:26:03,610 - handlers.user_handlers - ERROR - Error sending new user notification: Forbidden: bot is not a member of the channel chat
2025-07-20 15:26:04,982 - handlers.user_handlers - INFO - Notification sent to referrer 8153297249 about new user Robi Islam (ID: 1515620067): success
2025-07-20 15:26:08,667 - services.user_service - INFO - Created new user: 6041839958 (Mohan)
2025-07-20 15:26:10,023 - handlers.user_handlers - ERROR - Error sending new user notification: Forbidden: bot is not a member of the channel chat
2025-07-20 15:26:11,621 - handlers.user_handlers - INFO - Notification sent to referrer ********** about new user Mohan (ID: 6041839958): success
2025-07-20 15:27:07,369 - services.referral_service - INFO - Referral reward processed: ********** earned ₹3 for referring Mohan
2025-07-20 15:27:14,006 - services.user_service - INFO - Created new user: ********** (Red)
2025-07-20 15:27:15,171 - handlers.user_handlers - ERROR - Error sending new user notification: Forbidden: bot is not a member of the channel chat
2025-07-20 15:27:16,470 - handlers.user_handlers - INFO - Notification sent to referrer ********** about new user Red (ID: **********): success
2025-07-20 15:27:42,258 - handlers.callback_handlers - INFO - Message contains media, sending new text message instead of editing
2025-07-20 15:28:13,573 - handlers.callback_handlers - INFO - Message contains media, sending new text message instead of editing
2025-07-20 15:28:25,942 - handlers.user_handlers - INFO - Custom referral parameter used: Gift -> User ID: **********
2025-07-20 15:29:02,437 - handlers.callback_handlers - INFO - Message contains media, sending new text message instead of editing
2025-07-20 15:29:30,660 - handlers.user_handlers - INFO - Custom referral parameter used: Gift -> User ID: **********
2025-07-20 15:29:41,295 - services.referral_service - INFO - Referral reward processed: ********** earned ₹5 for referring Candi
2025-07-20 15:30:06,766 - services.user_service - INFO - Created new user: 7041894898 (Are)
2025-07-20 15:30:07,906 - handlers.user_handlers - ERROR - Error sending new user notification: Forbidden: bot is not a member of the channel chat
2025-07-20 15:30:09,239 - handlers.user_handlers - INFO - Notification sent to referrer ********** about new user Are (ID: 7041894898): success
2025-07-20 15:30:27,721 - services.referral_service - INFO - Referral reward processed: ********** earned ₹4 for referring Mom
2025-07-20 15:30:29,539 - handlers.callback_handlers - INFO - Message contains media, sending new text message instead of editing
2025-07-20 15:30:38,786 - handlers.session_handlers - INFO - === SESSION MESSAGE HANDLER for user 7585459513, step: submit_task_screenshot ===
2025-07-20 15:30:38,786 - handlers.session_handlers - INFO - Session data: {'_id': ObjectId('687cbe3e7104627ee20348fc'), 'user_id': 7585459513, 'created_at': 1753005630, 'data': {'task_id': 'task_1752995334.fd08d118'}, 'step': 'submit_task_screenshot', 'updated_at': 1753005630}
2025-07-20 15:30:38,786 - handlers.session_handlers - INFO - Message has text: False
2025-07-20 15:30:38,786 - handlers.session_handlers - INFO - Message has photo: True
2025-07-20 15:30:38,788 - handlers.session_handlers - INFO - Message has video: False
2025-07-20 15:30:38,788 - handlers.session_handlers - INFO - Message has document: False
2025-07-20 15:30:38,788 - handlers.session_handlers - INFO - Message text: 'None'
2025-07-20 15:30:39,795 - services.task_service - INFO - Created new task submission: submission_1753005639.bff2247d
2025-07-20 15:31:21,151 - services.referral_service - INFO - Referral reward processed: 8153297249 earned ₹1 for referring Robi Islam
2025-07-20 15:31:35,964 - utils.helpers - WARNING - Sync is_admin() called from async context for user ********** - consider using is_admin_async()
2025-07-20 15:31:35,965 - services.admin_service - ERROR - Error getting admin settings for **********: Task <Task pending name='Task-132' coro=<_check_admin_in_new_loop() running at C:\Users\<USER>\Desktop\referbot\python\utils\helpers.py:528> cb=[_run_until_complete_cb() at C:\Python312\Lib\asyncio\base_events.py:182]> got Future <Future pending cb=[_chain_future.<locals>._call_check_cancel() at C:\Python312\Lib\asyncio\futures.py:391]> attached to a different loop
2025-07-20 15:31:41,978 - services.referral_service - INFO - Referral reward processed: 6514877500 earned ₹2 for referring ~Sudram💖
2025-07-20 15:31:55,767 - services.user_service - INFO - Created new user: ********** (Yug)
2025-07-20 15:31:56,915 - handlers.user_handlers - ERROR - Error sending new user notification: Forbidden: bot is not a member of the channel chat
2025-07-20 15:31:58,848 - handlers.user_handlers - INFO - Notification sent to referrer ********** about new user Yug (ID: **********): success
2025-07-20 15:32:08,047 - handlers.session_handlers - INFO - === SESSION MESSAGE HANDLER for user **********, step: set_account_number ===
2025-07-20 15:32:08,047 - handlers.session_handlers - INFO - Session data: {'_id': ObjectId('687cbe8b7104627ee20348ff'), 'user_id': **********, 'created_at': **********, 'data': {}, 'step': 'set_account_number', 'updated_at': **********}
2025-07-20 15:32:08,047 - handlers.session_handlers - INFO - Message has text: True
2025-07-20 15:32:08,047 - handlers.session_handlers - INFO - Message has photo: False
2025-07-20 15:32:08,048 - handlers.session_handlers - INFO - Message has video: False
2025-07-20 15:32:08,048 - handlers.session_handlers - INFO - Message has document: False
2025-07-20 15:32:08,048 - handlers.session_handlers - INFO - Message text: '**********'
2025-07-20 15:32:35,227 - services.user_service - INFO - Created new user: ********** (Germaine Karenah)
2025-07-20 15:32:36,451 - handlers.user_handlers - ERROR - Error sending new user notification: Forbidden: bot is not a member of the channel chat
2025-07-20 15:32:38,076 - handlers.user_handlers - INFO - Notification sent to referrer ********** about new user Germaine Karenah (ID: **********): success
2025-07-20 15:32:56,529 - handlers.session_handlers - INFO - === SESSION MESSAGE HANDLER for user **********, step: set_account_name ===
2025-07-20 15:32:56,530 - handlers.session_handlers - INFO - Session data: {'_id': ObjectId('687cbeca7104627ee2034901'), 'user_id': **********, 'created_at': **********, 'data': {}, 'step': 'set_account_name', 'updated_at': **********}
2025-07-20 15:32:56,530 - handlers.session_handlers - INFO - Message has text: True
2025-07-20 15:32:56,530 - handlers.session_handlers - INFO - Message has photo: False
2025-07-20 15:32:56,531 - handlers.session_handlers - INFO - Message has video: False
2025-07-20 15:32:56,531 - handlers.session_handlers - INFO - Message has document: False
2025-07-20 15:32:56,531 - handlers.session_handlers - INFO - Message text: 'Ram Narayana'
2025-07-20 15:33:17,837 - handlers.session_handlers - INFO - === SESSION MESSAGE HANDLER for user **********, step: set_account_ifsc ===
2025-07-20 15:33:17,837 - handlers.session_handlers - INFO - Session data: {'_id': ObjectId('687cbed97104627ee2034902'), 'user_id': **********, 'created_at': **********, 'data': {}, 'step': 'set_account_ifsc', 'updated_at': **********}
2025-07-20 15:33:17,837 - handlers.session_handlers - INFO - Message has text: True
2025-07-20 15:33:17,838 - handlers.session_handlers - INFO - Message has photo: False
2025-07-20 15:33:17,838 - handlers.session_handlers - INFO - Message has video: False
2025-07-20 15:33:17,838 - handlers.session_handlers - INFO - Message has document: False
2025-07-20 15:33:17,838 - handlers.session_handlers - INFO - Message text: 'KKBK0007748'
2025-07-20 15:33:19,388 - utils.helpers - INFO - Cached bank details for IFSC: KKBK0007748
2025-07-20 15:33:32,653 - handlers.admin_handlers - INFO - Loaded 9 pending withdrawals in 0.494s
2025-07-20 15:33:33,052 - handlers.admin_handlers - INFO - handle_view_pending_withdrawals completed in 0.893s
2025-07-20 15:33:45,997 - handlers.user_handlers - INFO - Custom referral parameter used: from_channel -> User ID: **********
2025-07-20 15:33:50,792 - services.withdrawal_service - INFO - Withdrawal rejected: User **********, Amount ₹100
2025-07-20 15:33:51,812 - services.user_service - INFO - Created new user: ********** (Sundram)
2025-07-20 15:33:52,963 - handlers.user_handlers - ERROR - Error sending new user notification: Forbidden: bot is not a member of the channel chat
2025-07-20 15:33:54,221 - handlers.user_handlers - INFO - Notification sent to referrer ********** about new user Sundram (ID: **********): success
2025-07-20 15:33:55,716 - handlers.session_handlers - INFO - === SESSION MESSAGE HANDLER for user **********, step: set_account_email ===
2025-07-20 15:33:55,716 - handlers.session_handlers - INFO - Session data: {'_id': ObjectId('687cbf017104627ee2034903'), 'user_id': **********, 'created_at': **********, 'data': {}, 'step': 'set_account_email', 'updated_at': **********}
2025-07-20 15:33:55,717 - handlers.session_handlers - INFO - Message has text: True
2025-07-20 15:33:55,717 - handlers.session_handlers - INFO - Message has photo: False
2025-07-20 15:33:55,718 - handlers.session_handlers - INFO - Message has video: False
2025-07-20 15:33:55,718 - handlers.session_handlers - INFO - Message has document: False
2025-07-20 15:33:55,718 - handlers.session_handlers - INFO - Message text: '<EMAIL>'
2025-07-20 15:34:10,932 - handlers.admin_handlers - INFO - Loaded 8 pending withdrawals in 0.461s
2025-07-20 15:34:11,326 - handlers.admin_handlers - INFO - handle_view_pending_withdrawals completed in 0.855s
2025-07-20 15:34:18,704 - services.referral_service - INFO - Referral reward processed: ********** earned ₹3 for referring Sundram
2025-07-20 15:34:19,276 - handlers.session_handlers - INFO - === SESSION MESSAGE HANDLER for user **********, step: set_mobile_number ===
2025-07-20 15:34:19,276 - handlers.session_handlers - INFO - Session data: {'_id': ObjectId('687cbf167104627ee2034904'), 'user_id': **********, 'created_at': 1753005846, 'data': {}, 'step': 'set_mobile_number', 'updated_at': 1753005846}
2025-07-20 15:34:19,276 - handlers.session_handlers - INFO - Message has text: True
2025-07-20 15:34:19,277 - handlers.session_handlers - INFO - Message has photo: False
2025-07-20 15:34:19,277 - handlers.session_handlers - INFO - Message has video: False
2025-07-20 15:34:19,277 - handlers.session_handlers - INFO - Message has document: False
2025-07-20 15:34:19,277 - handlers.session_handlers - INFO - Message text: '9182759239'
2025-07-20 15:34:26,436 - services.withdrawal_service - INFO - Withdrawal rejected: User 6521550474, Amount ₹100
2025-07-20 15:34:27,864 - services.user_service - INFO - Created new user: 7177326003 (K)
2025-07-20 15:34:29,064 - handlers.user_handlers - ERROR - Error sending new user notification: Forbidden: bot is not a member of the channel chat
2025-07-20 15:34:30,387 - handlers.user_handlers - INFO - Notification sent to referrer ********** about new user K (ID: 7177326003): success
2025-07-20 15:34:46,925 - services.referral_service - INFO - Referral reward processed: 5590996532 earned ₹5 for referring ƘЄƧHV
2025-07-20 15:34:49,113 - handlers.session_handlers - INFO - === SESSION MESSAGE HANDLER for user **********, step: set_mobile_number ===
2025-07-20 15:34:49,113 - handlers.session_handlers - INFO - Session data: {'_id': ObjectId('687cbf167104627ee2034904'), 'user_id': **********, 'created_at': 1753005873, 'data': {}, 'step': 'set_mobile_number', 'updated_at': 1753005873}
2025-07-20 15:34:49,113 - handlers.session_handlers - INFO - Message has text: True
2025-07-20 15:34:49,113 - handlers.session_handlers - INFO - Message has photo: False
2025-07-20 15:34:49,114 - handlers.session_handlers - INFO - Message has video: False
2025-07-20 15:34:49,114 - handlers.session_handlers - INFO - Message has document: False
2025-07-20 15:34:49,114 - handlers.session_handlers - INFO - Message text: '+919182759239'
2025-07-20 15:34:49,616 - handlers.session_handlers - INFO - Attempting to send OTP to mobile number: +919182759239
2025-07-20 15:34:49,657 - services.withdrawal_service - INFO - Using OTP API key from environment variable
2025-07-20 15:34:49,657 - services.withdrawal_service - INFO - Converted +919182759239 to API format: 9182759239
2025-07-20 15:34:49,657 - services.withdrawal_service - INFO - Sending OTP to +919182759239 (API format: 9182759239) using URL: https://sms.renflair.in/V1.php
2025-07-20 15:34:51,438 - services.withdrawal_service - INFO - OTP API response for +919182759239: Status 200, Content: {"return":true,"request_id":"XlOxHDVda5PF7Wi","message":"SMS sent successfully.","status":"SUCCESS"}
2025-07-20 15:34:51,443 - services.withdrawal_service - INFO - OTP API analysis for +919182759239 (API format: 9182759239): Status=200, HasFailure=False, HasSuccess=True, ResponseLength=100
2025-07-20 15:34:51,443 - services.withdrawal_service - INFO - OTP API call successful for +919182759239. OTP: 5326
2025-07-20 15:34:51,445 - services.withdrawal_service - INFO - Note: SMS delivery may take 1-5 minutes depending on carrier
2025-07-20 15:34:51,899 - handlers.session_handlers - INFO - OTP API call successful for +919182759239, user ********** moved to verification step
2025-07-20 15:34:52,025 - handlers.user_handlers - INFO - Custom referral parameter used: from_channel -> User ID: **********
2025-07-20 15:35:04,109 - handlers.user_handlers - INFO - Custom referral parameter used: from_channel -> User ID: **********
2025-07-20 15:35:07,364 - handlers.callback_handlers - ERROR - Error in callback handler for data 'resend_otp': 'CallbackHandlers' object has no attribute 'session_handlers'
2025-07-20 15:35:27,550 - handlers.callback_handlers - ERROR - Error sending task media: Message can't be deleted for everyone
2025-07-20 15:35:46,631 - handlers.callback_handlers - ERROR - Error in _handle_custom_referral_link: Message is not modified: specified new message content and reply markup are exactly the same as a current content and reply markup of the message
2025-07-20 15:35:53,014 - services.referral_service - INFO - Referral reward processed: ********** earned ₹3 for referring K
2025-07-20 15:35:54,779 - handlers.session_handlers - INFO - === SESSION MESSAGE HANDLER for user **********, step: verify_otp ===
2025-07-20 15:35:54,779 - handlers.session_handlers - INFO - Session data: {'_id': ObjectId('687cbf167104627ee2034904'), 'user_id': **********, 'created_at': 1753005891, 'data': {'mobile_number': '+919182759239', 'otp': '5326', 'otp_timestamp': 1753005891}, 'step': 'verify_otp', 'updated_at': 1753005891}
2025-07-20 15:35:54,780 - handlers.session_handlers - INFO - Message has text: True
2025-07-20 15:35:54,780 - handlers.session_handlers - INFO - Message has photo: False
2025-07-20 15:35:54,780 - handlers.session_handlers - INFO - Message has video: False
2025-07-20 15:35:54,781 - handlers.session_handlers - INFO - Message has document: False
2025-07-20 15:35:54,781 - handlers.session_handlers - INFO - Message text: '5326'
2025-07-20 15:36:05,320 - handlers.admin_handlers - INFO - Loaded 7 pending withdrawals in 0.478s
2025-07-20 15:36:05,696 - handlers.admin_handlers - INFO - handle_view_pending_withdrawals completed in 0.853s
2025-07-20 15:36:32,255 - services.withdrawal_service - INFO - Withdrawal rejected: User 7132632561, Amount ₹100
2025-07-20 15:36:32,446 - handlers.callback_handlers - ERROR - Error updating admin message: Can't parse entities: unsupported start tag "\" at byte offset 56
2025-07-20 15:36:42,063 - services.withdrawal_service - INFO - Withdrawal rejected: User **********, Amount ₹100
2025-07-20 15:37:11,706 - services.user_service - INFO - Created new user: 7259314511 (Keshav)
2025-07-20 15:37:13,298 - handlers.user_handlers - ERROR - Error sending new user notification: Forbidden: bot is not a member of the channel chat
2025-07-20 15:37:14,537 - handlers.user_handlers - INFO - Notification sent to referrer 5340394045 about new user Keshav (ID: 7259314511): success
2025-07-20 15:37:38,671 - services.withdrawal_service - INFO - Withdrawal record saved for user **********: withdrawal_1753006058.98545db1
2025-07-20 15:37:43,697 - handlers.session_handlers - INFO - === SESSION MESSAGE HANDLER for user 7257657996, step: submit_task_screenshot ===
2025-07-20 15:37:43,697 - handlers.session_handlers - INFO - Session data: {'_id': ObjectId('687cbfdd7104627ee2034908'), 'user_id': 7257657996, 'created_at': 1753006045, 'data': {'task_id': 'task_1752995334.fd08d118'}, 'step': 'submit_task_screenshot', 'updated_at': 1753006045}
2025-07-20 15:37:43,697 - handlers.session_handlers - INFO - Message has text: False
2025-07-20 15:37:43,697 - handlers.session_handlers - INFO - Message has photo: True
2025-07-20 15:37:43,697 - handlers.session_handlers - INFO - Message has video: False
2025-07-20 15:37:43,697 - handlers.session_handlers - INFO - Message has document: False
2025-07-20 15:37:43,697 - handlers.session_handlers - INFO - Message text: 'None'
2025-07-20 15:37:44,783 - services.task_service - INFO - Created new task submission: submission_1753006064.b910257d
2025-07-20 15:37:50,258 - services.referral_service - INFO - Referral reward processed: 5340394045 earned ₹1 for referring Keshav
2025-07-20 15:37:52,678 - handlers.callback_handlers - INFO - Message contains media, sending new text message instead of editing
2025-07-20 15:38:29,952 - services.user_service - INFO - Created new user: 6703107280 (Mughal)
2025-07-20 15:38:31,167 - handlers.user_handlers - ERROR - Error sending new user notification: Forbidden: bot is not a member of the channel chat
2025-07-20 15:38:32,417 - handlers.user_handlers - INFO - Notification sent to referrer ********** about new user Mughal (ID: 6703107280): success
2025-07-20 15:38:34,229 - utils.helpers - WARNING - Sync is_admin() called from async context for user 7259314511 - consider using is_admin_async()
2025-07-20 15:38:34,232 - services.admin_service - ERROR - Error getting admin settings for **********: Task <Task pending name='Task-184' coro=<_check_admin_in_new_loop() running at C:\Users\<USER>\Desktop\referbot\python\utils\helpers.py:528> cb=[_run_until_complete_cb() at C:\Python312\Lib\asyncio\base_events.py:182]> got Future <Future pending cb=[_chain_future.<locals>._call_check_cancel() at C:\Python312\Lib\asyncio\futures.py:391]> attached to a different loop
2025-07-20 15:39:13,717 - handlers.user_handlers - INFO - Custom referral parameter used: Gift -> User ID: **********
2025-07-20 15:39:40,490 - handlers.session_handlers - INFO - === SESSION MESSAGE HANDLER for user **********, step: redeem_gift_code ===
2025-07-20 15:39:40,491 - handlers.session_handlers - INFO - Session data: {'_id': ObjectId('687cc0087104627ee2034909'), 'user_id': **********, 'created_at': 1753006088, 'data': {}, 'step': 'redeem_gift_code', 'updated_at': 1753006088}
2025-07-20 15:39:40,491 - handlers.session_handlers - INFO - Message has text: True
2025-07-20 15:39:40,491 - handlers.session_handlers - INFO - Message has photo: False
2025-07-20 15:39:40,491 - handlers.session_handlers - INFO - Message has video: False
2025-07-20 15:39:40,493 - handlers.session_handlers - INFO - Message has document: False
2025-07-20 15:39:40,493 - handlers.session_handlers - INFO - Message text: 'B7XKQ9E2T6LZ1FVC34YM'
2025-07-20 15:39:53,250 - services.referral_service - INFO - Referral reward processed: ********** earned ₹5 for referring Mughal
2025-07-20 15:39:56,575 - handlers.user_handlers - INFO - Custom referral parameter used: Gift -> User ID: **********
2025-07-20 15:40:03,752 - services.user_service - INFO - Created new user: 7670290449 (Rk)
2025-07-20 15:40:04,907 - handlers.user_handlers - ERROR - Error sending new user notification: Forbidden: bot is not a member of the channel chat
2025-07-20 15:40:06,331 - handlers.user_handlers - INFO - Notification sent to referrer ********** about new user Rk (ID: 7670290449): success
2025-07-20 15:41:07,021 - handlers.user_handlers - INFO - Custom referral parameter used: from_channel -> User ID: **********
2025-07-20 15:41:20,792 - services.user_service - INFO - Created new user: 7086844707 (Mughal)
2025-07-20 15:41:22,000 - handlers.user_handlers - ERROR - Error sending new user notification: Forbidden: bot is not a member of the channel chat
2025-07-20 15:41:23,268 - handlers.user_handlers - INFO - Notification sent to referrer 6703107280 about new user Mughal (ID: 7086844707): success
2025-07-20 15:41:44,104 - handlers.callback_handlers - INFO - Message contains media, sending new text message instead of editing
2025-07-20 15:41:56,839 - handlers.session_handlers - INFO - === SESSION MESSAGE HANDLER for user 5340394045, step: submit_task_screenshot ===
2025-07-20 15:41:56,840 - handlers.session_handlers - INFO - Session data: {'_id': ObjectId('687cbfae7104627ee2034906'), 'user_id': 5340394045, 'created_at': 1753006304, 'data': {'task_id': 'task_1752995334.fd08d118'}, 'step': 'submit_task_screenshot', 'updated_at': 1753006304}
2025-07-20 15:41:56,840 - handlers.session_handlers - INFO - Message has text: False
2025-07-20 15:41:56,840 - handlers.session_handlers - INFO - Message has photo: True
2025-07-20 15:41:56,840 - handlers.session_handlers - INFO - Message has video: False
2025-07-20 15:41:56,840 - handlers.session_handlers - INFO - Message has document: False
2025-07-20 15:41:56,841 - handlers.session_handlers - INFO - Message text: 'None'
2025-07-20 15:41:57,965 - services.task_service - INFO - Created new task submission: submission_1753006317.8e0250b6
2025-07-20 15:42:33,541 - services.referral_service - INFO - Referral reward processed: 6703107280 earned ₹1 for referring Mughal
2025-07-20 15:43:04,570 - services.user_service - INFO - Created new user: 5245239202 (Govind)
2025-07-20 15:43:05,742 - handlers.user_handlers - ERROR - Error sending new user notification: Forbidden: bot is not a member of the channel chat
2025-07-20 15:43:07,113 - handlers.user_handlers - INFO - Notification sent to referrer 7342085549 about new user Govind (ID: 5245239202): success
2025-07-20 15:43:25,550 - services.user_service - INFO - Created new user: 5985266430 (Akash)
2025-07-20 15:43:26,841 - handlers.user_handlers - ERROR - Error sending new user notification: Forbidden: bot is not a member of the channel chat
2025-07-20 15:43:28,656 - handlers.user_handlers - INFO - Notification sent to referrer ********** about new user Akash (ID: 5985266430): success
2025-07-20 15:43:38,087 - services.referral_service - INFO - Referral reward processed: 7342085549 earned ₹5 for referring Govind
2025-07-20 15:44:23,771 - handlers.admin_handlers - ERROR - Error in handle_withdrawal_settings: Message is not modified: specified new message content and reply markup are exactly the same as a current content and reply markup of the message
2025-07-20 15:44:25,286 - handlers.admin_handlers - ERROR - Error in handle_withdrawal_settings: Message is not modified: specified new message content and reply markup are exactly the same as a current content and reply markup of the message
2025-07-20 15:44:32,922 - services.withdrawal_service - INFO - Withdrawal record saved for user **********: withdrawal_1753006472.de2dcad2
2025-07-20 15:44:41,587 - services.user_service - INFO - Created new user: 7823926904 (Khatik)
2025-07-20 15:44:43,352 - handlers.user_handlers - ERROR - Error sending new user notification: Forbidden: bot is not a member of the channel chat
2025-07-20 15:44:45,126 - handlers.user_handlers - INFO - Notification sent to referrer ********** about new user Khatik (ID: 7823926904): success
2025-07-20 15:44:51,646 - handlers.admin_handlers - ERROR - Error in handle_manage_withdrawals: Message is not modified: specified new message content and reply markup are exactly the same as a current content and reply markup of the message
2025-07-20 15:44:52,007 - handlers.callback_handlers - ERROR - Error in callback handler for data 'manageWithdrawals': Query is too old and response timeout expired or query id is invalid
2025-07-20 15:44:52,398 - __main__ - ERROR - Error in callback handler: Query is too old and response timeout expired or query id is invalid
2025-07-20 15:44:57,839 - handlers.callback_handlers - ERROR - Error in smart navigation: Message to edit not found
2025-07-20 15:44:58,202 - handlers.callback_handlers - ERROR - Error in _handle_extra_rewards: Query is too old and response timeout expired or query id is invalid
2025-07-20 15:44:59,046 - handlers.callback_handlers - ERROR - Error in callback handler for data 'extraRewards': Query is too old and response timeout expired or query id is invalid
2025-07-20 15:44:59,454 - __main__ - ERROR - Error in callback handler: Query is too old and response timeout expired or query id is invalid
2025-07-20 15:45:00,160 - handlers.callback_handlers - ERROR - Error in callback handler for data 'viewPendingWithdrawals': Query is too old and response timeout expired or query id is invalid
2025-07-20 15:45:00,511 - __main__ - ERROR - Error in callback handler: Query is too old and response timeout expired or query id is invalid
2025-07-20 15:45:01,224 - handlers.callback_handlers - ERROR - Error in callback handler for data 'joined': Query is too old and response timeout expired or query id is invalid
2025-07-20 15:45:01,613 - __main__ - ERROR - Error in callback handler: Query is too old and response timeout expired or query id is invalid
2025-07-20 15:45:11,010 - handlers.admin_handlers - INFO - Loaded 7 pending withdrawals in 0.589s
2025-07-20 15:45:11,412 - handlers.admin_handlers - INFO - handle_view_pending_withdrawals completed in 0.992s
2025-07-20 15:45:14,425 - handlers.admin_handlers - INFO - Loaded 7 pending withdrawals in 0.468s
2025-07-20 15:45:14,824 - handlers.admin_handlers - ERROR - Error sending pending withdrawals message: Message is not modified: specified new message content and reply markup are exactly the same as a current content and reply markup of the message
2025-07-20 15:45:37,422 - services.withdrawal_service - INFO - Withdrawal rejected: User **********, Amount ₹100
2025-07-20 15:45:57,655 - handlers.user_handlers - INFO - Custom referral parameter used: from_channel -> User ID: **********
2025-07-20 15:46:01,884 - handlers.callback_handlers - ERROR - Error in _handle_check_subscription: Forbidden: bot was blocked by the user
2025-07-20 15:46:04,781 - handlers.admin_handlers - INFO - Loaded 6 pending withdrawals in 1.790s
2025-07-20 15:46:05,186 - handlers.admin_handlers - INFO - handle_view_pending_withdrawals completed in 2.195s
2025-07-20 15:46:10,671 - services.user_service - INFO - Created new user: 7909147576 (Mikey)
2025-07-20 15:46:12,265 - handlers.user_handlers - ERROR - Error sending new user notification: Forbidden: bot is not a member of the channel chat
2025-07-20 15:46:14,424 - handlers.user_handlers - INFO - Notification sent to referrer ********** about new user Mikey (ID: 7909147576): success
2025-07-20 15:46:21,236 - services.referral_service - INFO - Referral reward processed: ********** earned ₹4 for referring Khatik
2025-07-20 15:46:31,486 - services.withdrawal_service - INFO - Withdrawal rejected: User **********, Amount ₹100
2025-07-20 15:46:48,601 - services.user_service - INFO - Created new user: 7571675828 (Ashok)
2025-07-20 15:46:50,263 - handlers.user_handlers - ERROR - Error sending new user notification: Forbidden: bot is not a member of the channel chat
2025-07-20 15:46:51,734 - handlers.user_handlers - INFO - Notification sent to referrer 1833313460 about new user Ashok (ID: 7571675828): success
2025-07-20 15:47:09,928 - handlers.admin_handlers - INFO - Loaded 5 pending withdrawals in 0.652s
2025-07-20 15:47:10,324 - handlers.admin_handlers - INFO - handle_view_pending_withdrawals completed in 1.049s
2025-07-20 15:47:24,828 - services.withdrawal_service - INFO - Withdrawal rejected: User 7596351024, Amount ₹100
2025-07-20 15:47:56,539 - handlers.user_handlers - INFO - Custom referral parameter used: Gift -> User ID: **********
2025-07-20 15:48:03,893 - handlers.admin_handlers - INFO - Loaded 4 pending withdrawals in 0.645s
2025-07-20 15:48:04,273 - handlers.admin_handlers - INFO - handle_view_pending_withdrawals completed in 1.025s
2025-07-20 15:48:16,159 - handlers.user_handlers - INFO - Custom referral parameter used: from_channel -> User ID: **********
2025-07-20 15:48:20,145 - services.withdrawal_service - INFO - Withdrawal rejected: User **********, Amount ₹100
2025-07-20 15:48:43,791 - handlers.admin_handlers - INFO - Loaded 3 pending withdrawals in 0.845s
2025-07-20 15:48:44,234 - handlers.admin_handlers - INFO - handle_view_pending_withdrawals completed in 1.287s
2025-07-20 15:48:58,512 - services.withdrawal_service - INFO - Withdrawal rejected: User 6863525320, Amount ₹100
2025-07-20 15:48:59,189 - handlers.callback_handlers - ERROR - Error updating admin message: Can't parse entities: unsupported start tag "3" at byte offset 63
2025-07-20 15:49:29,291 - handlers.admin_handlers - INFO - Loaded 2 pending withdrawals in 0.555s
2025-07-20 15:49:29,721 - handlers.admin_handlers - INFO - handle_view_pending_withdrawals completed in 0.986s
2025-07-20 15:49:33,585 - handlers.admin_handlers - INFO - Loaded 2 pending withdrawals in 0.490s
2025-07-20 15:49:34,005 - handlers.admin_handlers - ERROR - Error sending pending withdrawals message: Message is not modified: specified new message content and reply markup are exactly the same as a current content and reply markup of the message
2025-07-20 15:49:36,593 - services.withdrawal_service - INFO - Withdrawal record saved for user 6863525320: withdrawal_1753006776.77cc7b0d
2025-07-20 15:49:37,294 - utils.helpers - ERROR - Failed to send message to **********: Can't parse entities: unsupported start tag "3" at byte offset 67
2025-07-20 15:50:10,502 - services.referral_service - INFO - Referral reward processed: 7996501648 earned ₹5 for referring Abhishek
2025-07-20 15:50:27,952 - services.withdrawal_service - INFO - Withdrawal rejected: User 6863525320, Amount ₹100
2025-07-20 15:50:28,155 - handlers.callback_handlers - ERROR - Error updating admin message: Can't parse entities: unsupported start tag "3" at byte offset 63
2025-07-20 15:51:22,292 - services.user_service - INFO - Created new user: 7640051499 (Babu)
2025-07-20 15:51:23,577 - handlers.user_handlers - ERROR - Error sending new user notification: Forbidden: bot is not a member of the channel chat
2025-07-20 15:51:24,993 - handlers.user_handlers - INFO - Notification sent to referrer ********** about new user Babu (ID: 7640051499): success
2025-07-20 15:51:57,834 - services.referral_service - INFO - Referral reward processed: ********** earned ₹4 for referring Babu
2025-07-20 15:53:03,071 - services.user_service - INFO - Created new user: 6934510169 (hassan)
2025-07-20 15:53:04,271 - handlers.user_handlers - ERROR - Error sending new user notification: Forbidden: bot is not a member of the channel chat
2025-07-20 15:53:05,491 - handlers.user_handlers - INFO - Notification sent to referrer ********** about new user hassan (ID: 6934510169): success
2025-07-20 15:53:26,117 - utils.helpers - WARNING - Sync is_admin() called from async context for user ********** - consider using is_admin_async()
2025-07-20 15:53:26,121 - services.admin_service - ERROR - Error getting admin settings for **********: Task <Task pending name='Task-267' coro=<_check_admin_in_new_loop() running at C:\Users\<USER>\Desktop\referbot\python\utils\helpers.py:528> cb=[_run_until_complete_cb() at C:\Python312\Lib\asyncio\base_events.py:182]> got Future <Future pending cb=[_chain_future.<locals>._call_check_cancel() at C:\Python312\Lib\asyncio\futures.py:391]> attached to a different loop
2025-07-20 15:53:49,982 - handlers.user_handlers - INFO - Custom referral parameter used: from_channel -> User ID: **********
2025-07-20 15:54:46,858 - services.user_service - INFO - Created new user: 830043095 (BO)
2025-07-20 15:54:49,201 - handlers.user_handlers - ERROR - Error sending new user notification: Forbidden: bot is not a member of the channel chat
2025-07-20 15:55:03,415 - handlers.user_handlers - INFO - Custom referral parameter used: from_channel -> User ID: **********
2025-07-20 15:56:03,551 - handlers.user_handlers - ERROR - Error showing main menu: Message to edit not found
2025-07-20 15:56:03,856 - handlers.callback_handlers - ERROR - Error in _handle_check_subscription: Message to edit not found
2025-07-20 15:56:39,766 - services.user_service - INFO - Created new user: 7687664990 (Raju)
2025-07-20 15:56:41,438 - handlers.user_handlers - ERROR - Error sending new user notification: Forbidden: bot is not a member of the channel chat
2025-07-20 15:56:43,328 - handlers.user_handlers - INFO - Notification sent to referrer ********** about new user Raju (ID: 7687664990): success
2025-07-20 15:57:03,001 - services.user_service - INFO - Created new user: 7509042477 (Wasif)
2025-07-20 15:57:05,660 - handlers.user_handlers - ERROR - Error sending new user notification: Forbidden: bot is not a member of the channel chat
2025-07-20 15:57:22,980 - services.user_service - INFO - Created new user: 8189848492 (Madam)
2025-07-20 15:57:26,105 - handlers.user_handlers - ERROR - Error sending new user notification: Forbidden: bot is not a member of the channel chat
2025-07-20 15:57:28,988 - handlers.user_handlers - INFO - Notification sent to referrer ********** about new user Madam (ID: 8189848492): success
2025-07-20 15:59:13,203 - services.user_service - INFO - Created new user: 6030416941 (Sriii)
2025-07-20 15:59:15,868 - handlers.user_handlers - ERROR - Error sending new user notification: Forbidden: bot is not a member of the channel chat
2025-07-20 15:59:18,776 - handlers.user_handlers - INFO - Notification sent to referrer 7238089165 about new user Sriii (ID: 6030416941): success
2025-07-20 15:59:29,056 - services.user_service - INFO - Created new user: 6498857535 (DHURAV BOT VERIFY)
2025-07-20 15:59:30,871 - handlers.user_handlers - ERROR - Error sending new user notification: Forbidden: bot is not a member of the channel chat
2025-07-20 15:59:32,508 - handlers.user_handlers - INFO - Notification sent to referrer ********** about new user DHURAV BOT VERIFY (ID: 6498857535): success
2025-07-20 15:59:39,488 - services.user_service - INFO - Created new user: 7323314365 (Shanert)
2025-07-20 15:59:41,012 - handlers.user_handlers - ERROR - Error sending new user notification: Forbidden: bot is not a member of the channel chat
2025-07-20 15:59:42,563 - handlers.user_handlers - INFO - Notification sent to referrer ********** about new user Shanert (ID: 7323314365): success
2025-07-20 16:00:19,483 - handlers.user_handlers - INFO - Custom referral parameter used: from_channel -> User ID: **********
2025-07-20 16:01:13,796 - handlers.callback_handlers - ERROR - Error in _handle_check_subscription: Query is too old and response timeout expired or query id is invalid
2025-07-20 16:01:14,188 - handlers.callback_handlers - ERROR - Error in callback handler for data 'checkSubscription': Query is too old and response timeout expired or query id is invalid
2025-07-20 16:01:14,547 - __main__ - ERROR - Error in callback handler: Query is too old and response timeout expired or query id is invalid
2025-07-20 16:01:20,549 - handlers.user_handlers - ERROR - Error showing main menu: Message to edit not found
2025-07-20 16:01:20,755 - handlers.callback_handlers - ERROR - Error in _handle_check_subscription: Message to edit not found
2025-07-20 16:01:21,202 - handlers.callback_handlers - ERROR - Error in callback handler for data 'checkSubscription': Query is too old and response timeout expired or query id is invalid
2025-07-20 16:01:21,722 - __main__ - ERROR - Error in callback handler: Query is too old and response timeout expired or query id is invalid
2025-07-20 16:01:36,515 - handlers.user_handlers - ERROR - Error showing main menu: Message to edit not found
2025-07-20 16:01:36,713 - handlers.callback_handlers - ERROR - Error in _handle_check_subscription: Message to edit not found
2025-07-20 16:01:41,951 - services.referral_service - INFO - Referral reward processed: ********** earned ₹4 for referring Sandesh
2025-07-20 16:01:59,472 - services.user_service - INFO - Created new user: 6498233243 (NA)
2025-07-20 16:02:00,819 - handlers.user_handlers - ERROR - Error sending new user notification: Forbidden: bot is not a member of the channel chat
2025-07-20 16:02:02,225 - handlers.user_handlers - INFO - Notification sent to referrer 7397609890 about new user NA (ID: 6498233243): success
2025-07-20 16:02:12,892 - services.user_service - INFO - Created new user: 7716465540 (Shyam)
2025-07-20 16:02:14,246 - handlers.user_handlers - ERROR - Error sending new user notification: Forbidden: bot is not a member of the channel chat
2025-07-20 16:02:15,678 - handlers.user_handlers - INFO - Notification sent to referrer 5836462366 about new user Shyam (ID: 7716465540): success
2025-07-20 16:02:59,257 - services.referral_service - INFO - Referral reward processed: 5836462366 earned ₹4 for referring Shyam
2025-07-20 16:03:03,086 - services.withdrawal_service - INFO - Withdrawal record saved for user **********: withdrawal_1753007583.ab2e2966
2025-07-20 16:03:07,177 - handlers.callback_handlers - INFO - Message contains media, sending new text message instead of editing
2025-07-20 16:03:09,791 - handlers.user_handlers - INFO - Custom referral parameter used: from_channel -> User ID: **********
2025-07-20 16:03:30,441 - handlers.user_handlers - INFO - Custom referral parameter used: from_channel -> User ID: **********
2025-07-20 16:03:32,924 - handlers.session_handlers - INFO - === SESSION MESSAGE HANDLER for user **********, step: submit_task_screenshot ===
2025-07-20 16:03:32,924 - handlers.session_handlers - INFO - Session data: {'_id': ObjectId('687cc5a57104627ee2034915'), 'user_id': **********, 'created_at': 1753007587, 'data': {'task_id': 'task_1752995334.fd08d118'}, 'step': 'submit_task_screenshot', 'updated_at': 1753007587}
2025-07-20 16:03:32,924 - handlers.session_handlers - INFO - Message has text: False
2025-07-20 16:03:32,924 - handlers.session_handlers - INFO - Message has photo: True
2025-07-20 16:03:32,924 - handlers.session_handlers - INFO - Message has video: False
2025-07-20 16:03:32,925 - handlers.session_handlers - INFO - Message has document: False
2025-07-20 16:03:32,925 - handlers.session_handlers - INFO - Message text: 'None'
2025-07-20 16:03:34,920 - services.task_service - INFO - Created new task submission: submission_1753007614.40571a9c
2025-07-20 16:03:36,356 - handlers.user_handlers - INFO - Custom referral parameter used: Gift -> User ID: **********
2025-07-20 16:04:14,501 - handlers.callback_handlers - INFO - Message contains media, sending new text message instead of editing
2025-07-20 16:04:19,608 - __main__ - INFO - Received signal 2, initiating shutdown...
2025-07-20 16:04:19,608 - __main__ - INFO - Shutting down bot...
2025-07-20 16:04:20,216 - apscheduler.scheduler - INFO - Scheduler has been shut down
2025-07-20 16:04:20,262 - config.database - INFO - Disconnected from MongoDB
2025-07-20 16:04:20,262 - __main__ - INFO - Bot shutdown completed
2025-07-20 16:04:30,295 - __main__ - INFO - Connecting to MongoDB...
2025-07-20 16:04:43,409 - config.database - INFO - Successfully connected to MongoDB: referral_bot
2025-07-20 16:04:45,000 - config.database - INFO - Database collections and indexes initialized successfully
2025-07-20 16:04:45,001 - __main__ - INFO - Initializing Telegram bot...
2025-07-20 16:04:45,537 - __main__ - INFO - All handlers registered successfully
2025-07-20 16:04:46,336 - services.admin_service - INFO - Initialized admin settings for 4 admins
2025-07-20 16:04:46,369 - __main__ - INFO - Bot info updated: @InstantoPayBot (InstantoPay)
2025-07-20 16:04:46,369 - __main__ - INFO - Admin settings and level rewards system initialized
2025-07-20 16:04:46,369 - __main__ - INFO - Bot initialization completed successfully
2025-07-20 16:04:46,369 - __main__ - INFO - Starting bot polling...
2025-07-20 16:04:46,611 - apscheduler.scheduler - INFO - Scheduler started
2025-07-20 16:04:46,816 - __main__ - INFO - Bot is now running and polling for updates...
2025-07-20 16:04:47,733 - handlers.callback_handlers - ERROR - Error in callback handler for data 'extraRewards': Query is too old and response timeout expired or query id is invalid
2025-07-20 16:04:48,088 - __main__ - ERROR - Error in callback handler: Query is too old and response timeout expired or query id is invalid
2025-07-20 16:05:10,585 - handlers.callback_handlers - ERROR - Error in smart navigation: Message is not modified: specified new message content and reply markup are exactly the same as a current content and reply markup of the message
2025-07-20 16:05:22,778 - utils.helpers - WARNING - Sync is_admin() called from async context for user ********** - consider using is_admin_async()
2025-07-20 16:05:22,782 - services.admin_service - ERROR - Error getting admin settings for **********: Task <Task pending name='Task-9' coro=<_check_admin_in_new_loop() running at C:\Users\<USER>\Desktop\referbot\python\utils\helpers.py:528> cb=[_run_until_complete_cb() at C:\Python312\Lib\asyncio\base_events.py:182]> got Future <Future pending cb=[_chain_future.<locals>._call_check_cancel() at C:\Python312\Lib\asyncio\futures.py:391]> attached to a different loop
2025-07-20 16:07:31,306 - services.user_service - INFO - Created new user: 1378568268 (Sandesh🍅)
2025-07-20 16:07:32,516 - handlers.user_handlers - ERROR - Error sending new user notification: Forbidden: bot is not a member of the channel chat
2025-07-20 16:07:33,878 - handlers.user_handlers - INFO - Notification sent to referrer ********** about new user Sandesh🍅 (ID: 1378568268): success
2025-07-20 16:08:13,452 - handlers.user_handlers - ERROR - Error showing main menu: Message to edit not found
2025-07-20 16:08:13,771 - handlers.callback_handlers - ERROR - Error in _handle_check_subscription: Message to edit not found
2025-07-20 16:08:42,046 - handlers.user_handlers - ERROR - Error showing main menu: Message to edit not found
2025-07-20 16:08:42,346 - handlers.callback_handlers - ERROR - Error in _handle_check_subscription: Message to edit not found
2025-07-20 16:08:59,720 - services.referral_service - INFO - Referral reward processed: ********** earned ₹3 for referring Sandesh🍅
2025-07-20 16:09:41,338 - handlers.user_handlers - ERROR - Error showing main menu: Message to edit not found
2025-07-20 16:09:41,539 - handlers.callback_handlers - ERROR - Error in _handle_check_subscription: Message to edit not found
2025-07-20 16:09:46,149 - services.referral_service - INFO - Referral reward processed: ********** earned ₹1 for referring Varun
2025-07-20 16:11:57,572 - handlers.callback_handlers - INFO - Message contains media, sending new text message instead of editing
2025-07-20 16:11:59,457 - services.user_service - INFO - Created new user: 7288689301 (🍅)
2025-07-20 16:12:00,630 - handlers.user_handlers - ERROR - Error sending new user notification: Forbidden: bot is not a member of the channel chat
2025-07-20 16:12:02,057 - handlers.user_handlers - INFO - Notification sent to referrer ********** about new user 🍅 (ID: 7288689301): success
2025-07-20 16:12:49,758 - handlers.user_handlers - INFO - Custom referral parameter used: from_channel -> User ID: **********
2025-07-20 16:12:56,168 - handlers.user_handlers - ERROR - Error showing main menu: Message to edit not found
2025-07-20 16:12:56,443 - handlers.callback_handlers - ERROR - Error in _handle_check_subscription: Message to edit not found
2025-07-20 16:13:01,507 - services.referral_service - INFO - Referral reward processed: ********** earned ₹2 for referring 🍅
2025-07-20 16:13:29,617 - services.withdrawal_service - INFO - Withdrawal record saved for user 7808018851: withdrawal_1753008209.3de4c29e
2025-07-20 16:13:47,756 - handlers.user_handlers - INFO - Custom referral parameter used: Gift -> User ID: **********
2025-07-20 16:14:08,221 - handlers.callback_handlers - ERROR - Error in _handle_custom_referral_link: Message is not modified: specified new message content and reply markup are exactly the same as a current content and reply markup of the message
2025-07-20 16:14:36,465 - handlers.session_handlers - INFO - === SESSION MESSAGE HANDLER for user **********, step: set_account_name ===
2025-07-20 16:14:36,465 - handlers.session_handlers - INFO - Session data: {'_id': ObjectId('687cc88e7104627ee2034920'), 'user_id': **********, 'created_at': **********, 'data': {}, 'step': 'set_account_name', 'updated_at': **********}
2025-07-20 16:14:36,465 - handlers.session_handlers - INFO - Message has text: True
2025-07-20 16:14:36,467 - handlers.session_handlers - INFO - Message has photo: False
2025-07-20 16:14:36,467 - handlers.session_handlers - INFO - Message has video: False
2025-07-20 16:14:36,467 - handlers.session_handlers - INFO - Message has document: False
2025-07-20 16:14:36,467 - handlers.session_handlers - INFO - Message text: 'Darshana'
2025-07-20 16:14:55,926 - handlers.session_handlers - INFO - === SESSION MESSAGE HANDLER for user **********, step: set_account_ifsc ===
2025-07-20 16:14:55,926 - handlers.session_handlers - INFO - Session data: {'_id': ObjectId('687cc8a17104627ee2034921'), 'user_id': **********, 'created_at': **********, 'data': {}, 'step': 'set_account_ifsc', 'updated_at': **********}
2025-07-20 16:14:55,927 - handlers.session_handlers - INFO - Message has text: True
2025-07-20 16:14:55,927 - handlers.session_handlers - INFO - Message has photo: False
2025-07-20 16:14:55,927 - handlers.session_handlers - INFO - Message has video: False
2025-07-20 16:14:55,927 - handlers.session_handlers - INFO - Message has document: False
2025-07-20 16:14:55,927 - handlers.session_handlers - INFO - Message text: 'BKID0006109'
2025-07-20 16:14:56,669 - utils.helpers - INFO - Cached bank details for IFSC: BKID0006109
2025-07-20 16:14:57,838 - handlers.session_handlers - INFO - === SESSION MESSAGE HANDLER for user **********, step: redeem_gift_code ===
2025-07-20 16:14:57,839 - handlers.session_handlers - INFO - Session data: {'_id': ObjectId('687cc8027104627ee203491e'), 'user_id': **********, 'created_at': **********, 'data': {}, 'step': 'redeem_gift_code', 'updated_at': **********}
2025-07-20 16:14:57,839 - handlers.session_handlers - INFO - Message has text: True
2025-07-20 16:14:57,839 - handlers.session_handlers - INFO - Message has photo: False
2025-07-20 16:14:57,839 - handlers.session_handlers - INFO - Message has video: False
2025-07-20 16:14:57,839 - handlers.session_handlers - INFO - Message has document: False
2025-07-20 16:14:57,840 - handlers.session_handlers - INFO - Message text: 'B7XKQ9E2T6LZ1FVC34YM'
2025-07-20 16:15:00,441 - handlers.session_handlers - INFO - === SESSION MESSAGE HANDLER for user **********, step: set_mobile_number ===
2025-07-20 16:15:00,442 - handlers.session_handlers - INFO - Session data: {'_id': ObjectId('687cc8767104627ee203491f'), 'user_id': **********, 'created_at': 1753008280, 'data': {}, 'step': 'set_mobile_number', 'updated_at': 1753008280}
2025-07-20 16:15:00,442 - handlers.session_handlers - INFO - Message has text: True
2025-07-20 16:15:00,442 - handlers.session_handlers - INFO - Message has photo: False
2025-07-20 16:15:00,442 - handlers.session_handlers - INFO - Message has video: False
2025-07-20 16:15:00,442 - handlers.session_handlers - INFO - Message has document: False
2025-07-20 16:15:00,442 - handlers.session_handlers - INFO - Message text: '+************'
2025-07-20 16:15:00,849 - handlers.session_handlers - INFO - Attempting to send OTP to mobile number: +************
2025-07-20 16:15:00,894 - services.withdrawal_service - INFO - Using OTP API key from environment variable
2025-07-20 16:15:00,894 - services.withdrawal_service - INFO - Converted +************ to API format: 8660410758
2025-07-20 16:15:00,894 - services.withdrawal_service - INFO - Sending OTP to +************ (API format: 8660410758) using URL: https://sms.renflair.in/V1.php
2025-07-20 16:15:03,189 - services.withdrawal_service - INFO - OTP API response for +************: Status 200, Content: {"return":true,"request_id":"OMFrlusPCAYKcvz","message":"SMS sent successfully.","status":"SUCCESS"}
2025-07-20 16:15:03,189 - services.withdrawal_service - INFO - OTP API analysis for +************ (API format: 8660410758): Status=200, HasFailure=False, HasSuccess=True, ResponseLength=100
2025-07-20 16:15:03,189 - services.withdrawal_service - INFO - OTP API call successful for +************. OTP: 7557
2025-07-20 16:15:03,189 - services.withdrawal_service - INFO - Note: SMS delivery may take 1-5 minutes depending on carrier
2025-07-20 16:15:03,638 - handlers.session_handlers - INFO - OTP API call successful for +************, user ********** moved to verification step
2025-07-20 16:15:18,903 - handlers.session_handlers - INFO - === SESSION MESSAGE HANDLER for user **********, step: set_account_email ===
2025-07-20 16:15:18,903 - handlers.session_handlers - INFO - Session data: {'_id': ObjectId('687cc8b77104627ee2034922'), 'user_id': **********, 'created_at': **********, 'data': {}, 'step': 'set_account_email', 'updated_at': **********}
2025-07-20 16:15:18,904 - handlers.session_handlers - INFO - Message has text: True
2025-07-20 16:15:18,904 - handlers.session_handlers - INFO - Message has photo: False
2025-07-20 16:15:18,904 - handlers.session_handlers - INFO - Message has video: False
2025-07-20 16:15:18,904 - handlers.session_handlers - INFO - Message has document: False
2025-07-20 16:15:18,904 - handlers.session_handlers - INFO - Message text: '<EMAIL>'
2025-07-20 16:15:22,397 - handlers.session_handlers - INFO - === SESSION MESSAGE HANDLER for user **********, step: verify_otp ===
2025-07-20 16:15:22,397 - handlers.session_handlers - INFO - Session data: {'_id': ObjectId('687cc8767104627ee203491f'), 'user_id': **********, 'created_at': **********, 'data': {'mobile_number': '+************', 'otp': '7557', 'otp_timestamp': **********}, 'step': 'verify_otp', 'updated_at': **********}
2025-07-20 16:15:22,399 - handlers.session_handlers - INFO - Message has text: True
2025-07-20 16:15:22,399 - handlers.session_handlers - INFO - Message has photo: False
2025-07-20 16:15:22,399 - handlers.session_handlers - INFO - Message has video: False
2025-07-20 16:15:22,399 - handlers.session_handlers - INFO - Message has document: False
2025-07-20 16:15:22,399 - handlers.session_handlers - INFO - Message text: '7557'
2025-07-20 16:15:42,819 - services.user_service - INFO - Created new user: ********** (Suhaib)
2025-07-20 16:15:44,057 - handlers.user_handlers - ERROR - Error sending new user notification: Forbidden: bot is not a member of the channel chat
2025-07-20 16:15:45,482 - handlers.user_handlers - INFO - Notification sent to referrer ********** about new user Suhaib (ID: **********): success
2025-07-20 16:15:48,497 - handlers.session_handlers - INFO - === SESSION MESSAGE HANDLER for user **********, step: set_account_number ===
2025-07-20 16:15:48,498 - handlers.session_handlers - INFO - Session data: {'_id': ObjectId('687cc8c77104627ee2034923'), 'user_id': **********, 'created_at': **********, 'data': {}, 'step': 'set_account_number', 'updated_at': **********}
2025-07-20 16:15:48,498 - handlers.session_handlers - INFO - Message has text: True
2025-07-20 16:15:48,498 - handlers.session_handlers - INFO - Message has photo: False
2025-07-20 16:15:48,498 - handlers.session_handlers - INFO - Message has video: False
2025-07-20 16:15:48,498 - handlers.session_handlers - INFO - Message has document: False
2025-07-20 16:15:48,498 - handlers.session_handlers - INFO - Message text: '***************'
2025-07-20 16:16:03,504 - handlers.callback_handlers - ERROR - Error in smart navigation: Message is not modified: specified new message content and reply markup are exactly the same as a current content and reply markup of the message
2025-07-20 16:16:08,614 - handlers.user_handlers - INFO - Custom referral parameter used: from_channel -> User ID: **********
2025-07-20 16:16:14,629 - services.referral_service - INFO - Referral reward processed: ********** earned ₹3 for referring Suhaib
2025-07-20 16:16:23,005 - handlers.session_handlers - INFO - === SESSION MESSAGE HANDLER for user **********, step: set_account_name ===
2025-07-20 16:16:23,007 - handlers.session_handlers - INFO - Session data: {'_id': ObjectId('687cc8fb7104627ee2034924'), 'user_id': **********, 'created_at': **********, 'data': {}, 'step': 'set_account_name', 'updated_at': **********}
2025-07-20 16:16:23,007 - handlers.session_handlers - INFO - Message has text: True
2025-07-20 16:16:23,007 - handlers.session_handlers - INFO - Message has photo: False
2025-07-20 16:16:23,007 - handlers.session_handlers - INFO - Message has video: False
2025-07-20 16:16:23,007 - handlers.session_handlers - INFO - Message has document: False
2025-07-20 16:16:23,008 - handlers.session_handlers - INFO - Message text: 'VINEETA'
2025-07-20 16:16:44,004 - handlers.session_handlers - INFO - === SESSION MESSAGE HANDLER for user **********, step: set_account_ifsc ===
2025-07-20 16:16:44,005 - handlers.session_handlers - INFO - Session data: {'_id': ObjectId('687cc90a7104627ee2034925'), 'user_id': **********, 'created_at': **********, 'data': {}, 'step': 'set_account_ifsc', 'updated_at': **********}
2025-07-20 16:16:44,005 - handlers.session_handlers - INFO - Message has text: True
2025-07-20 16:16:44,005 - handlers.session_handlers - INFO - Message has photo: False
2025-07-20 16:16:44,005 - handlers.session_handlers - INFO - Message has video: False
2025-07-20 16:16:44,006 - handlers.session_handlers - INFO - Message has document: False
2025-07-20 16:16:44,006 - handlers.session_handlers - INFO - Message text: 'KKBK0004604'
2025-07-20 16:16:44,622 - utils.helpers - INFO - Cached bank details for IFSC: KKBK0004604
2025-07-20 16:16:53,280 - handlers.callback_handlers - ERROR - Error in _handle_reenter_ifsc: Message is not modified: specified new message content and reply markup are exactly the same as a current content and reply markup of the message
2025-07-20 16:17:06,448 - services.user_service - INFO - Created new user: ********** (Suhaib)
2025-07-20 16:17:07,679 - handlers.user_handlers - ERROR - Error sending new user notification: Forbidden: bot is not a member of the channel chat
2025-07-20 16:17:09,019 - handlers.user_handlers - INFO - Notification sent to referrer ********** about new user Suhaib (ID: **********): success
2025-07-20 16:17:15,109 - handlers.session_handlers - INFO - === SESSION MESSAGE HANDLER for user **********, step: set_account_ifsc ===
2025-07-20 16:17:15,109 - handlers.session_handlers - INFO - Session data: {'_id': ObjectId('687cc90a7104627ee2034925'), 'user_id': **********, 'created_at': **********, 'data': {}, 'step': 'set_account_ifsc', 'updated_at': **********}
2025-07-20 16:17:15,110 - handlers.session_handlers - INFO - Message has text: True
2025-07-20 16:17:15,110 - handlers.session_handlers - INFO - Message has photo: False
2025-07-20 16:17:15,110 - handlers.session_handlers - INFO - Message has video: False
2025-07-20 16:17:15,110 - handlers.session_handlers - INFO - Message has document: False
2025-07-20 16:17:15,110 - handlers.session_handlers - INFO - Message text: 'KKBK0004608'
2025-07-20 16:17:15,759 - utils.helpers - INFO - Cached bank details for IFSC: KKBK0004608
2025-07-20 16:17:24,933 - handlers.session_handlers - INFO - === SESSION MESSAGE HANDLER for user **********, step: set_account_name ===
2025-07-20 16:17:24,933 - handlers.session_handlers - INFO - Session data: {'_id': ObjectId('687cc3ae7104627ee2034911'), 'user_id': **********, 'created_at': **********, 'data': {}, 'step': 'set_account_name', 'updated_at': **********}
2025-07-20 16:17:24,933 - handlers.session_handlers - INFO - Message has text: True
2025-07-20 16:17:24,933 - handlers.session_handlers - INFO - Message has photo: False
2025-07-20 16:17:24,934 - handlers.session_handlers - INFO - Message has video: False
2025-07-20 16:17:24,934 - handlers.session_handlers - INFO - Message has document: False
2025-07-20 16:17:24,934 - handlers.session_handlers - INFO - Message text: 'Refer not rec'
2025-07-20 16:17:35,953 - services.referral_service - INFO - Referral reward processed: ********** earned ₹5 for referring Suhaib
2025-07-20 16:17:42,062 - handlers.session_handlers - INFO - === SESSION MESSAGE HANDLER for user **********, step: set_account_email ===
2025-07-20 16:17:42,062 - handlers.session_handlers - INFO - Session data: {'_id': ObjectId('687cc9437104627ee2034926'), 'user_id': **********, 'created_at': **********, 'data': {}, 'step': 'set_account_email', 'updated_at': **********}
2025-07-20 16:17:42,063 - handlers.session_handlers - INFO - Message has text: True
2025-07-20 16:17:42,063 - handlers.session_handlers - INFO - Message has photo: False
2025-07-20 16:17:42,063 - handlers.session_handlers - INFO - Message has video: False
2025-07-20 16:17:42,063 - handlers.session_handlers - INFO - Message has document: False
2025-07-20 16:17:42,064 - handlers.session_handlers - INFO - Message text: '<EMAIL>'
2025-07-20 16:17:44,252 - utils.helpers - WARNING - Sync is_admin() called from async context for user ********** - consider using is_admin_async()
2025-07-20 16:17:44,254 - services.admin_service - ERROR - Error getting admin settings for **********: Task <Task pending name='Task-60' coro=<_check_admin_in_new_loop() running at C:\Users\<USER>\Desktop\referbot\python\utils\helpers.py:528> cb=[_run_until_complete_cb() at C:\Python312\Lib\asyncio\base_events.py:182]> got Future <Future pending cb=[_chain_future.<locals>._call_check_cancel() at C:\Python312\Lib\asyncio\futures.py:391]> attached to a different loop
2025-07-20 16:17:47,769 - utils.helpers - WARNING - Sync is_admin() called from async context for user ********** - consider using is_admin_async()
2025-07-20 16:17:47,772 - services.admin_service - ERROR - Error getting admin settings for **********: Task <Task pending name='Task-63' coro=<_check_admin_in_new_loop() running at C:\Users\<USER>\Desktop\referbot\python\utils\helpers.py:528> cb=[_run_until_complete_cb() at C:\Python312\Lib\asyncio\base_events.py:182]> got Future <Future pending cb=[_chain_future.<locals>._call_check_cancel() at C:\Python312\Lib\asyncio\futures.py:391]> attached to a different loop
2025-07-20 16:17:53,405 - utils.helpers - WARNING - Sync is_admin() called from async context for user ********** - consider using is_admin_async()
2025-07-20 16:17:53,408 - services.admin_service - ERROR - Error getting admin settings for **********: Task <Task pending name='Task-66' coro=<_check_admin_in_new_loop() running at C:\Users\<USER>\Desktop\referbot\python\utils\helpers.py:528> cb=[_run_until_complete_cb() at C:\Python312\Lib\asyncio\base_events.py:182]> got Future <Future pending cb=[_chain_future.<locals>._call_check_cancel() at C:\Python312\Lib\asyncio\futures.py:391]> attached to a different loop
2025-07-20 16:17:55,476 - handlers.session_handlers - INFO - === SESSION MESSAGE HANDLER for user **********, step: set_account_number ===
2025-07-20 16:17:55,477 - handlers.session_handlers - INFO - Session data: {'_id': ObjectId('687cc9567104627ee2034927'), 'user_id': **********, 'created_at': **********, 'data': {}, 'step': 'set_account_number', 'updated_at': **********}
2025-07-20 16:17:55,477 - handlers.session_handlers - INFO - Message has text: True
2025-07-20 16:17:55,477 - handlers.session_handlers - INFO - Message has photo: False
2025-07-20 16:17:55,477 - handlers.session_handlers - INFO - Message has video: False
2025-07-20 16:17:55,478 - handlers.session_handlers - INFO - Message has document: False
2025-07-20 16:17:55,478 - handlers.session_handlers - INFO - Message text: '**********'
2025-07-20 16:18:20,906 - utils.helpers - WARNING - Sync is_admin() called from async context for user ********** - consider using is_admin_async()
2025-07-20 16:18:20,907 - services.admin_service - ERROR - Error getting admin settings for **********: Task <Task pending name='Task-70' coro=<_check_admin_in_new_loop() running at C:\Users\<USER>\Desktop\referbot\python\utils\helpers.py:528> cb=[_run_until_complete_cb() at C:\Python312\Lib\asyncio\base_events.py:182]> got Future <Future pending cb=[_chain_future.<locals>._call_check_cancel() at C:\Python312\Lib\asyncio\futures.py:391]> attached to a different loop
2025-07-20 16:18:21,275 - handlers.callback_handlers - ERROR - Error in _handle_withdrawal_approval_callback: Query is too old and response timeout expired or query id is invalid
2025-07-20 16:18:21,636 - handlers.callback_handlers - ERROR - Error handling dynamic callback 'reject_withdrawal_**********': Query is too old and response timeout expired or query id is invalid
2025-07-20 16:18:21,995 - handlers.callback_handlers - ERROR - Error in callback handler for data 'reject_withdrawal_**********': Query is too old and response timeout expired or query id is invalid
2025-07-20 16:18:22,362 - __main__ - ERROR - Error in callback handler: Query is too old and response timeout expired or query id is invalid
2025-07-20 16:18:24,366 - utils.helpers - WARNING - Sync is_admin() called from async context for user ********** - consider using is_admin_async()
2025-07-20 16:18:24,369 - services.admin_service - ERROR - Error getting admin settings for **********: Task <Task pending name='Task-73' coro=<_check_admin_in_new_loop() running at C:\Users\<USER>\Desktop\referbot\python\utils\helpers.py:528> cb=[_run_until_complete_cb() at C:\Python312\Lib\asyncio\base_events.py:182]> got Future <Future pending cb=[_chain_future.<locals>._call_check_cancel() at C:\Python312\Lib\asyncio\futures.py:391]> attached to a different loop
2025-07-20 16:18:24,729 - handlers.callback_handlers - ERROR - Error in _handle_withdrawal_approval_callback: Query is too old and response timeout expired or query id is invalid
2025-07-20 16:18:25,087 - handlers.callback_handlers - ERROR - Error handling dynamic callback 'reject_withdrawal_**********': Query is too old and response timeout expired or query id is invalid
2025-07-20 16:18:25,538 - handlers.callback_handlers - ERROR - Error in callback handler for data 'reject_withdrawal_**********': Query is too old and response timeout expired or query id is invalid
2025-07-20 16:18:25,900 - __main__ - ERROR - Error in callback handler: Query is too old and response timeout expired or query id is invalid
2025-07-20 16:18:26,621 - handlers.callback_handlers - ERROR - Error in callback handler for data 'approveTask_submission_1753006317.8e0250b6': Query is too old and response timeout expired or query id is invalid
2025-07-20 16:18:26,999 - __main__ - ERROR - Error in callback handler: Query is too old and response timeout expired or query id is invalid
2025-07-20 16:18:27,736 - handlers.callback_handlers - ERROR - Error in callback handler for data 'approveTask_submission_1753006064.b910257d': Query is too old and response timeout expired or query id is invalid
2025-07-20 16:18:28,137 - __main__ - ERROR - Error in callback handler: Query is too old and response timeout expired or query id is invalid
2025-07-20 16:18:33,693 - utils.helpers - WARNING - Sync is_admin() called from async context for user ********** - consider using is_admin_async()
2025-07-20 16:18:33,696 - services.admin_service - ERROR - Error getting admin settings for **********: Task <Task pending name='Task-76' coro=<_check_admin_in_new_loop() running at C:\Users\<USER>\Desktop\referbot\python\utils\helpers.py:528> cb=[_run_until_complete_cb() at C:\Python312\Lib\asyncio\base_events.py:182]> got Future <Future pending cb=[_chain_future.<locals>._call_check_cancel() at C:\Python312\Lib\asyncio\futures.py:391]> attached to a different loop
2025-07-20 16:18:36,856 - handlers.callback_handlers - INFO - Message contains media, sending new text message instead of editing
2025-07-20 16:18:40,491 - handlers.callback_handlers - ERROR - Error in smart navigation: Message is not modified: specified new message content and reply markup are exactly the same as a current content and reply markup of the message
2025-07-20 16:18:42,410 - handlers.user_handlers - INFO - Custom referral parameter used: Gift -> User ID: **********
2025-07-20 16:18:44,292 - utils.helpers - WARNING - Sync is_admin() called from async context for user ********** - consider using is_admin_async()
2025-07-20 16:18:44,293 - services.admin_service - ERROR - Error getting admin settings for **********: Task <Task pending name='Task-79' coro=<_check_admin_in_new_loop() running at C:\Users\<USER>\Desktop\referbot\python\utils\helpers.py:528> cb=[_run_until_complete_cb() at C:\Python312\Lib\asyncio\base_events.py:182]> got Future <Future pending cb=[_chain_future.<locals>._call_check_cancel() at C:\Python312\Lib\asyncio\futures.py:391]> attached to a different loop
2025-07-20 16:18:51,487 - handlers.session_handlers - INFO - === SESSION MESSAGE HANDLER for user **********, step: set_mobile_number ===
2025-07-20 16:18:51,488 - handlers.session_handlers - INFO - Session data: {'_id': ObjectId('687cc9897104627ee2034929'), 'user_id': **********, 'created_at': 1753008522, 'data': {}, 'step': 'set_mobile_number', 'updated_at': 1753008522}
2025-07-20 16:18:51,488 - handlers.session_handlers - INFO - Message has text: True
2025-07-20 16:18:51,489 - handlers.session_handlers - INFO - Message has photo: False
2025-07-20 16:18:51,489 - handlers.session_handlers - INFO - Message has video: False
2025-07-20 16:18:51,489 - handlers.session_handlers - INFO - Message has document: False
2025-07-20 16:18:51,489 - handlers.session_handlers - INFO - Message text: '+919958256350'
2025-07-20 16:18:51,904 - handlers.session_handlers - INFO - Attempting to send OTP to mobile number: +919958256350
2025-07-20 16:18:51,950 - services.withdrawal_service - INFO - Using OTP API key from environment variable
2025-07-20 16:18:51,950 - services.withdrawal_service - INFO - Converted +919958256350 to API format: 9958256350
2025-07-20 16:18:51,951 - services.withdrawal_service - INFO - Sending OTP to +919958256350 (API format: 9958256350) using URL: https://sms.renflair.in/V1.php
2025-07-20 16:18:53,513 - services.withdrawal_service - INFO - OTP API response for +919958256350: Status 200, Content: {"return":true,"request_id":"J03LXOZouHwadxY","message":"SMS sent successfully.","status":"SUCCESS"}
2025-07-20 16:18:53,513 - services.withdrawal_service - INFO - OTP API analysis for +919958256350 (API format: 9958256350): Status=200, HasFailure=False, HasSuccess=True, ResponseLength=100
2025-07-20 16:18:53,514 - services.withdrawal_service - INFO - OTP API call successful for +919958256350. OTP: 4918
2025-07-20 16:18:53,514 - services.withdrawal_service - INFO - Note: SMS delivery may take 1-5 minutes depending on carrier
2025-07-20 16:18:53,949 - handlers.session_handlers - INFO - OTP API call successful for +919958256350, user ********** moved to verification step
2025-07-20 16:19:29,636 - handlers.session_handlers - INFO - === SESSION MESSAGE HANDLER for user **********, step: verify_otp ===
2025-07-20 16:19:29,636 - handlers.session_handlers - INFO - Session data: {'_id': ObjectId('687cc9897104627ee2034929'), 'user_id': **********, 'created_at': 1753008533, 'data': {'mobile_number': '+919958256350', 'otp': '4918', 'otp_timestamp': 1753008533}, 'step': 'verify_otp', 'updated_at': 1753008533}
2025-07-20 16:19:29,637 - handlers.session_handlers - INFO - Message has text: True
2025-07-20 16:19:29,637 - handlers.session_handlers - INFO - Message has photo: False
2025-07-20 16:19:29,637 - handlers.session_handlers - INFO - Message has video: False
2025-07-20 16:19:29,637 - handlers.session_handlers - INFO - Message has document: False
2025-07-20 16:19:29,637 - handlers.session_handlers - INFO - Message text: '4918'
2025-07-20 16:19:54,054 - handlers.session_handlers - INFO - === SESSION MESSAGE HANDLER for user 7288689301, step: submit_task_screenshot ===
2025-07-20 16:19:54,055 - handlers.session_handlers - INFO - Session data: {'_id': ObjectId('687cc9857104627ee2034928'), 'user_id': 7288689301, 'created_at': 1753008517, 'data': {'task_id': 'task_1752995334.fd08d118'}, 'step': 'submit_task_screenshot', 'updated_at': 1753008517}
2025-07-20 16:19:54,055 - handlers.session_handlers - INFO - Message has text: False
2025-07-20 16:19:54,055 - handlers.session_handlers - INFO - Message has photo: True
2025-07-20 16:19:54,056 - handlers.session_handlers - INFO - Message has video: False
2025-07-20 16:19:54,056 - handlers.session_handlers - INFO - Message has document: False
2025-07-20 16:19:54,056 - handlers.session_handlers - INFO - Message text: 'None'
2025-07-20 16:19:55,803 - services.task_service - INFO - Created new task submission: submission_1753008595.ee869dcd
2025-07-20 16:20:02,260 - services.user_service - INFO - Created new user: 7648854550 (Pokemon Go)
2025-07-20 16:20:03,548 - handlers.user_handlers - ERROR - Error sending new user notification: Forbidden: bot is not a member of the channel chat
2025-07-20 16:20:05,073 - handlers.user_handlers - INFO - Notification sent to referrer ********** about new user Pokemon Go (ID: 7648854550): success
2025-07-20 16:20:16,322 - services.withdrawal_service - INFO - Withdrawal record saved for user **********: withdrawal_1753008616.bfd97f1e
2025-07-20 16:20:28,264 - services.referral_service - INFO - Referral reward processed: ********** earned ₹2 for referring Pokemon Go
2025-07-20 16:20:42,449 - handlers.callback_handlers - INFO - Message contains media, sending new text message instead of editing
2025-07-20 16:20:56,707 - handlers.session_handlers - INFO - === SESSION MESSAGE HANDLER for user **********, step: submit_task_screenshot ===
2025-07-20 16:20:56,708 - handlers.session_handlers - INFO - Session data: {'_id': ObjectId('687cca027104627ee203492d'), 'user_id': **********, 'created_at': 1753008643, 'data': {'task_id': 'task_1752995334.fd08d118'}, 'step': 'submit_task_screenshot', 'updated_at': 1753008643}
2025-07-20 16:20:56,708 - handlers.session_handlers - INFO - Message has text: False
2025-07-20 16:20:56,710 - handlers.session_handlers - INFO - Message has photo: True
2025-07-20 16:20:56,710 - handlers.session_handlers - INFO - Message has video: False
2025-07-20 16:20:56,711 - handlers.session_handlers - INFO - Message has document: False
2025-07-20 16:20:56,712 - handlers.session_handlers - INFO - Message text: 'None'
2025-07-20 16:21:01,497 - services.task_service - INFO - Created new task submission: submission_1753008661.e112ed66
2025-07-20 16:21:20,714 - handlers.callback_handlers - ERROR - Error in callback handler for data 'joined': Query is too old and response timeout expired or query id is invalid
2025-07-20 16:21:21,177 - __main__ - ERROR - Error in callback handler: Query is too old and response timeout expired or query id is invalid
2025-07-20 16:21:28,292 - handlers.callback_handlers - ERROR - Error in callback handler for data 'myWallet': Query is too old and response timeout expired or query id is invalid
2025-07-20 16:21:28,683 - __main__ - ERROR - Error in callback handler: Query is too old and response timeout expired or query id is invalid
2025-07-20 16:21:29,426 - handlers.callback_handlers - ERROR - Error in callback handler for data 'myWallet': Query is too old and response timeout expired or query id is invalid
2025-07-20 16:21:29,856 - __main__ - ERROR - Error in callback handler: Query is too old and response timeout expired or query id is invalid
2025-07-20 16:21:30,501 - services.user_service - INFO - Created new user: 7397411551 (ᥫ𝆺꯭𝅥💓🚬 ̶‌🇦‌ 𝐊 🅐 𝐒 𝐇༎ࠫ💎 ─⃛͢𓆪)
2025-07-20 16:21:32,759 - handlers.user_handlers - ERROR - Error sending new user notification: Forbidden: bot is not a member of the channel chat
2025-07-20 16:21:35,179 - handlers.user_handlers - INFO - Notification sent to referrer ********** about new user ᥫ𝆺꯭𝅥💓🚬 ̶‌🇦‌ 𝐊 🅐 𝐒 𝐇༎ࠫ💎 ─⃛͢𓆪 (ID: 7397411551): success
2025-07-20 16:21:37,655 - handlers.callback_handlers - ERROR - Error in callback handler for data 'levelRewards': Query is too old and response timeout expired or query id is invalid
2025-07-20 16:21:38,022 - __main__ - ERROR - Error in callback handler: Query is too old and response timeout expired or query id is invalid
2025-07-20 16:21:38,867 - handlers.callback_handlers - ERROR - Error in callback handler for data 'joined': Query is too old and response timeout expired or query id is invalid
2025-07-20 16:21:39,281 - __main__ - ERROR - Error in callback handler: Query is too old and response timeout expired or query id is invalid
2025-07-20 16:23:10,798 - handlers.callback_handlers - INFO - Message contains media, sending new text message instead of editing
2025-07-20 16:23:25,212 - handlers.callback_handlers - INFO - Message contains media, sending new text message instead of editing
2025-07-20 16:23:25,423 - handlers.callback_handlers - WARNING - Could not delete media message: Message to delete not found
2025-07-20 16:23:28,074 - handlers.callback_handlers - ERROR - Error in callback handler for data 'myWallet': Query is too old and response timeout expired or query id is invalid
2025-07-20 16:23:28,447 - __main__ - ERROR - Error in callback handler: Query is too old and response timeout expired or query id is invalid
2025-07-20 16:23:29,200 - handlers.callback_handlers - ERROR - Error in callback handler for data 'myWallet': Query is too old and response timeout expired or query id is invalid
2025-07-20 16:23:29,582 - __main__ - ERROR - Error in callback handler: Query is too old and response timeout expired or query id is invalid
2025-07-20 16:23:32,974 - handlers.callback_handlers - ERROR - Error in callback handler for data 'myWallet': Query is too old and response timeout expired or query id is invalid
2025-07-20 16:23:33,413 - __main__ - ERROR - Error in callback handler: Query is too old and response timeout expired or query id is invalid
2025-07-20 16:23:34,188 - handlers.callback_handlers - ERROR - Error in callback handler for data 'myWallet': Query is too old and response timeout expired or query id is invalid
2025-07-20 16:23:34,592 - __main__ - ERROR - Error in callback handler: Query is too old and response timeout expired or query id is invalid
2025-07-20 16:23:35,410 - handlers.callback_handlers - ERROR - Error in callback handler for data 'extraRewards': Query is too old and response timeout expired or query id is invalid
2025-07-20 16:23:35,822 - __main__ - ERROR - Error in callback handler: Query is too old and response timeout expired or query id is invalid
2025-07-20 16:23:43,858 - services.referral_service - INFO - Referral reward processed: ********** earned ₹2 for referring ᥫ𝆺꯭𝅥💓🚬 ̶‌🇦‌ 𝐊 🅐 𝐒 𝐇༎ࠫ💎 ─⃛͢𓆪
2025-07-20 16:23:44,757 - handlers.callback_handlers - ERROR - Error in callback handler for data 'myWallet': Query is too old and response timeout expired or query id is invalid
2025-07-20 16:23:45,134 - __main__ - ERROR - Error in callback handler: Query is too old and response timeout expired or query id is invalid
2025-07-20 16:23:45,888 - handlers.callback_handlers - ERROR - Error in callback handler for data 'myWallet': Query is too old and response timeout expired or query id is invalid
2025-07-20 16:23:46,266 - __main__ - ERROR - Error in callback handler: Query is too old and response timeout expired or query id is invalid
2025-07-20 16:23:47,019 - handlers.callback_handlers - ERROR - Error in callback handler for data 'myWallet': Query is too old and response timeout expired or query id is invalid
2025-07-20 16:23:47,392 - __main__ - ERROR - Error in callback handler: Query is too old and response timeout expired or query id is invalid
2025-07-20 16:23:48,150 - handlers.callback_handlers - ERROR - Error in callback handler for data 'myWallet': Query is too old and response timeout expired or query id is invalid
2025-07-20 16:23:48,530 - __main__ - ERROR - Error in callback handler: Query is too old and response timeout expired or query id is invalid
2025-07-20 16:23:49,271 - handlers.callback_handlers - ERROR - Error in callback handler for data 'myWallet': Query is too old and response timeout expired or query id is invalid
2025-07-20 16:23:49,642 - __main__ - ERROR - Error in callback handler: Query is too old and response timeout expired or query id is invalid
2025-07-20 16:23:50,390 - handlers.callback_handlers - ERROR - Error in callback handler for data 'myWallet': Query is too old and response timeout expired or query id is invalid
2025-07-20 16:23:50,765 - __main__ - ERROR - Error in callback handler: Query is too old and response timeout expired or query id is invalid
2025-07-20 16:23:53,934 - handlers.callback_handlers - ERROR - Error in callback handler for data 'joined': Query is too old and response timeout expired or query id is invalid
2025-07-20 16:23:54,312 - __main__ - ERROR - Error in callback handler: Query is too old and response timeout expired or query id is invalid
2025-07-20 16:23:56,977 - handlers.callback_handlers - ERROR - Error in callback handler for data 'extraRewards': Query is too old and response timeout expired or query id is invalid
2025-07-20 16:23:57,352 - __main__ - ERROR - Error in callback handler: Query is too old and response timeout expired or query id is invalid
2025-07-20 16:23:57,849 - handlers.user_handlers - INFO - Custom referral parameter used: Gift -> User ID: **********
2025-07-20 16:23:59,912 - services.user_service - INFO - Created new user: 7303733189 (ANGESH)
2025-07-20 16:24:01,421 - handlers.user_handlers - ERROR - Error sending new user notification: Forbidden: bot is not a member of the channel chat
2025-07-20 16:24:03,061 - handlers.user_handlers - INFO - Notification sent to referrer ********** about new user ANGESH (ID: 7303733189): success
2025-07-20 16:24:12,076 - handlers.callback_handlers - ERROR - Error in smart navigation: Message is not modified: specified new message content and reply markup are exactly the same as a current content and reply markup of the message
2025-07-20 16:26:07,870 - handlers.admin_handlers - INFO - Loaded 5 pending withdrawals in 0.800s
2025-07-20 16:26:08,256 - handlers.admin_handlers - INFO - handle_view_pending_withdrawals completed in 1.186s
2025-07-20 16:26:50,790 - services.withdrawal_service - INFO - Withdrawal rejected: User **********, Amount ₹100
2025-07-20 16:26:54,478 - handlers.callback_handlers - ERROR - Error in smart navigation: Message is not modified: specified new message content and reply markup are exactly the same as a current content and reply markup of the message
2025-07-20 16:26:56,253 - handlers.callback_handlers - ERROR - Error in smart navigation: Message is not modified: specified new message content and reply markup are exactly the same as a current content and reply markup of the message
2025-07-20 16:26:58,594 - services.user_service - INFO - Created new user: 6825869417 (Michu)
2025-07-20 16:26:59,806 - handlers.user_handlers - ERROR - Error sending new user notification: Forbidden: bot is not a member of the channel chat
2025-07-20 16:27:01,125 - handlers.user_handlers - INFO - Notification sent to referrer ********** about new user Michu (ID: 6825869417): success
2025-07-20 16:27:45,991 - services.referral_service - INFO - Referral reward processed: ********** earned ₹4 for referring ANGESH
2025-07-20 16:27:53,908 - services.user_service - INFO - Created new user: 7325851501 (Pokemon)
2025-07-20 16:27:55,209 - handlers.user_handlers - ERROR - Error sending new user notification: Forbidden: bot is not a member of the channel chat
2025-07-20 16:27:56,521 - handlers.user_handlers - INFO - Notification sent to referrer ********** about new user Pokemon (ID: 7325851501): success
2025-07-20 16:28:18,696 - services.referral_service - INFO - Referral reward processed: ********** earned ₹4 for referring Pokemon
2025-07-20 16:28:30,533 - handlers.user_handlers - INFO - Custom referral parameter used: from_channel -> User ID: **********
2025-07-20 16:28:36,637 - services.user_service - INFO - Created new user: 7956304733 (NITESH SINGH)
2025-07-20 16:28:37,895 - handlers.user_handlers - ERROR - Error sending new user notification: Forbidden: bot is not a member of the channel chat
2025-07-20 16:28:39,282 - handlers.user_handlers - INFO - Notification sent to referrer ********** about new user NITESH SINGH (ID: 7956304733): success
2025-07-20 16:28:43,400 - services.user_service - INFO - Created new user: 6018141358 (UNKNOWN)
2025-07-20 16:28:44,661 - handlers.user_handlers - ERROR - Error sending new user notification: Forbidden: bot is not a member of the channel chat
2025-07-20 16:28:46,444 - handlers.user_handlers - INFO - Notification sent to referrer ********** about new user UNKNOWN (ID: 6018141358): success
2025-07-20 16:28:51,024 - services.user_service - INFO - Created new user: 2146510610 (RiShi🍅🐈‍⬛️)
2025-07-20 16:28:52,222 - handlers.user_handlers - ERROR - Error sending new user notification: Forbidden: bot is not a member of the channel chat
2025-07-20 16:28:53,557 - handlers.user_handlers - INFO - Notification sent to referrer ********** about new user RiShi🍅🐈‍⬛️ (ID: 2146510610): success
2025-07-20 16:28:59,963 - services.referral_service - INFO - Referral reward processed: ********** earned ₹3 for referring NITESH SINGH
2025-07-20 16:29:03,200 - handlers.user_handlers - INFO - Custom referral parameter used: from_channel -> User ID: **********
2025-07-20 16:29:11,476 - services.referral_service - INFO - Referral reward processed: ********** earned ₹3 for referring UNKNOWN
2025-07-20 16:29:18,710 - handlers.callback_handlers - ERROR - Error in smart navigation: Message is not modified: specified new message content and reply markup are exactly the same as a current content and reply markup of the message
2025-07-20 16:29:26,083 - handlers.user_handlers - INFO - Custom referral parameter used: from_channel -> User ID: **********
2025-07-20 16:29:28,719 - handlers.user_handlers - INFO - Custom referral parameter used: Gift -> User ID: **********
2025-07-20 16:29:43,524 - services.user_service - INFO - Created new user: 8199983441 (YASH)
2025-07-20 16:29:45,524 - handlers.user_handlers - ERROR - Error sending new user notification: Forbidden: bot is not a member of the channel chat
2025-07-20 16:29:47,769 - handlers.user_handlers - INFO - Notification sent to referrer ********** about new user YASH (ID: 8199983441): success
2025-07-20 16:30:09,041 - services.referral_service - INFO - Referral reward processed: ********** earned ₹5 for referring YASH
2025-07-20 16:30:18,100 - handlers.user_handlers - INFO - Custom referral parameter used: Gift -> User ID: **********
2025-07-20 16:30:29,596 - services.user_service - INFO - Created new user: 7210022167 (RAJPUT)
2025-07-20 16:30:30,924 - handlers.user_handlers - ERROR - Error sending new user notification: Forbidden: bot is not a member of the channel chat
2025-07-20 16:30:32,311 - handlers.user_handlers - INFO - Notification sent to referrer ********** about new user RAJPUT (ID: 7210022167): success
2025-07-20 16:30:37,206 - handlers.callback_handlers - ERROR - Error in smart navigation: Message is not modified: specified new message content and reply markup are exactly the same as a current content and reply markup of the message
2025-07-20 16:30:39,014 - handlers.callback_handlers - ERROR - Error in smart navigation: Message is not modified: specified new message content and reply markup are exactly the same as a current content and reply markup of the message
2025-07-20 16:30:40,861 - handlers.callback_handlers - ERROR - Error in smart navigation: Message is not modified: specified new message content and reply markup are exactly the same as a current content and reply markup of the message
2025-07-20 16:30:42,637 - handlers.callback_handlers - ERROR - Error in smart navigation: Message is not modified: specified new message content and reply markup are exactly the same as a current content and reply markup of the message
2025-07-20 16:30:49,464 - services.referral_service - INFO - Referral reward processed: ********** earned ₹1 for referring RAJPUT
2025-07-20 16:31:20,630 - services.user_service - INFO - Created new user: 8068883813 (HARSH)
2025-07-20 16:31:22,473 - handlers.user_handlers - ERROR - Error sending new user notification: Forbidden: bot is not a member of the channel chat
2025-07-20 16:31:24,810 - handlers.user_handlers - INFO - Notification sent to referrer ********** about new user HARSH (ID: 8068883813): success
2025-07-20 16:31:44,688 - services.referral_service - INFO - Referral reward processed: ********** earned ₹4 for referring HARSH
2025-07-20 16:31:53,303 - services.user_service - INFO - Created new user: 8149403675 (RANDHIR)
2025-07-20 16:31:54,599 - handlers.user_handlers - ERROR - Error sending new user notification: Forbidden: bot is not a member of the channel chat
2025-07-20 16:31:55,966 - handlers.user_handlers - INFO - Notification sent to referrer ********** about new user RANDHIR (ID: 8149403675): success
2025-07-20 16:32:08,796 - services.withdrawal_service - INFO - Withdrawal record saved for user 5924912755: withdrawal_1753009328.85a3eed7
2025-07-20 16:32:15,447 - services.referral_service - INFO - Referral reward processed: ********** earned ₹3 for referring RANDHIR
2025-07-20 16:32:23,616 - services.user_service - INFO - Created new user: 6587996681 (Muswer)
2025-07-20 16:32:25,396 - handlers.user_handlers - ERROR - Error sending new user notification: Forbidden: bot is not a member of the channel chat
2025-07-20 16:32:27,139 - handlers.user_handlers - INFO - Notification sent to referrer ********** about new user Muswer (ID: 6587996681): success
2025-07-20 16:32:41,690 - services.user_service - INFO - Created new user: 8046702914 (Pikachu)
2025-07-20 16:32:43,146 - handlers.user_handlers - ERROR - Error sending new user notification: Forbidden: bot is not a member of the channel chat
2025-07-20 16:32:44,554 - handlers.user_handlers - INFO - Notification sent to referrer ********** about new user Pikachu (ID: 8046702914): success
2025-07-20 16:32:50,353 - handlers.user_handlers - INFO - Custom referral parameter used: from_channel -> User ID: **********
2025-07-20 16:33:10,027 - services.referral_service - INFO - Referral reward processed: ********** earned ₹1 for referring Pikachu
2025-07-20 16:33:30,662 - services.user_service - INFO - Created new user: 8084077392 (Greninja)
2025-07-20 16:33:32,131 - handlers.user_handlers - ERROR - Error sending new user notification: Forbidden: bot is not a member of the channel chat
2025-07-20 16:33:33,681 - handlers.user_handlers - INFO - Notification sent to referrer ********** about new user Greninja (ID: 8084077392): success
2025-07-20 16:33:43,971 - handlers.user_handlers - INFO - Custom referral parameter used: Gift -> User ID: **********
2025-07-20 16:33:45,869 - services.user_service - INFO - Created new user: 1992907103 (Valli Priya)
2025-07-20 16:33:47,637 - handlers.user_handlers - ERROR - Error sending new user notification: Forbidden: bot is not a member of the channel chat
2025-07-20 16:33:49,553 - handlers.user_handlers - INFO - Notification sent to referrer ********** about new user Valli Priya (ID: 1992907103): success
2025-07-20 16:33:56,356 - services.referral_service - INFO - Referral reward processed: ********** earned ₹5 for referring Greninja
2025-07-20 16:34:07,674 - handlers.callback_handlers - ERROR - Error in _handle_check_subscription: Query is too old and response timeout expired or query id is invalid
2025-07-20 16:34:08,052 - handlers.callback_handlers - ERROR - Error in callback handler for data 'checkSubscription': Query is too old and response timeout expired or query id is invalid
2025-07-20 16:34:08,424 - __main__ - ERROR - Error in callback handler: Query is too old and response timeout expired or query id is invalid
2025-07-20 16:34:09,167 - handlers.callback_handlers - ERROR - Error in callback handler for data 'joined': Query is too old and response timeout expired or query id is invalid
2025-07-20 16:34:09,540 - __main__ - ERROR - Error in callback handler: Query is too old and response timeout expired or query id is invalid
2025-07-20 16:34:17,476 - services.referral_service - INFO - Referral reward processed: ********** earned ₹1 for referring Muswer
2025-07-20 16:34:18,257 - services.user_service - INFO - Created new user: 8013464649 (Singh)
2025-07-20 16:34:19,822 - handlers.user_handlers - ERROR - Error sending new user notification: Forbidden: bot is not a member of the channel chat
2025-07-20 16:34:21,427 - handlers.user_handlers - INFO - Notification sent to referrer ********** about new user Singh (ID: 8013464649): success
2025-07-20 16:34:23,615 - handlers.callback_handlers - ERROR - Error in callback handler for data 'joined': Query is too old and response timeout expired or query id is invalid
2025-07-20 16:34:23,991 - __main__ - ERROR - Error in callback handler: Query is too old and response timeout expired or query id is invalid
2025-07-20 16:34:24,739 - handlers.callback_handlers - ERROR - Error in callback handler for data 'joined': Query is too old and response timeout expired or query id is invalid
2025-07-20 16:34:25,115 - __main__ - ERROR - Error in callback handler: Query is too old and response timeout expired or query id is invalid
2025-07-20 16:34:27,650 - handlers.callback_handlers - ERROR - Error in callback handler for data 'joined': Query is too old and response timeout expired or query id is invalid
2025-07-20 16:34:28,029 - __main__ - ERROR - Error in callback handler: Query is too old and response timeout expired or query id is invalid
2025-07-20 16:34:28,797 - handlers.callback_handlers - ERROR - Error in callback handler for data 'joined': Query is too old and response timeout expired or query id is invalid
2025-07-20 16:34:29,172 - __main__ - ERROR - Error in callback handler: Query is too old and response timeout expired or query id is invalid
2025-07-20 16:34:29,972 - handlers.callback_handlers - ERROR - Error in callback handler for data 'joined': Query is too old and response timeout expired or query id is invalid
2025-07-20 16:34:30,679 - __main__ - ERROR - Error in callback handler: Query is too old and response timeout expired or query id is invalid
2025-07-20 16:34:31,462 - handlers.callback_handlers - ERROR - Error in callback handler for data 'checkSubscription': Query is too old and response timeout expired or query id is invalid
2025-07-20 16:34:31,854 - __main__ - ERROR - Error in callback handler: Query is too old and response timeout expired or query id is invalid
2025-07-20 16:34:34,523 - services.user_service - INFO - Created new user: 8041208491 (🇹 🇭 🇦 🇰 🇺 🇷)
2025-07-20 16:34:35,889 - handlers.user_handlers - ERROR - Error sending new user notification: Forbidden: bot is not a member of the channel chat
2025-07-20 16:34:37,274 - handlers.user_handlers - INFO - Notification sent to referrer ********** about new user 🇹 🇭 🇦 🇰 🇺 🇷 (ID: 8041208491): success
2025-07-20 16:35:02,753 - services.referral_service - INFO - Referral reward processed: ********** earned ₹2 for referring Singh
2025-07-20 16:35:17,492 - services.referral_service - INFO - Referral reward processed: ********** earned ₹2 for referring Valli Priya
2025-07-20 16:35:29,499 - services.referral_service - INFO - Referral reward processed: ********** earned ₹1 for referring 🇹 🇭 🇦 🇰 🇺 🇷
2025-07-20 16:35:51,864 - services.user_service - INFO - Created new user: 7925040787 (AYUSH)
2025-07-20 16:35:53,248 - handlers.user_handlers - ERROR - Error sending new user notification: Forbidden: bot is not a member of the channel chat
2025-07-20 16:35:55,313 - handlers.user_handlers - INFO - Notification sent to referrer ********** about new user AYUSH (ID: 7925040787): success
2025-07-20 16:36:10,909 - services.user_service - INFO - Created new user: 6981034336 (Emmanuel W- Coin)
2025-07-20 16:36:12,470 - handlers.user_handlers - ERROR - Error sending new user notification: Forbidden: bot is not a member of the channel chat
2025-07-20 16:36:14,130 - handlers.user_handlers - INFO - Notification sent to referrer 6844831204 about new user Emmanuel W- Coin (ID: 6981034336): success
2025-07-20 16:36:20,337 - services.referral_service - INFO - Referral reward processed: ********** earned ₹5 for referring AYUSH
2025-07-20 16:36:23,460 - services.user_service - INFO - Created new user: 7634844144 (Md)
2025-07-20 16:36:25,254 - handlers.user_handlers - ERROR - Error sending new user notification: Forbidden: bot is not a member of the channel chat
2025-07-20 16:36:27,382 - handlers.user_handlers - INFO - Notification sent to referrer ********** about new user Md (ID: 7634844144): success
2025-07-20 16:36:30,131 - handlers.callback_handlers - ERROR - Error in callback handler for data 'bot_statistics': Query is too old and response timeout expired or query id is invalid
2025-07-20 16:36:30,508 - __main__ - ERROR - Error in callback handler: Query is too old and response timeout expired or query id is invalid
2025-07-20 16:36:34,043 - handlers.user_handlers - ERROR - Error in handle_joined_channel: Query is too old and response timeout expired or query id is invalid
2025-07-20 16:36:34,688 - services.user_service - INFO - Created new user: 7692242870 (—͟͞ 𝐈тᷟʑ꯭ͤ𓄂︪︫︠𓆩꯭〭〬🇩꯭ ꯭𝙀꯭꯭𝙀꯭꯭𝙋꯭꯭𝘼꯭꯭𝙆꯭꯭𝙓꯭꯭𝙃꯭4꯭𝙀꯭꯭𝙍꯭⍣⃪͜ ꭗ̥̽𝆺꯭𝅥𔘓༌🪽⎯꯭̽)
2025-07-20 16:36:36,087 - handlers.user_handlers - ERROR - Error sending new user notification: Forbidden: bot is not a member of the channel chat
2025-07-20 16:36:37,581 - handlers.user_handlers - INFO - Notification sent to referrer ********** about new user —͟͞ 𝐈тᷟʑ꯭ͤ𓄂︪︫︠𓆩꯭〭〬🇩꯭ ꯭𝙀꯭꯭𝙀꯭꯭𝙋꯭꯭𝘼꯭꯭𝙆꯭꯭𝙓꯭꯭𝙃꯭4꯭𝙀꯭꯭𝙍꯭⍣⃪͜ ꭗ̥̽𝆺꯭𝅥𔘓༌🪽⎯꯭̽ (ID: 7692242870): success
2025-07-20 16:36:44,842 - services.user_service - INFO - Created new user: 7671315526 (𓆰↬֟፝𝐁 𝜄 𝚱 Ʌ ꯭𝛅 ʜ ⌯ ꭗ‌𝐃 𝅃⤹🍷𓆪ꪾ 🜲 ❮ 匚ΉΣΛƬ X ΛЯMY ❯)
2025-07-20 16:36:46,290 - handlers.user_handlers - ERROR - Error sending new user notification: Forbidden: bot is not a member of the channel chat
2025-07-20 16:36:47,776 - handlers.user_handlers - INFO - Notification sent to referrer ********** about new user 𓆰↬֟፝𝐁 𝜄 𝚱 Ʌ ꯭𝛅 ʜ ⌯ ꭗ‌𝐃 𝅃⤹🍷𓆪ꪾ 🜲 ❮ 匚ΉΣΛƬ X ΛЯMY ❯ (ID: 7671315526): success
2025-07-20 16:37:23,281 - services.referral_service - INFO - Referral reward processed: 6844831204 earned ₹5 for referring Emmanuel W- Coin
2025-07-20 16:37:40,905 - services.referral_service - INFO - Referral reward processed: ********** earned ₹4 for referring —͟͞ 𝐈тᷟʑ꯭ͤ𓄂︪︫︠𓆩꯭〭〬🇩꯭ ꯭𝙀꯭꯭𝙀꯭꯭𝙋꯭꯭𝘼꯭꯭𝙆꯭꯭𝙓꯭꯭𝙃꯭4꯭𝙀꯭꯭𝙍꯭⍣⃪͜ ꭗ̥̽𝆺꯭𝅥𔘓༌🪽⎯꯭̽
2025-07-20 16:37:50,812 - services.user_service - INFO - Created new user: ********** (SUMIT)
2025-07-20 16:37:52,058 - handlers.user_handlers - ERROR - Error sending new user notification: Forbidden: bot is not a member of the channel chat
2025-07-20 16:37:53,372 - handlers.user_handlers - INFO - Notification sent to referrer ********** about new user SUMIT (ID: **********): success
2025-07-20 16:38:00,184 - services.referral_service - INFO - Referral reward processed: ********** earned ₹1 for referring 𓆰↬֟፝𝐁 𝜄 𝚱 Ʌ ꯭𝛅 ʜ ⌯ ꭗ‌𝐃 𝅃⤹🍷𓆪ꪾ 🜲 ❮ 匚ΉΣΛƬ X ΛЯMY ❯
2025-07-20 16:38:26,777 - handlers.user_handlers - INFO - Custom referral parameter used: Gift -> User ID: **********
2025-07-20 16:39:07,679 - handlers.session_handlers - INFO - === SESSION MESSAGE HANDLER for user **********, step: set_account_name ===
2025-07-20 16:39:07,680 - handlers.session_handlers - INFO - Session data: {'_id': ObjectId('687cce457104627ee2034933'), 'user_id': **********, 'created_at': **********, 'data': {}, 'step': 'set_account_name', 'updated_at': **********}
2025-07-20 16:39:07,680 - handlers.session_handlers - INFO - Message has text: True
2025-07-20 16:39:07,680 - handlers.session_handlers - INFO - Message has photo: False
2025-07-20 16:39:07,680 - handlers.session_handlers - INFO - Message has video: False
2025-07-20 16:39:07,680 - handlers.session_handlers - INFO - Message has document: False
2025-07-20 16:39:07,681 - handlers.session_handlers - INFO - Message text: 'Raunak'
2025-07-20 16:39:28,661 - handlers.callback_handlers - ERROR - Error in _handle_withdrawal_method_selection: Message is not modified: specified new message content and reply markup are exactly the same as a current content and reply markup of the message
2025-07-20 16:39:32,168 - handlers.callback_handlers - ERROR - Error in smart navigation: Message is not modified: specified new message content and reply markup are exactly the same as a current content and reply markup of the message
2025-07-20 16:39:43,681 - services.user_service - INFO - Created new user: ********** (Piyush)
2025-07-20 16:39:45,159 - handlers.user_handlers - ERROR - Error sending new user notification: Forbidden: bot is not a member of the channel chat
2025-07-20 16:39:46,951 - handlers.user_handlers - INFO - Notification sent to referrer ********** about new user Piyush (ID: **********): success
2025-07-20 16:39:52,687 - handlers.session_handlers - INFO - === SESSION MESSAGE HANDLER for user **********, step: set_mobile_number ===
2025-07-20 16:39:52,688 - handlers.session_handlers - INFO - Session data: {'_id': ObjectId('687cce6e7104627ee2034934'), 'user_id': **********, 'created_at': 1753009775, 'data': {}, 'step': 'set_mobile_number', 'updated_at': 1753009775}
2025-07-20 16:39:52,688 - handlers.session_handlers - INFO - Message has text: True
2025-07-20 16:39:52,688 - handlers.session_handlers - INFO - Message has photo: False
2025-07-20 16:39:52,689 - handlers.session_handlers - INFO - Message has video: False
2025-07-20 16:39:52,689 - handlers.session_handlers - INFO - Message has document: False
2025-07-20 16:39:52,689 - handlers.session_handlers - INFO - Message text: '8404938672'
2025-07-20 16:39:54,672 - handlers.callback_handlers - ERROR - Error in smart navigation: Message is not modified: specified new message content and reply markup are exactly the same as a current content and reply markup of the message
2025-07-20 16:40:07,588 - services.user_service - INFO - Created new user: 7677377139 (Manoj)
2025-07-20 16:40:08,901 - handlers.user_handlers - ERROR - Error sending new user notification: Forbidden: bot is not a member of the channel chat
2025-07-20 16:40:10,336 - handlers.user_handlers - INFO - Notification sent to referrer ********** about new user Manoj (ID: 7677377139): success
2025-07-20 16:40:44,693 - services.user_service - INFO - Created new user: 7197452975 (Hasan)
2025-07-20 16:40:46,173 - handlers.user_handlers - ERROR - Error sending new user notification: Forbidden: bot is not a member of the channel chat
2025-07-20 16:40:47,967 - handlers.user_handlers - INFO - Notification sent to referrer ********** about new user Hasan (ID: 7197452975): success
2025-07-20 16:41:24,812 - handlers.user_handlers - INFO - Custom referral parameter used: from_channel -> User ID: **********
2025-07-20 16:42:05,617 - handlers.user_handlers - ERROR - Error showing main menu: Message to edit not found
2025-07-20 16:42:05,832 - handlers.callback_handlers - ERROR - Error in _handle_check_subscription: Message to edit not found
2025-07-20 16:42:10,293 - services.referral_service - INFO - Referral reward processed: ********** earned ₹1 for referring Hasan
2025-07-20 16:42:24,786 - handlers.user_handlers - ERROR - Error in handle_joined_channel: Query is too old and response timeout expired or query id is invalid
2025-07-20 16:42:25,573 - handlers.callback_handlers - ERROR - Error in callback handler for data 'joined': Query is too old and response timeout expired or query id is invalid
2025-07-20 16:42:25,951 - __main__ - ERROR - Error in callback handler: Query is too old and response timeout expired or query id is invalid
2025-07-20 16:42:27,768 - handlers.callback_handlers - INFO - Message contains media, sending new text message instead of editing
2025-07-20 16:42:58,024 - services.user_service - INFO - Created new user: 7409668641 (Daniel)
2025-07-20 16:42:59,198 - handlers.user_handlers - ERROR - Error sending new user notification: Forbidden: bot is not a member of the channel chat
2025-07-20 16:43:00,679 - handlers.user_handlers - INFO - Notification sent to referrer ********** about new user Daniel (ID: 7409668641): success
2025-07-20 16:43:15,832 - handlers.session_handlers - INFO - === SESSION MESSAGE HANDLER for user 6981034336, step: submit_task_screenshot ===
2025-07-20 16:43:15,833 - handlers.session_handlers - INFO - Session data: {'_id': ObjectId('687ccf1c7104627ee2034936'), 'user_id': 6981034336, 'created_at': 1753009948, 'data': {'task_id': 'task_1752995334.fd08d118'}, 'step': 'submit_task_screenshot', 'updated_at': 1753009948}
2025-07-20 16:43:15,833 - handlers.session_handlers - INFO - Message has text: False
2025-07-20 16:43:15,833 - handlers.session_handlers - INFO - Message has photo: True
2025-07-20 16:43:15,834 - handlers.session_handlers - INFO - Message has video: False
2025-07-20 16:43:15,834 - handlers.session_handlers - INFO - Message has document: False
2025-07-20 16:43:15,834 - handlers.session_handlers - INFO - Message text: 'None'
2025-07-20 16:43:16,941 - services.task_service - INFO - Created new task submission: submission_1753009996.0e96f693
2025-07-20 16:47:53,056 - utils.helpers - WARNING - Sync is_admin() called from async context for user 6661385723 - consider using is_admin_async()
2025-07-20 16:47:53,060 - services.admin_service - ERROR - Error getting admin settings for **********: Task <Task pending name='Task-239' coro=<_check_admin_in_new_loop() running at C:\Users\<USER>\Desktop\referbot\python\utils\helpers.py:528> cb=[_run_until_complete_cb() at C:\Python312\Lib\asyncio\base_events.py:182]> got Future <Future pending cb=[_chain_future.<locals>._call_check_cancel() at C:\Python312\Lib\asyncio\futures.py:391]> attached to a different loop
2025-07-20 16:48:26,318 - handlers.user_handlers - INFO - Custom referral parameter used: from_channel -> User ID: **********
2025-07-20 16:48:36,135 - handlers.user_handlers - INFO - Custom referral parameter used: from_channel -> User ID: **********
2025-07-20 16:48:50,872 - handlers.user_handlers - INFO - Custom referral parameter used: from_channel -> User ID: **********
2025-07-20 16:49:22,916 - handlers.session_handlers - INFO - === SESSION MESSAGE HANDLER for user 6661385723, step: redeem_gift_code ===
2025-07-20 16:49:22,916 - handlers.session_handlers - INFO - Session data: {'_id': ObjectId('687cd0887104627ee2034938'), 'user_id': 6661385723, 'created_at': 1753010340, 'data': {}, 'step': 'redeem_gift_code', 'updated_at': 1753010340}
2025-07-20 16:49:22,917 - handlers.session_handlers - INFO - Message has text: True
2025-07-20 16:49:22,917 - handlers.session_handlers - INFO - Message has photo: False
2025-07-20 16:49:22,917 - handlers.session_handlers - INFO - Message has video: False
2025-07-20 16:49:22,917 - handlers.session_handlers - INFO - Message has document: False
2025-07-20 16:49:22,918 - handlers.session_handlers - INFO - Message text: 'GDUDJWLWNRJSNS'
2025-07-20 16:49:53,763 - utils.helpers - WARNING - Sync is_admin() called from async context for user 6661385723 - consider using is_admin_async()
2025-07-20 16:49:53,765 - services.admin_service - ERROR - Error getting admin settings for **********: Task <Task pending name='Task-246' coro=<_check_admin_in_new_loop() running at C:\Users\<USER>\Desktop\referbot\python\utils\helpers.py:528> cb=[_run_until_complete_cb() at C:\Python312\Lib\asyncio\base_events.py:182]> got Future <Future pending cb=[_chain_future.<locals>._call_check_cancel() at C:\Python312\Lib\asyncio\futures.py:391]> attached to a different loop
2025-07-20 16:51:24,991 - handlers.user_handlers - ERROR - Error showing main menu: Message to edit not found
2025-07-20 16:51:25,199 - handlers.callback_handlers - ERROR - Error in _handle_check_subscription: Message to edit not found
2025-07-20 16:51:25,635 - handlers.session_handlers - INFO - === SESSION MESSAGE HANDLER for user 6661385723, step: set_mobile_number ===
2025-07-20 16:51:25,635 - handlers.session_handlers - INFO - Session data: {'_id': ObjectId('687cd10e7104627ee2034939'), 'user_id': 6661385723, 'created_at': 1753010447, 'data': {}, 'step': 'set_mobile_number', 'updated_at': 1753010447}
2025-07-20 16:51:25,636 - handlers.session_handlers - INFO - Message has text: True
2025-07-20 16:51:25,636 - handlers.session_handlers - INFO - Message has photo: False
2025-07-20 16:51:25,636 - handlers.session_handlers - INFO - Message has video: False
2025-07-20 16:51:25,636 - handlers.session_handlers - INFO - Message has document: False
2025-07-20 16:51:25,637 - handlers.session_handlers - INFO - Message text: '+917982100138'
2025-07-20 16:51:26,520 - handlers.session_handlers - INFO - Attempting to send OTP to mobile number: +917982100138
2025-07-20 16:51:26,570 - services.withdrawal_service - INFO - Using OTP API key from environment variable
2025-07-20 16:51:26,570 - services.withdrawal_service - INFO - Converted +917982100138 to API format: 7982100138
2025-07-20 16:51:26,570 - services.withdrawal_service - INFO - Sending OTP to +917982100138 (API format: 7982100138) using URL: https://sms.renflair.in/V1.php
2025-07-20 16:51:27,963 - services.withdrawal_service - INFO - OTP API response for +917982100138: Status 200, Content: {"return":true,"request_id":"XhZSP8OpucBlbwM","message":"SMS sent successfully.","status":"SUCCESS"}
2025-07-20 16:51:27,964 - services.withdrawal_service - INFO - OTP API analysis for +917982100138 (API format: 7982100138): Status=200, HasFailure=False, HasSuccess=True, ResponseLength=100
2025-07-20 16:51:27,964 - services.withdrawal_service - INFO - OTP API call successful for +917982100138. OTP: 9170
2025-07-20 16:51:27,965 - services.withdrawal_service - INFO - Note: SMS delivery may take 1-5 minutes depending on carrier
2025-07-20 16:51:28,434 - handlers.session_handlers - INFO - OTP API call successful for +917982100138, user 6661385723 moved to verification step
2025-07-20 16:51:41,893 - handlers.session_handlers - INFO - === SESSION MESSAGE HANDLER for user 6661385723, step: verify_otp ===
2025-07-20 16:51:41,894 - handlers.session_handlers - INFO - Session data: {'_id': ObjectId('687cd10e7104627ee2034939'), 'user_id': 6661385723, 'created_at': 1753010487, 'data': {'mobile_number': '+917982100138', 'otp': '9170', 'otp_timestamp': 1753010487}, 'step': 'verify_otp', 'updated_at': 1753010487}
2025-07-20 16:51:41,894 - handlers.session_handlers - INFO - Message has text: True
2025-07-20 16:51:41,894 - handlers.session_handlers - INFO - Message has photo: False
2025-07-20 16:51:41,894 - handlers.session_handlers - INFO - Message has video: False
2025-07-20 16:51:41,895 - handlers.session_handlers - INFO - Message has document: False
2025-07-20 16:51:41,895 - handlers.session_handlers - INFO - Message text: '9170'
2025-07-20 16:51:48,321 - services.referral_service - INFO - Referral reward processed: ********** earned ₹5 for referring Red
2025-07-20 16:51:52,725 - handlers.user_handlers - INFO - Custom referral parameter used: Gift -> User ID: **********
2025-07-20 16:52:07,784 - handlers.user_handlers - INFO - Custom referral parameter used: Gift -> User ID: **********
2025-07-20 16:53:06,625 - services.referral_service - INFO - Referral reward processed: ********** earned ₹3 for referring Piyush
2025-07-20 16:53:26,634 - services.user_service - INFO - Created new user: 5774675843 (Sritam)
2025-07-20 16:53:28,248 - handlers.user_handlers - ERROR - Error sending new user notification: Forbidden: bot is not a member of the channel chat
2025-07-20 16:53:29,666 - handlers.user_handlers - INFO - Notification sent to referrer ********** about new user Sritam (ID: 5774675843): success
2025-07-20 16:54:23,176 - __main__ - INFO - Received signal 2, initiating shutdown...
2025-07-20 16:54:23,176 - __main__ - INFO - Shutting down bot...
2025-07-20 16:54:23,628 - apscheduler.scheduler - INFO - Scheduler has been shut down
2025-07-20 16:54:23,673 - config.database - INFO - Disconnected from MongoDB
2025-07-20 16:54:23,673 - __main__ - INFO - Bot shutdown completed
2025-07-20 16:54:29,789 - __main__ - INFO - Connecting to MongoDB...
2025-07-20 16:54:43,453 - config.database - INFO - Successfully connected to MongoDB: referral_bot
2025-07-20 16:54:45,116 - config.database - INFO - Database collections and indexes initialized successfully
2025-07-20 16:54:45,117 - __main__ - INFO - Initializing Telegram bot...
2025-07-20 16:54:45,688 - __main__ - INFO - All handlers registered successfully
2025-07-20 16:54:46,526 - services.admin_service - INFO - Initialized admin settings for 4 admins
2025-07-20 16:54:46,570 - __main__ - INFO - Bot info updated: @InstantoPayBot (InstantoPay)
2025-07-20 16:54:46,570 - __main__ - INFO - Admin settings and level rewards system initialized
2025-07-20 16:54:46,571 - __main__ - INFO - Bot initialization completed successfully
2025-07-20 16:54:46,571 - __main__ - INFO - Starting bot polling...
2025-07-20 16:54:46,765 - apscheduler.scheduler - INFO - Scheduler started
2025-07-20 16:54:46,961 - __main__ - INFO - Bot is now running and polling for updates...
2025-07-20 16:54:47,940 - handlers.callback_handlers - ERROR - Error in callback handler for data 'myWallet': Query is too old and response timeout expired or query id is invalid
2025-07-20 16:54:48,301 - __main__ - ERROR - Error in callback handler: Query is too old and response timeout expired or query id is invalid
2025-07-20 16:55:09,840 - handlers.user_handlers - INFO - Custom referral parameter used: Gift -> User ID: **********
2025-07-20 16:56:22,813 - handlers.session_handlers - INFO - === SESSION MESSAGE HANDLER for user **********, step: set_mobile_number ===
2025-07-20 16:56:22,813 - handlers.session_handlers - INFO - Session data: {'_id': ObjectId('687cd24b7104627ee203493c'), 'user_id': **********, 'created_at': 1753010763, 'data': {}, 'step': 'set_mobile_number', 'updated_at': 1753010763}
2025-07-20 16:56:22,814 - handlers.session_handlers - INFO - Message has text: True
2025-07-20 16:56:22,814 - handlers.session_handlers - INFO - Message has photo: False
2025-07-20 16:56:22,814 - handlers.session_handlers - INFO - Message has video: False
2025-07-20 16:56:22,814 - handlers.session_handlers - INFO - Message has document: False
2025-07-20 16:56:22,815 - handlers.session_handlers - INFO - Message text: '8235761401'
2025-07-20 16:56:29,606 - handlers.callback_handlers - ERROR - Error in _handle_withdraw_amount: Message is not modified: specified new message content and reply markup are exactly the same as a current content and reply markup of the message
2025-07-20 16:56:33,147 - handlers.callback_handlers - INFO - Message contains media, sending new text message instead of editing
2025-07-20 16:56:44,789 - services.referral_service - INFO - Referral reward processed: ********** earned ₹4 for referring Ybhh
2025-07-20 16:56:50,307 - handlers.session_handlers - INFO - === SESSION MESSAGE HANDLER for user **********, step: set_mobile_number ===
2025-07-20 16:56:50,307 - handlers.session_handlers - INFO - Session data: {'_id': ObjectId('687cd24b7104627ee203493c'), 'user_id': **********, 'created_at': 1753010791, 'data': {}, 'step': 'set_mobile_number', 'updated_at': 1753010791}
2025-07-20 16:56:50,308 - handlers.session_handlers - INFO - Message has text: True
2025-07-20 16:56:50,308 - handlers.session_handlers - INFO - Message has photo: False
2025-07-20 16:56:50,308 - handlers.session_handlers - INFO - Message has video: False
2025-07-20 16:56:50,308 - handlers.session_handlers - INFO - Message has document: False
2025-07-20 16:56:50,309 - handlers.session_handlers - INFO - Message text: 'Send'
2025-07-20 16:56:55,034 - utils.helpers - WARNING - Sync is_admin() called from async context for user ********** - consider using is_admin_async()
2025-07-20 16:56:55,036 - services.admin_service - ERROR - Error getting admin settings for **********: Task <Task pending name='Task-13' coro=<_check_admin_in_new_loop() running at C:\Users\<USER>\Desktop\referbot\python\utils\helpers.py:528> cb=[_run_until_complete_cb() at C:\Python312\Lib\asyncio\base_events.py:182]> got Future <Future pending cb=[_chain_future.<locals>._call_check_cancel() at C:\Python312\Lib\asyncio\futures.py:391]> attached to a different loop
2025-07-20 16:57:14,519 - handlers.user_handlers - INFO - Custom referral parameter used: from_channel -> User ID: **********
2025-07-20 16:57:35,527 - services.user_service - INFO - Created new user: ********** (Narendra)
2025-07-20 16:57:36,825 - handlers.user_handlers - ERROR - Error sending new user notification: Forbidden: bot is not a member of the channel chat
2025-07-20 16:57:38,305 - handlers.user_handlers - INFO - Notification sent to referrer ********** about new user Narendra (ID: **********): success
2025-07-20 16:57:42,141 - handlers.session_handlers - INFO - === SESSION MESSAGE HANDLER for user **********, step: set_account_name ===
2025-07-20 16:57:42,141 - handlers.session_handlers - INFO - Session data: {'_id': ObjectId('687cd2877104627ee203493d'), 'user_id': **********, 'created_at': **********, 'data': {}, 'step': 'set_account_name', 'updated_at': **********}
2025-07-20 16:57:42,141 - handlers.session_handlers - INFO - Message has text: True
2025-07-20 16:57:42,142 - handlers.session_handlers - INFO - Message has photo: False
2025-07-20 16:57:42,142 - handlers.session_handlers - INFO - Message has video: False
2025-07-20 16:57:42,142 - handlers.session_handlers - INFO - Message has document: False
2025-07-20 16:57:42,143 - handlers.session_handlers - INFO - Message text: 'Sachin Kumar'
2025-07-20 16:57:57,411 - handlers.session_handlers - INFO - === SESSION MESSAGE HANDLER for user **********, step: submit_task_screenshot ===
2025-07-20 16:57:57,411 - handlers.session_handlers - INFO - Session data: {'_id': ObjectId('687cd2207104627ee203493b'), 'user_id': **********, 'created_at': **********, 'data': {'task_id': 'task_1752995334.fd08d118'}, 'step': 'submit_task_screenshot', 'updated_at': **********}
2025-07-20 16:57:57,411 - handlers.session_handlers - INFO - Message has text: False
2025-07-20 16:57:57,412 - handlers.session_handlers - INFO - Message has photo: True
2025-07-20 16:57:57,412 - handlers.session_handlers - INFO - Message has video: False
2025-07-20 16:57:57,412 - handlers.session_handlers - INFO - Message has document: False
2025-07-20 16:57:57,412 - handlers.session_handlers - INFO - Message text: 'None'
2025-07-20 16:57:58,630 - services.task_service - INFO - Created new task submission: submission_1753010878.283b9254
2025-07-20 16:58:12,904 - handlers.admin_handlers - INFO - Loaded 5 pending withdrawals in 0.561s
2025-07-20 16:58:13,285 - handlers.admin_handlers - INFO - handle_view_pending_withdrawals completed in 0.942s
2025-07-20 16:58:19,067 - handlers.user_handlers - INFO - Custom referral parameter used: from_channel -> User ID: **********
2025-07-20 16:58:46,070 - handlers.admin_handlers - ERROR - Error in handle_withdrawal_history: Can't parse entities: unsupported start tag "3" at byte offset 745
2025-07-20 16:58:46,439 - handlers.callback_handlers - ERROR - Error in callback handler for data 'withdrawalHistory': Query is too old and response timeout expired or query id is invalid
2025-07-20 16:58:46,810 - __main__ - ERROR - Error in callback handler: Query is too old and response timeout expired or query id is invalid
2025-07-20 16:58:47,630 - handlers.callback_handlers - ERROR - Error in callback handler for data 'myWallet': Query is too old and response timeout expired or query id is invalid
2025-07-20 16:58:47,995 - __main__ - ERROR - Error in callback handler: Query is too old and response timeout expired or query id is invalid
2025-07-20 16:58:48,765 - handlers.callback_handlers - ERROR - Error in callback handler for data 'myWallet': Query is too old and response timeout expired or query id is invalid
2025-07-20 16:58:49,139 - __main__ - ERROR - Error in callback handler: Query is too old and response timeout expired or query id is invalid
2025-07-20 16:58:49,636 - handlers.user_handlers - INFO - Custom referral parameter used: from_channel -> User ID: **********
2025-07-20 16:58:51,487 - handlers.callback_handlers - ERROR - Error in callback handler for data 'cashOut': Query is too old and response timeout expired or query id is invalid
2025-07-20 16:58:51,858 - __main__ - ERROR - Error in callback handler: Query is too old and response timeout expired or query id is invalid
2025-07-20 16:59:03,864 - handlers.admin_handlers - ERROR - Error in handle_withdrawal_history: Can't parse entities: unsupported start tag "3" at byte offset 745
2025-07-20 16:59:04,262 - handlers.callback_handlers - ERROR - Error in callback handler for data 'withdrawalHistory': Query is too old and response timeout expired or query id is invalid
2025-07-20 16:59:04,627 - __main__ - ERROR - Error in callback handler: Query is too old and response timeout expired or query id is invalid
2025-07-20 16:59:05,406 - handlers.callback_handlers - ERROR - Error in callback handler for data 'myWallet': Query is too old and response timeout expired or query id is invalid
2025-07-20 16:59:05,765 - __main__ - ERROR - Error in callback handler: Query is too old and response timeout expired or query id is invalid
2025-07-20 16:59:06,499 - handlers.callback_handlers - ERROR - Error in callback handler for data 'cashOut': Query is too old and response timeout expired or query id is invalid
2025-07-20 16:59:06,895 - __main__ - ERROR - Error in callback handler: Query is too old and response timeout expired or query id is invalid
2025-07-20 16:59:07,723 - handlers.callback_handlers - ERROR - Error in callback handler for data 'myWallet': Query is too old and response timeout expired or query id is invalid
2025-07-20 16:59:08,086 - __main__ - ERROR - Error in callback handler: Query is too old and response timeout expired or query id is invalid
2025-07-20 16:59:08,894 - handlers.callback_handlers - ERROR - Error in callback handler for data 'myWallet': Query is too old and response timeout expired or query id is invalid
2025-07-20 16:59:09,269 - __main__ - ERROR - Error in callback handler: Query is too old and response timeout expired or query id is invalid
2025-07-20 16:59:10,157 - handlers.callback_handlers - ERROR - Error in callback handler for data 'myWallet': Query is too old and response timeout expired or query id is invalid
2025-07-20 16:59:10,530 - __main__ - ERROR - Error in callback handler: Query is too old and response timeout expired or query id is invalid
2025-07-20 16:59:11,264 - handlers.callback_handlers - ERROR - Error in callback handler for data 'myWallet': Query is too old and response timeout expired or query id is invalid
2025-07-20 16:59:11,663 - __main__ - ERROR - Error in callback handler: Query is too old and response timeout expired or query id is invalid
2025-07-20 16:59:12,392 - handlers.callback_handlers - ERROR - Error in callback handler for data 'cashOut': Query is too old and response timeout expired or query id is invalid
2025-07-20 16:59:12,772 - __main__ - ERROR - Error in callback handler: Query is too old and response timeout expired or query id is invalid
2025-07-20 16:59:25,057 - handlers.admin_handlers - ERROR - Error in handle_withdrawal_history: Can't parse entities: unsupported start tag "3" at byte offset 745
2025-07-20 16:59:25,432 - handlers.callback_handlers - ERROR - Error in callback handler for data 'withdrawalHistory': Query is too old and response timeout expired or query id is invalid
2025-07-20 16:59:25,810 - __main__ - ERROR - Error in callback handler: Query is too old and response timeout expired or query id is invalid
2025-07-20 16:59:26,655 - handlers.callback_handlers - ERROR - Error in callback handler for data 'extraRewards': Query is too old and response timeout expired or query id is invalid
2025-07-20 16:59:27,086 - __main__ - ERROR - Error in callback handler: Query is too old and response timeout expired or query id is invalid
2025-07-20 16:59:29,867 - handlers.callback_handlers - ERROR - Error in callback handler for data 'myWallet': Query is too old and response timeout expired or query id is invalid
2025-07-20 16:59:30,249 - __main__ - ERROR - Error in callback handler: Query is too old and response timeout expired or query id is invalid
2025-07-20 16:59:58,677 - handlers.user_handlers - INFO - Custom referral parameter used: from_channel -> User ID: **********
2025-07-20 17:00:10,992 - handlers.user_handlers - INFO - Custom referral parameter used: from_channel -> User ID: **********
2025-07-20 17:00:37,982 - handlers.admin_handlers - ERROR - Error in handle_withdrawal_history: Can't parse entities: unsupported start tag "3" at byte offset 745
2025-07-20 17:00:38,341 - handlers.callback_handlers - ERROR - Error in callback handler for data 'withdrawalHistory': Query is too old and response timeout expired or query id is invalid
2025-07-20 17:00:38,698 - __main__ - ERROR - Error in callback handler: Query is too old and response timeout expired or query id is invalid
2025-07-20 17:00:52,816 - handlers.admin_handlers - ERROR - Error in handle_withdrawal_history: Can't parse entities: unsupported start tag "3" at byte offset 745
2025-07-20 17:00:53,270 - handlers.callback_handlers - ERROR - Error in callback handler for data 'withdrawalHistory': Query is too old and response timeout expired or query id is invalid
2025-07-20 17:00:53,637 - __main__ - ERROR - Error in callback handler: Query is too old and response timeout expired or query id is invalid
2025-07-20 17:00:54,464 - handlers.callback_handlers - ERROR - Error in callback handler for data 'checkSubscription': Query is too old and response timeout expired or query id is invalid
2025-07-20 17:00:54,828 - __main__ - ERROR - Error in callback handler: Query is too old and response timeout expired or query id is invalid
2025-07-20 17:00:57,235 - services.user_service - INFO - Created new user: 7301283932 (Mr)
2025-07-20 17:00:58,414 - handlers.user_handlers - ERROR - Error sending new user notification: Forbidden: bot is not a member of the channel chat
2025-07-20 17:00:59,680 - handlers.user_handlers - INFO - Notification sent to referrer 7589328962 about new user Mr (ID: 7301283932): success
2025-07-20 17:01:43,187 - handlers.admin_handlers - ERROR - Error in handle_withdrawal_history: Can't parse entities: unsupported start tag "3" at byte offset 745
2025-07-20 17:01:45,363 - services.user_service - INFO - Created new user: ********** (Aman)
2025-07-20 17:01:46,499 - handlers.user_handlers - ERROR - Error sending new user notification: Forbidden: bot is not a member of the channel chat
2025-07-20 17:01:47,723 - handlers.user_handlers - INFO - Notification sent to referrer ********** about new user Aman (ID: **********): success
2025-07-20 17:01:49,329 - handlers.user_handlers - INFO - Custom referral parameter used: from_channel -> User ID: **********
2025-07-20 17:01:50,945 - handlers.user_handlers - INFO - Custom referral parameter used: from_channel -> User ID: **********
2025-07-20 17:01:56,338 - services.user_service - INFO - Created new user: 7606666097 (AYUSH)
2025-07-20 17:01:57,694 - handlers.user_handlers - ERROR - Error sending new user notification: Forbidden: bot is not a member of the channel chat
2025-07-20 17:01:59,073 - handlers.user_handlers - INFO - Notification sent to referrer ********** about new user AYUSH (ID: 7606666097): success
2025-07-20 17:02:06,985 - handlers.user_handlers - ERROR - Error in handle_joined_channel: Query is too old and response timeout expired or query id is invalid
2025-07-20 17:02:07,746 - handlers.callback_handlers - ERROR - Error in callback handler for data 'joined': Query is too old and response timeout expired or query id is invalid
2025-07-20 17:02:08,114 - __main__ - ERROR - Error in callback handler: Query is too old and response timeout expired or query id is invalid
2025-07-20 17:02:08,865 - handlers.callback_handlers - ERROR - Error in callback handler for data 'withdrawalHistory': Query is too old and response timeout expired or query id is invalid
2025-07-20 17:02:09,232 - __main__ - ERROR - Error in callback handler: Query is too old and response timeout expired or query id is invalid
2025-07-20 17:02:16,097 - handlers.user_handlers - INFO - Custom referral parameter used: from_channel -> User ID: **********
2025-07-20 17:02:25,323 - handlers.callback_handlers - ERROR - Error in smart navigation: Message is not modified: specified new message content and reply markup are exactly the same as a current content and reply markup of the message
2025-07-20 17:02:29,998 - services.referral_service - INFO - Referral reward processed: ********** earned ₹4 for referring AYUSH
2025-07-20 17:02:34,294 - handlers.user_handlers - INFO - Custom referral parameter used: from_channel -> User ID: **********
2025-07-20 17:02:54,855 - handlers.session_handlers - INFO - === SESSION MESSAGE HANDLER for user **********, step: set_account_name ===
2025-07-20 17:02:54,856 - handlers.session_handlers - INFO - Session data: {'_id': ObjectId('687cd1ca7104627ee203493a'), 'user_id': **********, 'created_at': **********, 'data': {}, 'step': 'set_account_name', 'updated_at': **********}
2025-07-20 17:02:54,856 - handlers.session_handlers - INFO - Message has text: True
2025-07-20 17:02:54,856 - handlers.session_handlers - INFO - Message has photo: False
2025-07-20 17:02:54,857 - handlers.session_handlers - INFO - Message has video: False
2025-07-20 17:02:54,857 - handlers.session_handlers - INFO - Message has document: False
2025-07-20 17:02:54,857 - handlers.session_handlers - INFO - Message text: 'Name:piyush kumar gunjan
IFSC :UBIN0542784
Email:<EMAIL>
Account no :***************
Mobile :**********'
2025-07-20 17:03:08,356 - services.referral_service - INFO - Referral reward processed: ********** earned ₹1 for referring Aman
2025-07-20 17:03:11,292 - handlers.session_handlers - INFO - === SESSION MESSAGE HANDLER for user **********, step: set_account_name ===
2025-07-20 17:03:11,292 - handlers.session_handlers - INFO - Session data: {'_id': ObjectId('687cd1ca7104627ee203493a'), 'user_id': **********, 'created_at': **********, 'data': {}, 'step': 'set_account_name', 'updated_at': **********}
2025-07-20 17:03:11,292 - handlers.session_handlers - INFO - Message has text: True
2025-07-20 17:03:11,292 - handlers.session_handlers - INFO - Message has photo: False
2025-07-20 17:03:11,292 - handlers.session_handlers - INFO - Message has video: False
2025-07-20 17:03:11,292 - handlers.session_handlers - INFO - Message has document: False
2025-07-20 17:03:11,292 - handlers.session_handlers - INFO - Message text: 'Name:piyush gunjan
IFSC :UBIN0542784
Email:<EMAIL>
Account no :***************
Mobile :**********'
2025-07-20 17:03:22,390 - services.referral_service - INFO - Referral reward processed: ********** earned ₹1 for referring Tayana
2025-07-20 17:03:29,096 - handlers.session_handlers - INFO - === SESSION MESSAGE HANDLER for user **********, step: set_account_name ===
2025-07-20 17:03:29,097 - handlers.session_handlers - INFO - Session data: {'_id': ObjectId('687cd1ca7104627ee203493a'), 'user_id': **********, 'created_at': **********, 'data': {}, 'step': 'set_account_name', 'updated_at': **********}
2025-07-20 17:03:29,097 - handlers.session_handlers - INFO - Message has text: True
2025-07-20 17:03:29,097 - handlers.session_handlers - INFO - Message has photo: False
2025-07-20 17:03:29,097 - handlers.session_handlers - INFO - Message has video: False
2025-07-20 17:03:29,098 - handlers.session_handlers - INFO - Message has document: False
2025-07-20 17:03:29,098 - handlers.session_handlers - INFO - Message text: 'Name:piyush
IFSC :UBIN0542784
Email:<EMAIL>
Account no :***************
Mobile :**********'
2025-07-20 17:03:43,531 - handlers.callback_handlers - ERROR - Error in _handle_withdraw_amount: Message is not modified: specified new message content and reply markup are exactly the same as a current content and reply markup of the message
2025-07-20 17:03:48,068 - handlers.callback_handlers - ERROR - Error in smart navigation: Message is not modified: specified new message content and reply markup are exactly the same as a current content and reply markup of the message
2025-07-20 17:03:51,546 - handlers.session_handlers - INFO - === SESSION MESSAGE HANDLER for user **********, step: set_account_name ===
2025-07-20 17:03:51,547 - handlers.session_handlers - INFO - Session data: {'_id': ObjectId('687cd1ca7104627ee203493a'), 'user_id': **********, 'created_at': **********, 'data': {}, 'step': 'set_account_name', 'updated_at': **********}
2025-07-20 17:03:51,547 - handlers.session_handlers - INFO - Message has text: True
2025-07-20 17:03:51,547 - handlers.session_handlers - INFO - Message has photo: False
2025-07-20 17:03:51,547 - handlers.session_handlers - INFO - Message has video: False
2025-07-20 17:03:51,547 - handlers.session_handlers - INFO - Message has document: False
2025-07-20 17:03:51,547 - handlers.session_handlers - INFO - Message text: 'Name:Piyush Gunjan
IFSC :UBIN0542784
Email:<EMAIL>
Account no :***************
Mobile :**********'
2025-07-20 17:04:28,808 - services.user_service - INFO - Created new user: ********** (Danial)
2025-07-20 17:04:30,298 - handlers.user_handlers - ERROR - Error sending new user notification: Forbidden: bot is not a member of the channel chat
2025-07-20 17:04:31,850 - handlers.user_handlers - INFO - Notification sent to referrer ********** about new user Danial (ID: **********): success
2025-07-20 17:04:54,062 - handlers.session_handlers - INFO - === SESSION MESSAGE HANDLER for user **********, step: set_account_email ===
2025-07-20 17:04:54,062 - handlers.session_handlers - INFO - Session data: {'_id': ObjectId('687cd1ca7104627ee203493a'), 'user_id': **********, 'created_at': **********, 'data': {}, 'step': 'set_account_email', 'updated_at': **********}
2025-07-20 17:04:54,063 - handlers.session_handlers - INFO - Message has text: True
2025-07-20 17:04:54,063 - handlers.session_handlers - INFO - Message has photo: False
2025-07-20 17:04:54,063 - handlers.session_handlers - INFO - Message has video: False
2025-07-20 17:04:54,063 - handlers.session_handlers - INFO - Message has document: False
2025-07-20 17:04:54,063 - handlers.session_handlers - INFO - Message text: 'Name:Piyush doe
IFSC :UBIN0542784
Email:<EMAIL>
Account no :***************
Mobile :**********'
2025-07-20 17:05:16,363 - services.referral_service - INFO - Referral reward processed: ********** earned ₹2 for referring Danial
2025-07-20 17:05:18,544 - services.user_service - INFO - Created new user: ********** (Sunny)
2025-07-20 17:05:19,818 - handlers.user_handlers - ERROR - Error sending new user notification: Forbidden: bot is not a member of the channel chat
2025-07-20 17:05:22,038 - handlers.user_handlers - INFO - Notification sent to referrer ********** about new user Sunny (ID: **********): success
2025-07-20 17:05:38,288 - handlers.callback_handlers - ERROR - Error in _handle_set_account_field: Message is not modified: specified new message content and reply markup are exactly the same as a current content and reply markup of the message
2025-07-20 17:05:38,868 - services.user_service - INFO - Created new user: ********** (Sachin)
2025-07-20 17:05:40,391 - handlers.user_handlers - ERROR - Error sending new user notification: Forbidden: bot is not a member of the channel chat
2025-07-20 17:05:41,784 - handlers.user_handlers - INFO - Notification sent to referrer ********** about new user Sachin (ID: **********): success
2025-07-20 17:05:44,738 - handlers.callback_handlers - ERROR - Error in _handle_set_account_field: Message is not modified: specified new message content and reply markup are exactly the same as a current content and reply markup of the message
2025-07-20 17:05:53,090 - handlers.session_handlers - INFO - === SESSION MESSAGE HANDLER for user **********, step: set_account_name ===
2025-07-20 17:05:53,090 - handlers.session_handlers - INFO - Session data: {'_id': ObjectId('687cd1ca7104627ee203493a'), 'user_id': **********, 'created_at': **********, 'data': {}, 'step': 'set_account_name', 'updated_at': **********}
2025-07-20 17:05:53,090 - handlers.session_handlers - INFO - Message has text: True
2025-07-20 17:05:53,091 - handlers.session_handlers - INFO - Message has photo: False
2025-07-20 17:05:53,091 - handlers.session_handlers - INFO - Message has video: False
2025-07-20 17:05:53,091 - handlers.session_handlers - INFO - Message has document: False
2025-07-20 17:05:53,091 - handlers.session_handlers - INFO - Message text: 'Name:Piyush Kumar Gunjan
IFSC :UBIN0542784
Email:<EMAIL>
Account no :***************
Mobile :**********'
2025-07-20 17:05:57,765 - services.referral_service - INFO - Referral reward processed: ********** earned ₹2 for referring Sunny
2025-07-20 17:06:03,011 - handlers.callback_handlers - ERROR - Error in smart navigation: Message is not modified: specified new message content and reply markup are exactly the same as a current content and reply markup of the message
2025-07-20 17:06:05,063 - handlers.callback_handlers - ERROR - Error in _handle_set_account_field: Message is not modified: specified new message content and reply markup are exactly the same as a current content and reply markup of the message
2025-07-20 17:06:11,831 - handlers.callback_handlers - ERROR - Error in _handle_custom_referral_link: Message is not modified: specified new message content and reply markup are exactly the same as a current content and reply markup of the message
2025-07-20 17:06:25,361 - handlers.user_handlers - ERROR - Error in handle_joined_channel: Query is too old and response timeout expired or query id is invalid
2025-07-20 17:06:34,654 - handlers.user_handlers - ERROR - Error in handle_joined_channel: Query is too old and response timeout expired or query id is invalid
2025-07-20 17:06:35,030 - handlers.callback_handlers - ERROR - Error in callback handler for data 'joined': Message is not modified: specified new message content and reply markup are exactly the same as a current content and reply markup of the message
2025-07-20 17:06:35,410 - __main__ - ERROR - Error in callback handler: Query is too old and response timeout expired or query id is invalid
2025-07-20 17:06:46,847 - handlers.user_handlers - ERROR - Error in handle_joined_channel: Query is too old and response timeout expired or query id is invalid
2025-07-20 17:06:52,272 - handlers.callback_handlers - ERROR - Error in callback handler for data 'joined': Query is too old and response timeout expired or query id is invalid
2025-07-20 17:06:52,645 - __main__ - ERROR - Error in callback handler: Query is too old and response timeout expired or query id is invalid
2025-07-20 17:06:53,391 - handlers.callback_handlers - ERROR - Error in callback handler for data 'joined': Query is too old and response timeout expired or query id is invalid
2025-07-20 17:06:53,762 - __main__ - ERROR - Error in callback handler: Query is too old and response timeout expired or query id is invalid
2025-07-20 17:06:54,525 - handlers.callback_handlers - ERROR - Error in callback handler for data 'joined': Query is too old and response timeout expired or query id is invalid
2025-07-20 17:06:54,897 - __main__ - ERROR - Error in callback handler: Query is too old and response timeout expired or query id is invalid
2025-07-20 17:06:55,652 - handlers.callback_handlers - ERROR - Error in callback handler for data 'joined': Query is too old and response timeout expired or query id is invalid
2025-07-20 17:06:56,025 - __main__ - ERROR - Error in callback handler: Query is too old and response timeout expired or query id is invalid
2025-07-20 17:06:56,769 - handlers.callback_handlers - ERROR - Error in callback handler for data 'setAccountInfo': Query is too old and response timeout expired or query id is invalid
2025-07-20 17:06:57,140 - __main__ - ERROR - Error in callback handler: Query is too old and response timeout expired or query id is invalid
2025-07-20 17:06:57,639 - handlers.user_handlers - INFO - Custom referral parameter used: from_channel -> User ID: **********
2025-07-20 17:06:59,609 - handlers.callback_handlers - ERROR - Error in callback handler for data 'joined': Query is too old and response timeout expired or query id is invalid
2025-07-20 17:06:59,981 - __main__ - ERROR - Error in callback handler: Query is too old and response timeout expired or query id is invalid
2025-07-20 17:07:03,011 - handlers.callback_handlers - ERROR - Error in _handle_check_subscription: Query is too old and response timeout expired or query id is invalid
2025-07-20 17:07:03,386 - handlers.callback_handlers - ERROR - Error in callback handler for data 'checkSubscription': Query is too old and response timeout expired or query id is invalid
2025-07-20 17:07:03,762 - __main__ - ERROR - Error in callback handler: Query is too old and response timeout expired or query id is invalid
2025-07-20 17:07:04,512 - handlers.callback_handlers - ERROR - Error in callback handler for data 'promotionReport': Query is too old and response timeout expired or query id is invalid
2025-07-20 17:07:04,883 - __main__ - ERROR - Error in callback handler: Query is too old and response timeout expired or query id is invalid
2025-07-20 17:07:08,983 - handlers.callback_handlers - ERROR - Error in callback handler for data 'setAccountInfo': Query is too old and response timeout expired or query id is invalid
2025-07-20 17:07:09,366 - __main__ - ERROR - Error in callback handler: Query is too old and response timeout expired or query id is invalid
2025-07-20 17:07:10,134 - handlers.callback_handlers - ERROR - Error in callback handler for data 'setAccountInfo': Query is too old and response timeout expired or query id is invalid
2025-07-20 17:07:10,514 - __main__ - ERROR - Error in callback handler: Query is too old and response timeout expired or query id is invalid
2025-07-20 17:07:13,646 - handlers.callback_handlers - ERROR - Error in callback handler for data 'myWallet': Query is too old and response timeout expired or query id is invalid
2025-07-20 17:07:14,044 - __main__ - ERROR - Error in callback handler: Query is too old and response timeout expired or query id is invalid
2025-07-20 17:07:16,507 - handlers.callback_handlers - ERROR - Error in callback handler for data 'joined': Query is too old and response timeout expired or query id is invalid
2025-07-20 17:07:16,879 - __main__ - ERROR - Error in callback handler: Query is too old and response timeout expired or query id is invalid
2025-07-20 17:07:17,622 - handlers.callback_handlers - ERROR - Error in callback handler for data 'joined': Query is too old and response timeout expired or query id is invalid
2025-07-20 17:07:18,006 - __main__ - ERROR - Error in callback handler: Query is too old and response timeout expired or query id is invalid
2025-07-20 17:07:18,753 - handlers.callback_handlers - ERROR - Error in callback handler for data 'myWallet': Query is too old and response timeout expired or query id is invalid
2025-07-20 17:07:19,130 - __main__ - ERROR - Error in callback handler: Query is too old and response timeout expired or query id is invalid
2025-07-20 17:07:21,720 - handlers.callback_handlers - ERROR - Error in _handle_check_subscription: Query is too old and response timeout expired or query id is invalid
2025-07-20 17:07:22,172 - handlers.callback_handlers - ERROR - Error in callback handler for data 'checkSubscription': Query is too old and response timeout expired or query id is invalid
2025-07-20 17:07:22,694 - __main__ - ERROR - Error in callback handler: Query is too old and response timeout expired or query id is invalid
2025-07-20 17:07:23,443 - handlers.callback_handlers - ERROR - Error in callback handler for data 'myWallet': Query is too old and response timeout expired or query id is invalid
2025-07-20 17:07:23,817 - __main__ - ERROR - Error in callback handler: Query is too old and response timeout expired or query id is invalid
2025-07-20 17:07:24,654 - handlers.callback_handlers - ERROR - Error in callback handler for data 'myWallet': Query is too old and response timeout expired or query id is invalid
2025-07-20 17:07:25,038 - __main__ - ERROR - Error in callback handler: Query is too old and response timeout expired or query id is invalid
2025-07-20 17:07:25,840 - handlers.callback_handlers - ERROR - Error in callback handler for data 'myWallet': Query is too old and response timeout expired or query id is invalid
2025-07-20 17:07:26,201 - __main__ - ERROR - Error in callback handler: Query is too old and response timeout expired or query id is invalid
2025-07-20 17:07:26,939 - handlers.callback_handlers - ERROR - Error in callback handler for data 'joined': Query is too old and response timeout expired or query id is invalid
2025-07-20 17:07:27,310 - __main__ - ERROR - Error in callback handler: Query is too old and response timeout expired or query id is invalid
2025-07-20 17:07:28,064 - handlers.callback_handlers - ERROR - Error in callback handler for data 'set name': Query is too old and response timeout expired or query id is invalid
2025-07-20 17:07:28,450 - __main__ - ERROR - Error in callback handler: Query is too old and response timeout expired or query id is invalid
2025-07-20 17:07:29,395 - handlers.callback_handlers - ERROR - Error in callback handler for data 'set ifsc': Query is too old and response timeout expired or query id is invalid
2025-07-20 17:07:29,766 - __main__ - ERROR - Error in callback handler: Query is too old and response timeout expired or query id is invalid
2025-07-20 17:07:30,490 - handlers.callback_handlers - ERROR - Error in callback handler for data 'checkSubscription': Query is too old and response timeout expired or query id is invalid
2025-07-20 17:07:30,861 - __main__ - ERROR - Error in callback handler: Query is too old and response timeout expired or query id is invalid
2025-07-20 17:07:31,586 - handlers.callback_handlers - ERROR - Error in callback handler for data 'set email': Query is too old and response timeout expired or query id is invalid
2025-07-20 17:07:31,952 - __main__ - ERROR - Error in callback handler: Query is too old and response timeout expired or query id is invalid
2025-07-20 17:07:32,682 - handlers.callback_handlers - ERROR - Error in callback handler for data 'set account_number': Query is too old and response timeout expired or query id is invalid
2025-07-20 17:07:33,046 - __main__ - ERROR - Error in callback handler: Query is too old and response timeout expired or query id is invalid
2025-07-20 17:07:33,781 - handlers.callback_handlers - ERROR - Error in callback handler for data 'set mobile_number': Query is too old and response timeout expired or query id is invalid
2025-07-20 17:07:34,158 - __main__ - ERROR - Error in callback handler: Query is too old and response timeout expired or query id is invalid
2025-07-20 17:07:36,905 - __main__ - INFO - Connecting to MongoDB...
2025-07-20 17:07:37,065 - handlers.callback_handlers - ERROR - Error in callback handler for data 'joined': Query is too old and response timeout expired or query id is invalid
2025-07-20 17:07:37,436 - __main__ - ERROR - Error in callback handler: Query is too old and response timeout expired or query id is invalid
2025-07-20 17:07:48,638 - services.referral_service - INFO - Referral reward processed: ********** earned ₹5 for referring Sachin
2025-07-20 17:07:49,657 - config.database - INFO - Successfully connected to MongoDB: referral_bot
2025-07-20 17:07:51,295 - config.database - INFO - Database collections and indexes initialized successfully
2025-07-20 17:07:51,296 - __main__ - INFO - Initializing Telegram bot...
2025-07-20 17:07:51,698 - __main__ - INFO - All handlers registered successfully
2025-07-20 17:07:52,580 - services.admin_service - INFO - Initialized admin settings for 4 admins
2025-07-20 17:07:52,623 - __main__ - INFO - Bot info updated: @InstantoPayBot (InstantoPay)
2025-07-20 17:07:52,623 - __main__ - INFO - Admin settings and level rewards system initialized
2025-07-20 17:07:52,623 - __main__ - INFO - Bot initialization completed successfully
2025-07-20 17:07:52,624 - __main__ - INFO - Starting bot polling...
2025-07-20 17:07:52,829 - apscheduler.scheduler - INFO - Scheduler started
2025-07-20 17:07:53,033 - __main__ - INFO - Bot is now running and polling for updates...
2025-07-20 17:07:53,670 - handlers.session_handlers - INFO - === SESSION MESSAGE HANDLER for user **********, step: set_account_number ===
2025-07-20 17:07:53,670 - handlers.session_handlers - INFO - Session data: {'_id': ObjectId('687cd1ca7104627ee203493a'), 'user_id': **********, 'created_at': **********, 'data': {}, 'step': 'set_account_number', 'updated_at': **********}
2025-07-20 17:07:53,671 - handlers.session_handlers - INFO - Message has text: True
2025-07-20 17:07:53,671 - handlers.session_handlers - INFO - Message has photo: False
2025-07-20 17:07:53,671 - handlers.session_handlers - INFO - Message has video: False
2025-07-20 17:07:53,671 - handlers.session_handlers - INFO - Message has document: False
2025-07-20 17:07:53,672 - handlers.session_handlers - INFO - Message text: 'Name: Piyush Kumar Gunjan
IFSC : UBIN0542784
Email:  <EMAIL>
Account no : ***************
Mobile : **********'
2025-07-20 17:07:53,756 - handlers.user_handlers - INFO - Custom referral parameter used: from_channel -> User ID: **********
2025-07-20 17:07:54,246 - handlers.user_handlers - INFO - Custom referral parameter used: from_channel -> User ID: **********
2025-07-20 17:07:54,915 - telegram.ext.Updater - ERROR - Error while getting Updates: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-20 17:07:54,916 - telegram.ext.Updater - ERROR - Exception happened while polling for updates.
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_updater.py", line 715, in _network_loop_retry
    if not await action_cb():
           ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_updater.py", line 384, in polling_action_cb
    raise exc
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_updater.py", line 373, in polling_action_cb
    updates = await self.bot.get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_extbot.py", line 618, in get_updates
    updates = await super().get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 542, in decorator
    result = await func(self, *args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 4164, in get_updates
    await self._post(
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 630, in _post
    return await self._do_post(
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_extbot.py", line 347, in _do_post
    return await super()._do_post(
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 658, in _do_post
    return await request.post(
           ^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\request\_baserequest.py", line 200, in post
    result = await self._request_wrapper(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\request\_baserequest.py", line 381, in _request_wrapper
    raise Conflict(message)
telegram.error.Conflict: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-20 17:07:58,110 - telegram.ext.Updater - ERROR - Error while getting Updates: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-20 17:07:58,110 - telegram.ext.Updater - ERROR - Exception happened while polling for updates.
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_updater.py", line 715, in _network_loop_retry
    if not await action_cb():
           ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_updater.py", line 384, in polling_action_cb
    raise exc
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_updater.py", line 373, in polling_action_cb
    updates = await self.bot.get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_extbot.py", line 618, in get_updates
    updates = await super().get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 542, in decorator
    result = await func(self, *args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 4164, in get_updates
    await self._post(
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 630, in _post
    return await self._do_post(
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_extbot.py", line 347, in _do_post
    return await super()._do_post(
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 658, in _do_post
    return await request.post(
           ^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\request\_baserequest.py", line 200, in post
    result = await self._request_wrapper(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\request\_baserequest.py", line 381, in _request_wrapper
    raise Conflict(message)
telegram.error.Conflict: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-20 17:07:58,338 - handlers.callback_handlers - ERROR - Error in smart navigation: Message is not modified: specified new message content and reply markup are exactly the same as a current content and reply markup of the message
2025-07-20 17:08:06,093 - handlers.callback_handlers - ERROR - Error in smart navigation: Message is not modified: specified new message content and reply markup are exactly the same as a current content and reply markup of the message
2025-07-20 17:08:07,949 - handlers.callback_handlers - ERROR - Error in smart navigation: Message is not modified: specified new message content and reply markup are exactly the same as a current content and reply markup of the message
2025-07-20 17:08:08,347 - telegram.ext.Updater - ERROR - Error while getting Updates: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-20 17:08:08,348 - telegram.ext.Updater - ERROR - Exception happened while polling for updates.
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_updater.py", line 715, in _network_loop_retry
    if not await action_cb():
           ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_updater.py", line 384, in polling_action_cb
    raise exc
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_updater.py", line 373, in polling_action_cb
    updates = await self.bot.get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_extbot.py", line 618, in get_updates
    updates = await super().get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 542, in decorator
    result = await func(self, *args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 4164, in get_updates
    await self._post(
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 630, in _post
    return await self._do_post(
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_extbot.py", line 347, in _do_post
    return await super()._do_post(
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 658, in _do_post
    return await request.post(
           ^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\request\_baserequest.py", line 200, in post
    result = await self._request_wrapper(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\request\_baserequest.py", line 381, in _request_wrapper
    raise Conflict(message)
telegram.error.Conflict: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-20 17:08:09,959 - handlers.callback_handlers - ERROR - Error in smart navigation: Message is not modified: specified new message content and reply markup are exactly the same as a current content and reply markup of the message
2025-07-20 17:08:11,611 - telegram.ext.Updater - ERROR - Error while getting Updates: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-20 17:08:11,611 - telegram.ext.Updater - ERROR - Exception happened while polling for updates.
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_updater.py", line 715, in _network_loop_retry
    if not await action_cb():
           ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_updater.py", line 384, in polling_action_cb
    raise exc
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_updater.py", line 373, in polling_action_cb
    updates = await self.bot.get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_extbot.py", line 618, in get_updates
    updates = await super().get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 542, in decorator
    result = await func(self, *args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 4164, in get_updates
    await self._post(
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 630, in _post
    return await self._do_post(
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_extbot.py", line 347, in _do_post
    return await super()._do_post(
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 658, in _do_post
    return await request.post(
           ^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\request\_baserequest.py", line 200, in post
    result = await self._request_wrapper(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\request\_baserequest.py", line 381, in _request_wrapper
    raise Conflict(message)
telegram.error.Conflict: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-20 17:08:11,722 - handlers.callback_handlers - ERROR - Error in smart navigation: Message is not modified: specified new message content and reply markup are exactly the same as a current content and reply markup of the message
2025-07-20 17:08:11,867 - handlers.callback_handlers - ERROR - Error in smart navigation: Message is not modified: specified new message content and reply markup are exactly the same as a current content and reply markup of the message
2025-07-20 17:08:13,822 - handlers.callback_handlers - ERROR - Error in smart navigation: Message is not modified: specified new message content and reply markup are exactly the same as a current content and reply markup of the message
2025-07-20 17:08:15,553 - handlers.session_handlers - INFO - === SESSION MESSAGE HANDLER for user **********, step: set_account_number ===
2025-07-20 17:08:15,554 - handlers.session_handlers - INFO - Session data: {'_id': ObjectId('687cd1ca7104627ee203493a'), 'user_id': **********, 'created_at': **********, 'data': {}, 'step': 'set_account_number', 'updated_at': **********}
2025-07-20 17:08:15,554 - handlers.session_handlers - INFO - Message has text: True
2025-07-20 17:08:15,554 - handlers.session_handlers - INFO - Message has photo: False
2025-07-20 17:08:15,554 - handlers.session_handlers - INFO - Message has video: False
2025-07-20 17:08:15,555 - handlers.session_handlers - INFO - Message has document: False
2025-07-20 17:08:15,555 - handlers.session_handlers - INFO - Message text: 'Name: Piyush Kumar Gunjan
IFSC : UBIN0542784
Email:  <EMAIL>
Account no :***************
Mobile : **********'
2025-07-20 17:08:16,068 - handlers.callback_handlers - ERROR - Error in smart navigation: Message is not modified: specified new message content and reply markup are exactly the same as a current content and reply markup of the message
2025-07-20 17:08:16,312 - telegram.ext.Updater - ERROR - Error while getting Updates: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-20 17:08:16,312 - telegram.ext.Updater - ERROR - Exception happened while polling for updates.
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_updater.py", line 715, in _network_loop_retry
    if not await action_cb():
           ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_updater.py", line 384, in polling_action_cb
    raise exc
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_updater.py", line 373, in polling_action_cb
    updates = await self.bot.get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_extbot.py", line 618, in get_updates
    updates = await super().get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 542, in decorator
    result = await func(self, *args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 4164, in get_updates
    await self._post(
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 630, in _post
    return await self._do_post(
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_extbot.py", line 347, in _do_post
    return await super()._do_post(
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 658, in _do_post
    return await request.post(
           ^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\request\_baserequest.py", line 200, in post
    result = await self._request_wrapper(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\request\_baserequest.py", line 381, in _request_wrapper
    raise Conflict(message)
telegram.error.Conflict: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-20 17:08:18,016 - telegram.ext.Updater - ERROR - Error while getting Updates: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-20 17:08:18,017 - telegram.ext.Updater - ERROR - Exception happened while polling for updates.
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_updater.py", line 715, in _network_loop_retry
    if not await action_cb():
           ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_updater.py", line 384, in polling_action_cb
    raise exc
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_updater.py", line 373, in polling_action_cb
    updates = await self.bot.get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_extbot.py", line 618, in get_updates
    updates = await super().get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 542, in decorator
    result = await func(self, *args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 4164, in get_updates
    await self._post(
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 630, in _post
    return await self._do_post(
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_extbot.py", line 347, in _do_post
    return await super()._do_post(
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 658, in _do_post
    return await request.post(
           ^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\request\_baserequest.py", line 200, in post
    result = await self._request_wrapper(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\request\_baserequest.py", line 381, in _request_wrapper
    raise Conflict(message)
telegram.error.Conflict: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-20 17:08:22,052 - services.user_service - INFO - Created new user: 1060159065 (rahul)
2025-07-20 17:08:22,712 - telegram.ext.Updater - ERROR - Error while getting Updates: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-20 17:08:22,713 - telegram.ext.Updater - ERROR - Exception happened while polling for updates.
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_updater.py", line 715, in _network_loop_retry
    if not await action_cb():
           ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_updater.py", line 384, in polling_action_cb
    raise exc
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_updater.py", line 373, in polling_action_cb
    updates = await self.bot.get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_extbot.py", line 618, in get_updates
    updates = await super().get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 542, in decorator
    result = await func(self, *args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 4164, in get_updates
    await self._post(
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 630, in _post
    return await self._do_post(
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_extbot.py", line 347, in _do_post
    return await super()._do_post(
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 658, in _do_post
    return await request.post(
           ^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\request\_baserequest.py", line 200, in post
    result = await self._request_wrapper(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\request\_baserequest.py", line 381, in _request_wrapper
    raise Conflict(message)
telegram.error.Conflict: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-20 17:08:23,174 - handlers.user_handlers - ERROR - Error sending new user notification: Forbidden: bot is not a member of the channel chat
2025-07-20 17:08:24,418 - telegram.ext.Updater - ERROR - Error while getting Updates: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-20 17:08:24,418 - telegram.ext.Updater - ERROR - Exception happened while polling for updates.
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_updater.py", line 715, in _network_loop_retry
    if not await action_cb():
           ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_updater.py", line 384, in polling_action_cb
    raise exc
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_updater.py", line 373, in polling_action_cb
    updates = await self.bot.get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_extbot.py", line 618, in get_updates
    updates = await super().get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 542, in decorator
    result = await func(self, *args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 4164, in get_updates
    await self._post(
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 630, in _post
    return await self._do_post(
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_extbot.py", line 347, in _do_post
    return await super()._do_post(
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 658, in _do_post
    return await request.post(
           ^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\request\_baserequest.py", line 200, in post
    result = await self._request_wrapper(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\request\_baserequest.py", line 381, in _request_wrapper
    raise Conflict(message)
telegram.error.Conflict: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-20 17:08:24,495 - handlers.user_handlers - INFO - Notification sent to referrer ********** about new user rahul (ID: 1060159065): success
2025-07-20 17:08:29,115 - telegram.ext.Updater - ERROR - Error while getting Updates: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-20 17:08:29,115 - telegram.ext.Updater - ERROR - Exception happened while polling for updates.
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_updater.py", line 715, in _network_loop_retry
    if not await action_cb():
           ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_updater.py", line 384, in polling_action_cb
    raise exc
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_updater.py", line 373, in polling_action_cb
    updates = await self.bot.get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_extbot.py", line 618, in get_updates
    updates = await super().get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 542, in decorator
    result = await func(self, *args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 4164, in get_updates
    await self._post(
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 630, in _post
    return await self._do_post(
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_extbot.py", line 347, in _do_post
    return await super()._do_post(
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 658, in _do_post
    return await request.post(
           ^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\request\_baserequest.py", line 200, in post
    result = await self._request_wrapper(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\request\_baserequest.py", line 381, in _request_wrapper
    raise Conflict(message)
telegram.error.Conflict: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-20 17:08:35,229 - handlers.callback_handlers - ERROR - Error in smart navigation: Message is not modified: specified new message content and reply markup are exactly the same as a current content and reply markup of the message
2025-07-20 17:08:35,998 - telegram.ext.Updater - ERROR - Error while getting Updates: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-20 17:08:35,998 - telegram.ext.Updater - ERROR - Exception happened while polling for updates.
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_updater.py", line 715, in _network_loop_retry
    if not await action_cb():
           ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_updater.py", line 384, in polling_action_cb
    raise exc
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_updater.py", line 373, in polling_action_cb
    updates = await self.bot.get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_extbot.py", line 618, in get_updates
    updates = await super().get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 542, in decorator
    result = await func(self, *args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 4164, in get_updates
    await self._post(
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 630, in _post
    return await self._do_post(
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_extbot.py", line 347, in _do_post
    return await super()._do_post(
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 658, in _do_post
    return await request.post(
           ^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\request\_baserequest.py", line 200, in post
    result = await self._request_wrapper(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\request\_baserequest.py", line 381, in _request_wrapper
    raise Conflict(message)
telegram.error.Conflict: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-20 17:08:38,684 - services.user_service - INFO - Created new user: 7100752177 (Sunny)
2025-07-20 17:08:40,594 - handlers.callback_handlers - ERROR - Error in smart navigation: Message is not modified: specified new message content and reply markup are exactly the same as a current content and reply markup of the message
2025-07-20 17:08:41,242 - telegram.ext.Updater - ERROR - Error while getting Updates: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-20 17:08:41,244 - telegram.ext.Updater - ERROR - Exception happened while polling for updates.
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_updater.py", line 715, in _network_loop_retry
    if not await action_cb():
           ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_updater.py", line 384, in polling_action_cb
    raise exc
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_updater.py", line 373, in polling_action_cb
    updates = await self.bot.get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_extbot.py", line 618, in get_updates
    updates = await super().get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 542, in decorator
    result = await func(self, *args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 4164, in get_updates
    await self._post(
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 630, in _post
    return await self._do_post(
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_extbot.py", line 347, in _do_post
    return await super()._do_post(
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 658, in _do_post
    return await request.post(
           ^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\request\_baserequest.py", line 200, in post
    result = await self._request_wrapper(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\request\_baserequest.py", line 381, in _request_wrapper
    raise Conflict(message)
telegram.error.Conflict: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-20 17:08:50,954 - handlers.session_handlers - INFO - === SESSION MESSAGE HANDLER for user **********, step: set_account_number ===
2025-07-20 17:08:50,954 - handlers.session_handlers - INFO - Session data: {'_id': ObjectId('687cd1ca7104627ee203493a'), 'user_id': **********, 'created_at': **********, 'data': {}, 'step': 'set_account_number', 'updated_at': **********}
2025-07-20 17:08:50,954 - handlers.session_handlers - INFO - Message has text: True
2025-07-20 17:08:50,955 - handlers.session_handlers - INFO - Message has photo: False
2025-07-20 17:08:50,955 - handlers.session_handlers - INFO - Message has video: False
2025-07-20 17:08:50,955 - handlers.session_handlers - INFO - Message has document: False
2025-07-20 17:08:50,955 - handlers.session_handlers - INFO - Message text: 'Name: Piyush Kumar Gunjan
IFSC : UBIN0542784
Email:  <EMAIL>
Account no : ***************
Mobile : **********'
2025-07-20 17:09:03,011 - services.referral_service - INFO - Referral reward processed: ********** earned ₹1 for referring Sunny
2025-07-20 17:09:11,352 - handlers.callback_handlers - INFO - Message contains media, sending new text message instead of editing
2025-07-20 17:09:28,201 - handlers.session_handlers - INFO - === SESSION MESSAGE HANDLER for user **********, step: set_account_number ===
2025-07-20 17:09:28,201 - handlers.session_handlers - INFO - Session data: {'_id': ObjectId('687cd1ca7104627ee203493a'), 'user_id': **********, 'created_at': **********, 'data': {}, 'step': 'set_account_number', 'updated_at': **********}
2025-07-20 17:09:28,201 - handlers.session_handlers - INFO - Message has text: True
2025-07-20 17:09:28,201 - handlers.session_handlers - INFO - Message has photo: False
2025-07-20 17:09:28,202 - handlers.session_handlers - INFO - Message has video: False
2025-07-20 17:09:28,202 - handlers.session_handlers - INFO - Message has document: False
2025-07-20 17:09:28,202 - handlers.session_handlers - INFO - Message text: 'Name: Piyush Kumar Gunjan
IFSC : UBIN0542784
Email:  <EMAIL>
Account no : **********
Mobile : **********'
2025-07-20 17:09:31,820 - handlers.session_handlers - INFO - === SESSION MESSAGE HANDLER for user **********, step: submit_task_screenshot ===
2025-07-20 17:09:31,821 - handlers.session_handlers - INFO - Session data: {'_id': ObjectId('687cd55f7104627ee2034944'), 'user_id': **********, 'created_at': **********, 'data': {'task_id': 'task_1752995334.fd08d118'}, 'step': 'submit_task_screenshot', 'updated_at': **********}
2025-07-20 17:09:31,821 - handlers.session_handlers - INFO - Message has text: False
2025-07-20 17:09:31,821 - handlers.session_handlers - INFO - Message has photo: True
2025-07-20 17:09:31,821 - handlers.session_handlers - INFO - Message has video: False
2025-07-20 17:09:31,821 - handlers.session_handlers - INFO - Message has document: False
2025-07-20 17:09:31,821 - handlers.session_handlers - INFO - Message text: 'None'
2025-07-20 17:09:33,225 - services.task_service - INFO - Created new task submission: submission_1753011573.7f44713c
2025-07-20 17:09:34,632 - handlers.user_handlers - INFO - Custom referral parameter used: from_channel -> User ID: **********
2025-07-20 17:09:59,716 - handlers.session_handlers - INFO - === SESSION MESSAGE HANDLER for user **********, step: set_account_number ===
2025-07-20 17:09:59,717 - handlers.session_handlers - INFO - Session data: {'_id': ObjectId('687cd1ca7104627ee203493a'), 'user_id': **********, 'created_at': **********, 'data': {}, 'step': 'set_account_number', 'updated_at': **********}
2025-07-20 17:09:59,717 - handlers.session_handlers - INFO - Message has text: True
2025-07-20 17:09:59,717 - handlers.session_handlers - INFO - Message has photo: False
2025-07-20 17:09:59,717 - handlers.session_handlers - INFO - Message has video: False
2025-07-20 17:09:59,718 - handlers.session_handlers - INFO - Message has document: False
2025-07-20 17:09:59,718 - handlers.session_handlers - INFO - Message text: 'Name: Piyush Kumar Gunjan
IFSC : UBIN0542784
Email:  <EMAIL>
Account no :  ***************
Mobile : **********'
2025-07-20 17:10:18,356 - handlers.callback_handlers - INFO - Message contains media, sending new text message instead of editing
2025-07-20 17:10:21,040 - handlers.callback_handlers - INFO - Message contains media, sending new text message instead of editing
2025-07-20 17:10:32,127 - __main__ - INFO - Received signal 2, initiating shutdown...
2025-07-20 17:10:32,127 - __main__ - INFO - Shutting down bot...
2025-07-20 17:10:32,740 - apscheduler.scheduler - INFO - Scheduler has been shut down
2025-07-20 17:10:32,792 - config.database - INFO - Disconnected from MongoDB
2025-07-20 17:10:32,792 - __main__ - INFO - Bot shutdown completed
2025-07-20 17:10:40,685 - __main__ - INFO - Connecting to MongoDB...
2025-07-20 17:10:53,520 - config.database - INFO - Successfully connected to MongoDB: referral_bot
2025-07-20 17:10:55,207 - config.database - INFO - Database collections and indexes initialized successfully
2025-07-20 17:10:55,208 - __main__ - INFO - Initializing Telegram bot...
2025-07-20 17:10:55,774 - __main__ - INFO - All handlers registered successfully
2025-07-20 17:10:56,653 - services.admin_service - INFO - Initialized admin settings for 4 admins
2025-07-20 17:10:56,695 - __main__ - INFO - Bot info updated: @InstantoPayBot (InstantoPay)
2025-07-20 17:10:56,695 - __main__ - INFO - Admin settings and level rewards system initialized
2025-07-20 17:10:56,695 - __main__ - INFO - Bot initialization completed successfully
2025-07-20 17:10:56,696 - __main__ - INFO - Starting bot polling...
2025-07-20 17:10:56,901 - apscheduler.scheduler - INFO - Scheduler started
2025-07-20 17:10:57,114 - __main__ - INFO - Bot is now running and polling for updates...
2025-07-20 17:10:58,301 - handlers.callback_handlers - ERROR - Error in internal callback handler for data 'levelRewards': Query is too old and response timeout expired or query id is invalid
2025-07-20 17:10:58,301 - handlers.callback_handlers - ERROR - Error in callback handler for data 'levelRewards': Query is too old and response timeout expired or query id is invalid
2025-07-20 17:10:59,039 - handlers.callback_handlers - ERROR - Error in internal callback handler for data 'myWallet': Query is too old and response timeout expired or query id is invalid
2025-07-20 17:10:59,039 - handlers.callback_handlers - ERROR - Error in callback handler for data 'myWallet': Query is too old and response timeout expired or query id is invalid
2025-07-20 17:10:59,451 - handlers.session_handlers - INFO - === SESSION MESSAGE HANDLER for user **********, step: submit_task_screenshot ===
2025-07-20 17:10:59,452 - handlers.session_handlers - INFO - Session data: {'_id': ObjectId('687cd4a57104627ee2034943'), 'user_id': **********, 'created_at': 1753011619, 'data': {'task_id': 'task_1752995334.fd08d118'}, 'step': 'submit_task_screenshot', 'updated_at': 1753011619}
2025-07-20 17:10:59,452 - handlers.session_handlers - INFO - Message has text: False
2025-07-20 17:10:59,453 - handlers.session_handlers - INFO - Message has photo: True
2025-07-20 17:10:59,453 - handlers.session_handlers - INFO - Message has video: False
2025-07-20 17:10:59,453 - handlers.session_handlers - INFO - Message has document: False
2025-07-20 17:10:59,453 - handlers.session_handlers - INFO - Message text: 'None'
2025-07-20 17:11:00,800 - services.task_service - INFO - Created new task submission: submission_1753011660.ed1bccc8
2025-07-20 17:11:02,391 - handlers.callback_handlers - ERROR - Error in internal callback handler for data 'taskRewards': Query is too old and response timeout expired or query id is invalid
2025-07-20 17:11:02,392 - handlers.callback_handlers - ERROR - Error in callback handler for data 'taskRewards': Query is too old and response timeout expired or query id is invalid
2025-07-20 17:11:03,139 - handlers.callback_handlers - ERROR - Error in internal callback handler for data 'levelRewards': Query is too old and response timeout expired or query id is invalid
2025-07-20 17:11:03,140 - handlers.callback_handlers - ERROR - Error in callback handler for data 'levelRewards': Query is too old and response timeout expired or query id is invalid
2025-07-20 17:11:19,786 - handlers.session_handlers - INFO - === SESSION MESSAGE HANDLER for user **********, step: set_account_number ===
2025-07-20 17:11:19,787 - handlers.session_handlers - INFO - Session data: {'_id': ObjectId('687cd1ca7104627ee203493a'), 'user_id': **********, 'created_at': **********, 'data': {}, 'step': 'set_account_number', 'updated_at': **********}
2025-07-20 17:11:19,787 - handlers.session_handlers - INFO - Message has text: True
2025-07-20 17:11:19,787 - handlers.session_handlers - INFO - Message has photo: False
2025-07-20 17:11:19,787 - handlers.session_handlers - INFO - Message has video: False
2025-07-20 17:11:19,787 - handlers.session_handlers - INFO - Message has document: False
2025-07-20 17:11:19,788 - handlers.session_handlers - INFO - Message text: 'Name: Piyush Kumar Gunjan
IFSC : UBIN0542784
Email:  <EMAIL>
Account no : *************** 
Mobile : **********'
2025-07-20 17:11:22,501 - services.user_service - INFO - Created new user: ********** (Vinay)
2025-07-20 17:11:23,662 - handlers.user_handlers - ERROR - Error sending new user notification: Forbidden: bot is not a member of the channel chat
2025-07-20 17:11:24,944 - handlers.user_handlers - INFO - Notification sent to referrer ********** about new user Vinay (ID: **********): success
2025-07-20 17:11:26,488 - handlers.session_handlers - INFO - === SESSION MESSAGE HANDLER for user **********, step: set_account_number ===
2025-07-20 17:11:26,489 - handlers.session_handlers - INFO - Session data: {'_id': ObjectId('687cd1ca7104627ee203493a'), 'user_id': **********, 'created_at': **********, 'data': {}, 'step': 'set_account_number', 'updated_at': **********}
2025-07-20 17:11:26,489 - handlers.session_handlers - INFO - Message has text: True
2025-07-20 17:11:26,489 - handlers.session_handlers - INFO - Message has photo: False
2025-07-20 17:11:26,489 - handlers.session_handlers - INFO - Message has video: False
2025-07-20 17:11:26,489 - handlers.session_handlers - INFO - Message has document: False
2025-07-20 17:11:26,489 - handlers.session_handlers - INFO - Message text: 'Ok'
2025-07-20 17:12:08,953 - services.user_service - INFO - Created new user: ********** (Arman)
2025-07-20 17:12:10,136 - handlers.user_handlers - ERROR - Error sending new user notification: Forbidden: bot is not a member of the channel chat
2025-07-20 17:12:11,460 - handlers.user_handlers - INFO - Notification sent to referrer ********** about new user Arman (ID: **********): success
2025-07-20 17:12:32,448 - handlers.session_handlers - INFO - === SESSION MESSAGE HANDLER for user **********, step: set_account_number ===
2025-07-20 17:12:32,449 - handlers.session_handlers - INFO - Session data: {'_id': ObjectId('687cd1ca7104627ee203493a'), 'user_id': **********, 'created_at': **********, 'data': {}, 'step': 'set_account_number', 'updated_at': **********}
2025-07-20 17:12:32,449 - handlers.session_handlers - INFO - Message has text: True
2025-07-20 17:12:32,449 - handlers.session_handlers - INFO - Message has photo: False
2025-07-20 17:12:32,449 - handlers.session_handlers - INFO - Message has video: False
2025-07-20 17:12:32,449 - handlers.session_handlers - INFO - Message has document: False
2025-07-20 17:12:32,449 - handlers.session_handlers - INFO - Message text: 'Name: Piyush Kumar Gunjan
IFSC : UBIN0542784
Email:  <EMAIL>
Account Number : *************** 
Mobile Number: **********'
2025-07-20 17:12:43,620 - services.referral_service - INFO - Referral reward processed: ********** earned ₹2 for referring @Aliyamgc
2025-07-20 17:12:46,259 - services.user_service - INFO - Created new user: ********** (Ar)
2025-07-20 17:12:47,389 - handlers.user_handlers - ERROR - Error sending new user notification: Forbidden: bot is not a member of the channel chat
2025-07-20 17:12:48,700 - handlers.user_handlers - INFO - Notification sent to referrer ********** about new user Ar (ID: **********): success
2025-07-20 17:14:00,735 - services.referral_service - INFO - Referral reward processed: ********** earned ₹5 for referring Vinay
2025-07-20 17:14:07,943 - handlers.session_handlers - INFO - === SESSION MESSAGE HANDLER for user **********, step: set_account_number ===
2025-07-20 17:14:07,943 - handlers.session_handlers - INFO - Session data: {'_id': ObjectId('687cd1ca7104627ee203493a'), 'user_id': **********, 'created_at': **********, 'data': {}, 'step': 'set_account_number', 'updated_at': **********}
2025-07-20 17:14:07,943 - handlers.session_handlers - INFO - Message has text: True
2025-07-20 17:14:07,944 - handlers.session_handlers - INFO - Message has photo: False
2025-07-20 17:14:07,944 - handlers.session_handlers - INFO - Message has video: False
2025-07-20 17:14:07,944 - handlers.session_handlers - INFO - Message has document: False
2025-07-20 17:14:07,944 - handlers.session_handlers - INFO - Message text: 'Name: Piyush Kumar Gunjan
IFSC : UBIN0542784
Email:  <EMAIL>
Account Number: *************** 
Mobile Number: +************'
2025-07-20 17:14:25,875 - utils.helpers - WARNING - Sync is_admin() called from async context for user ********** - consider using is_admin_async()
2025-07-20 17:14:25,878 - services.admin_service - ERROR - Error getting admin settings for **********: Task <Task pending name='Task-26' coro=<_check_admin_in_new_loop() running at C:\Users\<USER>\Desktop\referbot\python\utils\helpers.py:535> cb=[_run_until_complete_cb() at C:\Python312\Lib\asyncio\base_events.py:182]> got Future <Future pending cb=[_chain_future.<locals>._call_check_cancel() at C:\Python312\Lib\asyncio\futures.py:391]> attached to a different loop
2025-07-20 17:14:32,726 - handlers.session_handlers - INFO - === SESSION MESSAGE HANDLER for user **********, step: set_account_number ===
2025-07-20 17:14:32,726 - handlers.session_handlers - INFO - Session data: {'_id': ObjectId('687cd1ca7104627ee203493a'), 'user_id': **********, 'created_at': **********, 'data': {}, 'step': 'set_account_number', 'updated_at': **********}
2025-07-20 17:14:32,727 - handlers.session_handlers - INFO - Message has text: True
2025-07-20 17:14:32,727 - handlers.session_handlers - INFO - Message has photo: False
2025-07-20 17:14:32,727 - handlers.session_handlers - INFO - Message has video: False
2025-07-20 17:14:32,727 - handlers.session_handlers - INFO - Message has document: False
2025-07-20 17:14:32,728 - handlers.session_handlers - INFO - Message text: 'Name: Piyush Kumar Gunjan
IFSC : UBIN0542784
Email:  <EMAIL>
Account Number:     *************** 
Mobile Number: +************'
2025-07-20 17:14:41,449 - handlers.callback_handlers - INFO - Message contains media, sending new text message instead of editing
2025-07-20 17:14:50,625 - handlers.session_handlers - INFO - === SESSION MESSAGE HANDLER for user **********, step: set_account_number ===
2025-07-20 17:14:50,627 - handlers.session_handlers - INFO - Session data: {'_id': ObjectId('687cd1ca7104627ee203493a'), 'user_id': **********, 'created_at': **********, 'data': {}, 'step': 'set_account_number', 'updated_at': **********}
2025-07-20 17:14:50,627 - handlers.session_handlers - INFO - Message has text: True
2025-07-20 17:14:50,628 - handlers.session_handlers - INFO - Message has photo: False
2025-07-20 17:14:50,628 - handlers.session_handlers - INFO - Message has video: False
2025-07-20 17:14:50,628 - handlers.session_handlers - INFO - Message has document: False
2025-07-20 17:14:50,628 - handlers.session_handlers - INFO - Message text: 'Name: Piyush Kumar Gunjan
IFSC : UBIN0542784
Email:  <EMAIL>
Account Number:   *************** 
Mobile Number: +************'
2025-07-20 17:14:55,568 - handlers.user_handlers - INFO - Custom referral parameter used: from_channel -> User ID: **********
2025-07-20 17:15:15,189 - handlers.session_handlers - INFO - === SESSION MESSAGE HANDLER for user **********, step: set_account_number ===
2025-07-20 17:15:15,190 - handlers.session_handlers - INFO - Session data: {'_id': ObjectId('687cd1ca7104627ee203493a'), 'user_id': **********, 'created_at': **********, 'data': {}, 'step': 'set_account_number', 'updated_at': **********}
2025-07-20 17:15:15,190 - handlers.session_handlers - INFO - Message has text: True
2025-07-20 17:15:15,190 - handlers.session_handlers - INFO - Message has photo: False
2025-07-20 17:15:15,190 - handlers.session_handlers - INFO - Message has video: False
2025-07-20 17:15:15,191 - handlers.session_handlers - INFO - Message has document: False
2025-07-20 17:15:15,191 - handlers.session_handlers - INFO - Message text: 'Name: Piyush Kumar Gunjan
IFSC : UBIN0542784
Email:  <EMAIL>
Account Number: *************** 
Mobile Number: +************'
2025-07-20 17:15:39,809 - handlers.session_handlers - INFO - === SESSION MESSAGE HANDLER for user **********, step: set_account_number ===
2025-07-20 17:15:39,809 - handlers.session_handlers - INFO - Session data: {'_id': ObjectId('687cd1ca7104627ee203493a'), 'user_id': **********, 'created_at': **********, 'data': {}, 'step': 'set_account_number', 'updated_at': **********}
2025-07-20 17:15:39,809 - handlers.session_handlers - INFO - Message has text: True
2025-07-20 17:15:39,809 - handlers.session_handlers - INFO - Message has photo: False
2025-07-20 17:15:39,809 - handlers.session_handlers - INFO - Message has video: False
2025-07-20 17:15:39,810 - handlers.session_handlers - INFO - Message has document: False
2025-07-20 17:15:39,810 - handlers.session_handlers - INFO - Message text: 'Name: Piyush Kumar Gunjan
IFSC : UBIN0542784
Email:  <EMAIL>
Account Number:*************** 
Mobile Number: +************'
2025-07-20 17:15:41,733 - handlers.session_handlers - INFO - === SESSION MESSAGE HANDLER for user **********, step: create_custom_referral ===
2025-07-20 17:15:41,734 - handlers.session_handlers - INFO - Session data: {'_id': ObjectId('687cd4667104627ee2034941'), 'user_id': **********, 'created_at': **********, 'data': {}, 'step': 'create_custom_referral', 'updated_at': **********}
2025-07-20 17:15:41,734 - handlers.session_handlers - INFO - Message has text: True
2025-07-20 17:15:41,735 - handlers.session_handlers - INFO - Message has photo: False
2025-07-20 17:15:41,735 - handlers.session_handlers - INFO - Message has video: False
2025-07-20 17:15:41,735 - handlers.session_handlers - INFO - Message has document: False
2025-07-20 17:15:41,735 - handlers.session_handlers - INFO - Message text: 'Name kiran jaiswal 
IFSC CBIN0285042
Account number ********** 
Mobile no **********'
2025-07-20 17:15:45,686 - services.user_service - INFO - Created new user: ********** (Naveen)
2025-07-20 17:15:46,990 - handlers.user_handlers - ERROR - Error sending new user notification: Forbidden: bot is not a member of the channel chat
2025-07-20 17:15:48,467 - handlers.user_handlers - INFO - Notification sent to referrer ********** about new user Naveen (ID: **********): success
2025-07-20 17:16:08,423 - services.user_service - INFO - Created new user: ********** (Roshan)
2025-07-20 17:16:09,711 - handlers.user_handlers - ERROR - Error sending new user notification: Forbidden: bot is not a member of the channel chat
2025-07-20 17:16:11,157 - handlers.user_handlers - INFO - Notification sent to referrer ********** about new user Roshan (ID: **********): success
2025-07-20 17:16:23,759 - services.referral_service - INFO - Referral reward processed: ********** earned ₹4 for referring Naveen
2025-07-20 17:16:33,813 - services.user_service - INFO - Created new user: ********** (Emmanuel)
2025-07-20 17:16:35,009 - handlers.user_handlers - ERROR - Error sending new user notification: Forbidden: bot is not a member of the channel chat
2025-07-20 17:16:36,438 - handlers.user_handlers - INFO - Notification sent to referrer ********** about new user Emmanuel (ID: **********): success
2025-07-20 17:16:42,232 - handlers.session_handlers - INFO - === SESSION MESSAGE HANDLER for user **********, step: set_account_number ===
2025-07-20 17:16:42,232 - handlers.session_handlers - INFO - Session data: {'_id': ObjectId('687cd1ca7104627ee203493a'), 'user_id': **********, 'created_at': **********, 'data': {}, 'step': 'set_account_number', 'updated_at': **********}
2025-07-20 17:16:42,232 - handlers.session_handlers - INFO - Message has text: True
2025-07-20 17:16:42,232 - handlers.session_handlers - INFO - Message has photo: False
2025-07-20 17:16:42,232 - handlers.session_handlers - INFO - Message has video: False
2025-07-20 17:16:42,232 - handlers.session_handlers - INFO - Message has document: False
2025-07-20 17:16:42,234 - handlers.session_handlers - INFO - Message text: 'Name: Piyush Kumar Gunjan
IFSC : UBIN0542784
Email:  <EMAIL> Account Number: *************** 
Mobile Number: +************'
2025-07-20 17:17:00,955 - handlers.session_handlers - INFO - === SESSION MESSAGE HANDLER for user **********, step: set_account_number ===
2025-07-20 17:17:00,955 - handlers.session_handlers - INFO - Session data: {'_id': ObjectId('687cd1ca7104627ee203493a'), 'user_id': **********, 'created_at': **********, 'data': {}, 'step': 'set_account_number', 'updated_at': **********}
2025-07-20 17:17:00,956 - handlers.session_handlers - INFO - Message has text: True
2025-07-20 17:17:00,956 - handlers.session_handlers - INFO - Message has photo: False
2025-07-20 17:17:00,956 - handlers.session_handlers - INFO - Message has video: False
2025-07-20 17:17:00,956 - handlers.session_handlers - INFO - Message has document: False
2025-07-20 17:17:00,956 - handlers.session_handlers - INFO - Message text: 'Name: Piyush Kumar Gunjan
IFSC : UBIN0542784
Email:  <EMAIL> Account Number: **********
Mobile Number: +************'
2025-07-20 17:17:02,972 - services.user_service - INFO - Created new user: ********** (Sumit)
2025-07-20 17:17:04,536 - handlers.user_handlers - ERROR - Error sending new user notification: Forbidden: bot is not a member of the channel chat
2025-07-20 17:17:05,859 - handlers.user_handlers - INFO - Notification sent to referrer ********** about new user Sumit (ID: **********): success
2025-07-20 17:17:06,236 - __main__ - INFO - Received signal 2, initiating shutdown...
2025-07-20 17:17:06,440 - __main__ - INFO - Shutting down bot...
2025-07-20 17:17:10,039 - apscheduler.scheduler - INFO - Scheduler has been shut down
2025-07-20 17:17:10,083 - config.database - INFO - Disconnected from MongoDB
2025-07-20 17:17:10,084 - __main__ - INFO - Bot shutdown completed
2025-07-20 17:17:15,849 - __main__ - INFO - Connecting to MongoDB...
2025-07-20 17:17:28,835 - config.database - INFO - Successfully connected to MongoDB: referral_bot
2025-07-20 17:17:30,607 - config.database - INFO - Database collections and indexes initialized successfully
2025-07-20 17:17:30,608 - __main__ - INFO - Initializing Telegram bot...
2025-07-20 17:17:31,137 - __main__ - INFO - All handlers registered successfully
2025-07-20 17:17:32,039 - services.admin_service - INFO - Initialized admin settings for 4 admins
2025-07-20 17:17:32,086 - __main__ - INFO - Bot info updated: @InstantoPayBot (InstantoPay)
2025-07-20 17:17:32,086 - __main__ - INFO - Admin settings and level rewards system initialized
2025-07-20 17:17:32,086 - __main__ - INFO - Bot initialization completed successfully
2025-07-20 17:17:32,087 - __main__ - INFO - Starting bot polling...
2025-07-20 17:17:32,300 - apscheduler.scheduler - INFO - Scheduler started
2025-07-20 17:17:32,513 - __main__ - INFO - Bot is now running and polling for updates...
2025-07-20 17:17:33,705 - handlers.user_handlers - ERROR - Error in handle_joined_channel: Query is too old and response timeout expired or query id is invalid
2025-07-20 17:17:34,482 - handlers.callback_handlers - ERROR - Error in _handle_check_subscription: Query is too old and response timeout expired or query id is invalid
2025-07-20 17:17:34,860 - handlers.user_handlers - ERROR - Error in handle_joined_channel: Query is too old and response timeout expired or query id is invalid
2025-07-20 17:17:35,245 - handlers.callback_handlers - ERROR - Error in internal callback handler for data 'joined': Message is not modified: specified new message content and reply markup are exactly the same as a current content and reply markup of the message
2025-07-20 17:17:35,246 - handlers.callback_handlers - ERROR - Error in callback handler for data 'joined': Message is not modified: specified new message content and reply markup are exactly the same as a current content and reply markup of the message
2025-07-20 17:17:36,001 - handlers.callback_handlers - ERROR - Error in internal callback handler for data 'myWallet': Query is too old and response timeout expired or query id is invalid
2025-07-20 17:17:36,001 - handlers.callback_handlers - ERROR - Error in callback handler for data 'myWallet': Query is too old and response timeout expired or query id is invalid
2025-07-20 17:17:36,759 - handlers.user_handlers - ERROR - Error in handle_joined_channel: Query is too old and response timeout expired or query id is invalid
2025-07-20 17:17:37,158 - handlers.callback_handlers - ERROR - Error in internal callback handler for data 'joined': Message is not modified: specified new message content and reply markup are exactly the same as a current content and reply markup of the message
2025-07-20 17:17:37,158 - handlers.callback_handlers - ERROR - Error in callback handler for data 'joined': Message is not modified: specified new message content and reply markup are exactly the same as a current content and reply markup of the message
2025-07-20 17:17:37,922 - handlers.user_handlers - ERROR - Error in handle_joined_channel: Query is too old and response timeout expired or query id is invalid
2025-07-20 17:17:38,303 - handlers.callback_handlers - ERROR - Error in internal callback handler for data 'joined': Message is not modified: specified new message content and reply markup are exactly the same as a current content and reply markup of the message
2025-07-20 17:17:38,304 - handlers.callback_handlers - ERROR - Error in callback handler for data 'joined': Message is not modified: specified new message content and reply markup are exactly the same as a current content and reply markup of the message
2025-07-20 17:17:39,060 - handlers.callback_handlers - ERROR - Error in internal callback handler for data 'setAccountInfo': Query is too old and response timeout expired or query id is invalid
2025-07-20 17:17:39,061 - handlers.callback_handlers - ERROR - Error in callback handler for data 'setAccountInfo': Query is too old and response timeout expired or query id is invalid
2025-07-20 17:17:39,823 - handlers.user_handlers - ERROR - Error in handle_joined_channel: Query is too old and response timeout expired or query id is invalid
2025-07-20 17:17:40,204 - handlers.callback_handlers - ERROR - Error in internal callback handler for data 'joined': Message is not modified: specified new message content and reply markup are exactly the same as a current content and reply markup of the message
2025-07-20 17:17:40,205 - handlers.callback_handlers - ERROR - Error in callback handler for data 'joined': Message is not modified: specified new message content and reply markup are exactly the same as a current content and reply markup of the message
2025-07-20 17:17:40,962 - handlers.user_handlers - ERROR - Error in handle_joined_channel: Query is too old and response timeout expired or query id is invalid
2025-07-20 17:17:41,352 - handlers.callback_handlers - ERROR - Error in internal callback handler for data 'joined': Message is not modified: specified new message content and reply markup are exactly the same as a current content and reply markup of the message
2025-07-20 17:17:41,352 - handlers.callback_handlers - ERROR - Error in callback handler for data 'joined': Message is not modified: specified new message content and reply markup are exactly the same as a current content and reply markup of the message
2025-07-20 17:17:41,869 - handlers.user_handlers - INFO - Custom referral parameter used: Gift -> User ID: **********
2025-07-20 17:17:46,070 - handlers.user_handlers - ERROR - Error in handle_joined_channel: Query is too old and response timeout expired or query id is invalid
2025-07-20 17:17:46,451 - handlers.callback_handlers - ERROR - Error in internal callback handler for data 'joined': Message is not modified: specified new message content and reply markup are exactly the same as a current content and reply markup of the message
2025-07-20 17:17:46,452 - handlers.callback_handlers - ERROR - Error in callback handler for data 'joined': Message is not modified: specified new message content and reply markup are exactly the same as a current content and reply markup of the message
2025-07-20 17:17:46,970 - handlers.user_handlers - INFO - Custom referral parameter used: Gift -> User ID: **********
2025-07-20 17:17:48,985 - handlers.user_handlers - ERROR - Error in handle_joined_channel: Query is too old and response timeout expired or query id is invalid
2025-07-20 17:17:49,372 - handlers.callback_handlers - ERROR - Error in internal callback handler for data 'joined': Message is not modified: specified new message content and reply markup are exactly the same as a current content and reply markup of the message
2025-07-20 17:17:49,373 - handlers.callback_handlers - ERROR - Error in callback handler for data 'joined': Message is not modified: specified new message content and reply markup are exactly the same as a current content and reply markup of the message
2025-07-20 17:17:50,124 - handlers.user_handlers - ERROR - Error in handle_joined_channel: Query is too old and response timeout expired or query id is invalid
2025-07-20 17:17:50,511 - handlers.callback_handlers - ERROR - Error in internal callback handler for data 'joined': Message is not modified: specified new message content and reply markup are exactly the same as a current content and reply markup of the message
2025-07-20 17:17:50,511 - handlers.callback_handlers - ERROR - Error in callback handler for data 'joined': Message is not modified: specified new message content and reply markup are exactly the same as a current content and reply markup of the message
2025-07-20 17:17:51,263 - handlers.user_handlers - ERROR - Error in handle_joined_channel: Query is too old and response timeout expired or query id is invalid
2025-07-20 17:17:51,645 - handlers.callback_handlers - ERROR - Error in internal callback handler for data 'joined': Message is not modified: specified new message content and reply markup are exactly the same as a current content and reply markup of the message
2025-07-20 17:17:51,646 - handlers.callback_handlers - ERROR - Error in callback handler for data 'joined': Message is not modified: specified new message content and reply markup are exactly the same as a current content and reply markup of the message
2025-07-20 17:17:52,525 - handlers.callback_handlers - ERROR - Error in internal callback handler for data 'myWallet': Query is too old and response timeout expired or query id is invalid
2025-07-20 17:17:52,526 - handlers.callback_handlers - ERROR - Error in callback handler for data 'myWallet': Query is too old and response timeout expired or query id is invalid
2025-07-20 17:17:53,314 - handlers.user_handlers - ERROR - Error in handle_joined_channel: Query is too old and response timeout expired or query id is invalid
2025-07-20 17:17:53,741 - handlers.callback_handlers - ERROR - Error in internal callback handler for data 'joined': Message is not modified: specified new message content and reply markup are exactly the same as a current content and reply markup of the message
2025-07-20 17:17:53,742 - handlers.callback_handlers - ERROR - Error in callback handler for data 'joined': Message is not modified: specified new message content and reply markup are exactly the same as a current content and reply markup of the message
2025-07-20 17:17:54,501 - handlers.user_handlers - ERROR - Error in handle_joined_channel: Query is too old and response timeout expired or query id is invalid
2025-07-20 17:17:54,995 - handlers.callback_handlers - ERROR - Error in internal callback handler for data 'joined': Message is not modified: specified new message content and reply markup are exactly the same as a current content and reply markup of the message
2025-07-20 17:17:54,995 - handlers.callback_handlers - ERROR - Error in callback handler for data 'joined': Message is not modified: specified new message content and reply markup are exactly the same as a current content and reply markup of the message
2025-07-20 17:17:55,753 - handlers.callback_handlers - ERROR - Error in _handle_check_subscription: Query is too old and response timeout expired or query id is invalid
2025-07-20 17:17:56,132 - handlers.user_handlers - ERROR - Error in handle_joined_channel: Query is too old and response timeout expired or query id is invalid
2025-07-20 17:17:56,522 - handlers.callback_handlers - ERROR - Error in internal callback handler for data 'joined': Message is not modified: specified new message content and reply markup are exactly the same as a current content and reply markup of the message
2025-07-20 17:17:56,522 - handlers.callback_handlers - ERROR - Error in callback handler for data 'joined': Message is not modified: specified new message content and reply markup are exactly the same as a current content and reply markup of the message
2025-07-20 17:17:57,724 - handlers.user_handlers - ERROR - Error in handle_joined_channel: Query is too old and response timeout expired or query id is invalid
2025-07-20 17:17:58,486 - handlers.callback_handlers - ERROR - Error in internal callback handler for data 'myWallet': Query is too old and response timeout expired or query id is invalid
2025-07-20 17:17:58,486 - handlers.callback_handlers - ERROR - Error in callback handler for data 'myWallet': Query is too old and response timeout expired or query id is invalid
2025-07-20 17:17:58,918 - handlers.session_handlers - INFO - === SESSION MESSAGE HANDLER for user **********, step: set_account_number ===
2025-07-20 17:17:58,919 - handlers.session_handlers - INFO - Session data: {'_id': ObjectId('687cd1ca7104627ee203493a'), 'user_id': **********, 'created_at': **********, 'data': {}, 'step': 'set_account_number', 'updated_at': **********}
2025-07-20 17:17:58,919 - handlers.session_handlers - INFO - Message has text: True
2025-07-20 17:17:58,920 - handlers.session_handlers - INFO - Message has photo: False
2025-07-20 17:17:58,920 - handlers.session_handlers - INFO - Message has video: False
2025-07-20 17:17:58,920 - handlers.session_handlers - INFO - Message has document: False
2025-07-20 17:17:58,921 - handlers.session_handlers - INFO - Message text: 'Name: Piyush Kumar Gunjan
IFSC : UBIN0542784
Email:  <EMAIL> Account Number: ***************
Mobile Number: +************'
2025-07-20 17:18:18,250 - handlers.session_handlers - INFO - === SESSION MESSAGE HANDLER for user **********, step: user_details_get_id ===
2025-07-20 17:18:18,251 - handlers.session_handlers - INFO - Session data: {'_id': ObjectId('687cd77a7104627ee2034948'), 'user_id': **********, 'created_at': **********, 'data': {}, 'step': 'user_details_get_id', 'updated_at': **********}
2025-07-20 17:18:18,252 - handlers.session_handlers - INFO - Message has text: True
2025-07-20 17:18:18,252 - handlers.session_handlers - INFO - Message has photo: False
2025-07-20 17:18:18,253 - handlers.session_handlers - INFO - Message has video: False
2025-07-20 17:18:18,253 - handlers.session_handlers - INFO - Message has document: False
2025-07-20 17:18:18,253 - handlers.session_handlers - INFO - Message text: '**********'
2025-07-20 17:18:25,264 - handlers.session_handlers - INFO - === SESSION MESSAGE HANDLER for user **********, step: add_user_balance ===
2025-07-20 17:18:25,264 - handlers.session_handlers - INFO - Session data: {'_id': ObjectId('687cd7877104627ee2034949'), 'user_id': **********, 'created_at': 1753012103, 'data': {'target_user_id': **********}, 'step': 'add_user_balance', 'updated_at': 1753012103}
2025-07-20 17:18:25,265 - handlers.session_handlers - INFO - Message has text: True
2025-07-20 17:18:25,265 - handlers.session_handlers - INFO - Message has photo: False
2025-07-20 17:18:25,265 - handlers.session_handlers - INFO - Message has video: False
2025-07-20 17:18:25,265 - handlers.session_handlers - INFO - Message has document: False
2025-07-20 17:18:25,266 - handlers.session_handlers - INFO - Message text: '100'
2025-07-20 17:18:27,056 - services.admin_user_service - INFO - Balance change notification sent to user **********: added Rs.100
2025-07-20 17:18:55,653 - utils.helpers - WARNING - Sync is_admin() called from async context for user ********** - consider using is_admin_async()
2025-07-20 17:18:55,655 - services.admin_service - ERROR - Error getting admin settings for **********: Task <Task pending name='Task-12' coro=<_check_admin_in_new_loop() running at C:\Users\<USER>\Desktop\referbot\python\utils\helpers.py:535> cb=[_run_until_complete_cb() at C:\Python312\Lib\asyncio\base_events.py:182]> got Future <Future pending cb=[_chain_future.<locals>._call_check_cancel() at C:\Python312\Lib\asyncio\futures.py:391]> attached to a different loop
2025-07-20 17:19:09,304 - handlers.session_handlers - INFO - === SESSION MESSAGE HANDLER for user **********, step: set_binance_id ===
2025-07-20 17:19:09,305 - handlers.session_handlers - INFO - Session data: {'_id': ObjectId('687cd7ad7104627ee203494a'), 'user_id': **********, 'created_at': 1753012142, 'data': {}, 'step': 'set_binance_id', 'updated_at': 1753012142}
2025-07-20 17:19:09,305 - handlers.session_handlers - INFO - Message has text: True
2025-07-20 17:19:09,305 - handlers.session_handlers - INFO - Message has photo: False
2025-07-20 17:19:09,305 - handlers.session_handlers - INFO - Message has video: False
2025-07-20 17:19:09,306 - handlers.session_handlers - INFO - Message has document: False
2025-07-20 17:19:09,306 - handlers.session_handlers - INFO - Message text: '123434234'
2025-07-20 17:19:10,687 - handlers.user_handlers - INFO - Custom referral parameter used: from_channel -> User ID: **********
2025-07-20 17:19:13,634 - handlers.user_handlers - INFO - Custom referral parameter used: Gift -> User ID: **********
2025-07-20 17:19:28,099 - services.withdrawal_service - INFO - Withdrawal record saved for user **********: withdrawal_1753012168.********
2025-07-20 17:20:28,916 - handlers.callback_handlers - ERROR - Error sending task media: Message can't be deleted for everyone
2025-07-20 17:20:56,835 - handlers.session_handlers - INFO - === SESSION MESSAGE HANDLER for user **********, step: set_account_number ===
2025-07-20 17:20:56,836 - handlers.session_handlers - INFO - Session data: {'_id': ObjectId('687cd7c27104627ee203494c'), 'user_id': **********, 'created_at': **********, 'data': {}, 'step': 'set_account_number', 'updated_at': **********}
2025-07-20 17:20:56,836 - handlers.session_handlers - INFO - Message has text: True
2025-07-20 17:20:56,836 - handlers.session_handlers - INFO - Message has photo: False
2025-07-20 17:20:56,836 - handlers.session_handlers - INFO - Message has video: False
2025-07-20 17:20:56,837 - handlers.session_handlers - INFO - Message has document: False
2025-07-20 17:20:56,837 - handlers.session_handlers - INFO - Message text: 'Name: kiran 
IFSC: CBIN0285042
Email: a <EMAIL>
Account no: **********
Mobile no: **********'
2025-07-20 17:21:09,459 - services.user_service - INFO - Created new user: ********** (𝚂)
2025-07-20 17:21:10,901 - handlers.user_handlers - ERROR - Error sending new user notification: Forbidden: bot is not a member of the channel chat
2025-07-20 17:21:12,361 - handlers.user_handlers - INFO - Notification sent to referrer ********** about new user 𝚂 (ID: **********): success
2025-07-20 17:21:16,787 - __main__ - INFO - Received signal 2, initiating shutdown...
2025-07-20 17:21:16,787 - __main__ - INFO - Shutting down bot...
2025-07-20 17:21:17,384 - apscheduler.scheduler - INFO - Scheduler has been shut down
2025-07-20 17:21:17,429 - config.database - INFO - Disconnected from MongoDB
2025-07-20 17:21:17,430 - __main__ - INFO - Bot shutdown completed
2025-07-20 17:21:23,942 - __main__ - INFO - Connecting to MongoDB...
2025-07-20 17:21:37,026 - config.database - INFO - Successfully connected to MongoDB: referral_bot
2025-07-20 17:21:38,673 - config.database - INFO - Database collections and indexes initialized successfully
2025-07-20 17:21:38,673 - __main__ - INFO - Initializing Telegram bot...
2025-07-20 17:21:39,181 - __main__ - INFO - All handlers registered successfully
2025-07-20 17:21:40,044 - services.admin_service - INFO - Initialized admin settings for 4 admins
2025-07-20 17:21:40,086 - __main__ - INFO - Bot info updated: @InstantoPayBot (InstantoPay)
2025-07-20 17:21:40,087 - __main__ - INFO - Admin settings and level rewards system initialized
2025-07-20 17:21:40,087 - __main__ - INFO - Bot initialization completed successfully
2025-07-20 17:21:40,087 - __main__ - INFO - Starting bot polling...
2025-07-20 17:21:40,289 - apscheduler.scheduler - INFO - Scheduler started
2025-07-20 17:21:40,496 - __main__ - INFO - Bot is now running and polling for updates...
2025-07-20 17:21:41,525 - handlers.callback_handlers - ERROR - Error in internal callback handler for data 'myWallet': Query is too old and response timeout expired or query id is invalid
2025-07-20 17:21:41,526 - handlers.callback_handlers - ERROR - Error in callback handler for data 'myWallet': Query is too old and response timeout expired or query id is invalid
2025-07-20 17:22:11,936 - services.user_service - INFO - Created new user: 7903953739 (Shalu)
2025-07-20 17:22:13,273 - handlers.user_handlers - ERROR - Error sending new user notification: Forbidden: bot is not a member of the channel chat
2025-07-20 17:22:14,510 - handlers.user_handlers - INFO - Notification sent to referrer ********** about new user Shalu (ID: 7903953739): success
2025-07-20 17:22:46,190 - __main__ - INFO - Received signal 2, initiating shutdown...
2025-07-20 17:22:46,190 - __main__ - INFO - Shutting down bot...
2025-07-20 17:22:46,797 - apscheduler.scheduler - INFO - Scheduler has been shut down
2025-07-20 17:22:46,842 - config.database - INFO - Disconnected from MongoDB
2025-07-20 17:22:46,842 - __main__ - INFO - Bot shutdown completed
2025-07-20 17:23:05,369 - __main__ - INFO - Connecting to MongoDB...
2025-07-20 17:23:18,197 - config.database - INFO - Successfully connected to MongoDB: referral_bot
2025-07-20 17:23:19,783 - config.database - INFO - Database collections and indexes initialized successfully
2025-07-20 17:23:19,783 - __main__ - INFO - Initializing Telegram bot...
2025-07-20 17:23:20,171 - __main__ - INFO - All handlers registered successfully
2025-07-20 17:23:21,076 - services.admin_service - INFO - Initialized admin settings for 4 admins
2025-07-20 17:23:21,116 - __main__ - INFO - Bot info updated: @InstantoPayBot (InstantoPay)
2025-07-20 17:23:21,116 - __main__ - INFO - Admin settings and level rewards system initialized
2025-07-20 17:23:21,116 - __main__ - INFO - Bot initialization completed successfully
2025-07-20 17:23:21,116 - __main__ - INFO - Starting bot polling...
2025-07-20 17:23:21,327 - apscheduler.scheduler - INFO - Scheduler started
2025-07-20 17:23:21,538 - __main__ - INFO - Bot is now running and polling for updates...
2025-07-20 17:23:24,145 - handlers.user_handlers - ERROR - Error in handle_joined_channel: Query is too old and response timeout expired or query id is invalid
2025-07-20 17:23:24,578 - handlers.session_handlers - INFO - === SESSION MESSAGE HANDLER for user **********, step: set_account_number ===
2025-07-20 17:23:24,578 - handlers.session_handlers - INFO - Session data: {'_id': ObjectId('687cd7c27104627ee203494c'), 'user_id': **********, 'created_at': **********, 'data': {}, 'step': 'set_account_number', 'updated_at': **********}
2025-07-20 17:23:24,578 - handlers.session_handlers - INFO - Message has text: True
2025-07-20 17:23:24,579 - handlers.session_handlers - INFO - Message has photo: False
2025-07-20 17:23:24,579 - handlers.session_handlers - INFO - Message has video: False
2025-07-20 17:23:24,579 - handlers.session_handlers - INFO - Message has document: False
2025-07-20 17:23:24,579 - handlers.session_handlers - INFO - Message text: 'Name: kiran 
IFSC: CBIN0285042
Email: a <EMAIL>
Account no: **********
Mobile no: **********'
2025-07-20 17:24:06,443 - services.user_service - INFO - Created new user: ********** (💲 𝓓 𝓐 𝓡 𝓚 💱)
2025-07-20 17:24:07,614 - handlers.user_handlers - ERROR - Error sending new user notification: Forbidden: bot is not a member of the channel chat
2025-07-20 17:24:08,905 - handlers.user_handlers - INFO - Notification sent to referrer ********** about new user 💲 𝓓 𝓐 𝓡 𝓚 💱 (ID: **********): success
2025-07-20 17:24:50,606 - handlers.session_handlers - INFO - === SESSION MESSAGE HANDLER for user **********, step: user_details_get_id ===
2025-07-20 17:24:50,606 - handlers.session_handlers - INFO - Session data: {'_id': ObjectId('687cd7cc7104627ee203494d'), 'user_id': **********, 'created_at': **********, 'data': {}, 'step': 'user_details_get_id', 'updated_at': **********}
2025-07-20 17:24:50,606 - handlers.session_handlers - INFO - Message has text: True
2025-07-20 17:24:50,606 - handlers.session_handlers - INFO - Message has photo: False
2025-07-20 17:24:50,607 - handlers.session_handlers - INFO - Message has video: False
2025-07-20 17:24:50,607 - handlers.session_handlers - INFO - Message has document: False
2025-07-20 17:24:50,607 - handlers.session_handlers - INFO - Message text: '**********'
2025-07-20 17:24:56,932 - handlers.session_handlers - INFO - === SESSION MESSAGE HANDLER for user **********, step: set_account_number ===
2025-07-20 17:24:56,934 - handlers.session_handlers - INFO - Session data: {'_id': ObjectId('687cd7c27104627ee203494c'), 'user_id': **********, 'created_at': **********, 'data': {}, 'step': 'set_account_number', 'updated_at': **********}
2025-07-20 17:24:56,934 - handlers.session_handlers - INFO - Message has text: True
2025-07-20 17:24:56,934 - handlers.session_handlers - INFO - Message has photo: False
2025-07-20 17:24:56,934 - handlers.session_handlers - INFO - Message has video: False
2025-07-20 17:24:56,934 - handlers.session_handlers - INFO - Message has document: False
2025-07-20 17:24:56,934 - handlers.session_handlers - INFO - Message text: 'Name: kiran 
IFSC: CBIN0285042
Email: <EMAIL>
Account no: **********
Mobile no: **********'
2025-07-20 17:25:12,429 - handlers.callback_handlers - INFO - Message contains media, sending new text message instead of editing
2025-07-20 17:25:16,875 - handlers.session_handlers - INFO - === SESSION MESSAGE HANDLER for user **********, step: add_user_balance ===
2025-07-20 17:25:16,875 - handlers.session_handlers - INFO - Session data: {'_id': ObjectId('687cd9157104627ee2034952'), 'user_id': **********, 'created_at': **********, 'data': {'target_user_id': **********}, 'step': 'add_user_balance', 'updated_at': **********}
2025-07-20 17:25:16,875 - handlers.session_handlers - INFO - Message has text: True
2025-07-20 17:25:16,876 - handlers.session_handlers - INFO - Message has photo: False
2025-07-20 17:25:16,876 - handlers.session_handlers - INFO - Message has video: False
2025-07-20 17:25:16,876 - handlers.session_handlers - INFO - Message has document: False
2025-07-20 17:25:16,876 - handlers.session_handlers - INFO - Message text: '1000'
2025-07-20 17:25:18,669 - services.admin_user_service - INFO - Balance change notification sent to user **********: added Rs.1000
2025-07-20 17:25:21,910 - handlers.user_handlers - ERROR - Error in handle_joined_channel: Query is too old and response timeout expired or query id is invalid
2025-07-20 17:25:22,688 - handlers.callback_handlers - ERROR - Error in internal callback handler for data 'setAccountInfo': Query is too old and response timeout expired or query id is invalid
2025-07-20 17:25:22,688 - handlers.callback_handlers - ERROR - Error in callback handler for data 'setAccountInfo': Query is too old and response timeout expired or query id is invalid
2025-07-20 17:25:23,447 - handlers.user_handlers - ERROR - Error in handle_joined_channel: Query is too old and response timeout expired or query id is invalid
2025-07-20 17:25:23,831 - handlers.callback_handlers - ERROR - Error in internal callback handler for data 'joined': Message is not modified: specified new message content and reply markup are exactly the same as a current content and reply markup of the message
2025-07-20 17:25:23,831 - handlers.callback_handlers - ERROR - Error in callback handler for data 'joined': Message is not modified: specified new message content and reply markup are exactly the same as a current content and reply markup of the message
2025-07-20 17:25:24,385 - handlers.user_handlers - INFO - Custom referral parameter used: from_channel -> User ID: **********
2025-07-20 17:25:26,841 - handlers.user_handlers - ERROR - Error in handle_joined_channel: Query is too old and response timeout expired or query id is invalid
2025-07-20 17:25:27,226 - handlers.callback_handlers - ERROR - Error in internal callback handler for data 'joined': Message is not modified: specified new message content and reply markup are exactly the same as a current content and reply markup of the message
2025-07-20 17:25:27,227 - handlers.callback_handlers - ERROR - Error in callback handler for data 'joined': Message is not modified: specified new message content and reply markup are exactly the same as a current content and reply markup of the message
2025-07-20 17:25:27,991 - handlers.callback_handlers - ERROR - Error in internal callback handler for data 'setAccountInfo': Query is too old and response timeout expired or query id is invalid
2025-07-20 17:25:27,991 - handlers.callback_handlers - ERROR - Error in callback handler for data 'setAccountInfo': Query is too old and response timeout expired or query id is invalid
2025-07-20 17:25:28,758 - handlers.user_handlers - ERROR - Error in handle_joined_channel: Query is too old and response timeout expired or query id is invalid
2025-07-20 17:25:29,142 - handlers.callback_handlers - ERROR - Error in internal callback handler for data 'joined': Message is not modified: specified new message content and reply markup are exactly the same as a current content and reply markup of the message
2025-07-20 17:25:29,143 - handlers.callback_handlers - ERROR - Error in callback handler for data 'joined': Message is not modified: specified new message content and reply markup are exactly the same as a current content and reply markup of the message
2025-07-20 17:25:29,897 - handlers.user_handlers - ERROR - Error in handle_joined_channel: Query is too old and response timeout expired or query id is invalid
2025-07-20 17:25:30,288 - handlers.callback_handlers - ERROR - Error in internal callback handler for data 'joined': Message is not modified: specified new message content and reply markup are exactly the same as a current content and reply markup of the message
2025-07-20 17:25:30,289 - handlers.callback_handlers - ERROR - Error in callback handler for data 'joined': Message is not modified: specified new message content and reply markup are exactly the same as a current content and reply markup of the message
2025-07-20 17:25:31,066 - handlers.user_handlers - ERROR - Error in handle_joined_channel: Query is too old and response timeout expired or query id is invalid
2025-07-20 17:25:31,439 - handlers.callback_handlers - ERROR - Error in internal callback handler for data 'joined': Message is not modified: specified new message content and reply markup are exactly the same as a current content and reply markup of the message
2025-07-20 17:25:31,440 - handlers.callback_handlers - ERROR - Error in callback handler for data 'joined': Message is not modified: specified new message content and reply markup are exactly the same as a current content and reply markup of the message
2025-07-20 17:25:32,177 - handlers.callback_handlers - ERROR - Error in internal callback handler for data 'promotionReport': Query is too old and response timeout expired or query id is invalid
2025-07-20 17:25:32,177 - handlers.callback_handlers - ERROR - Error in callback handler for data 'promotionReport': Query is too old and response timeout expired or query id is invalid
2025-07-20 17:25:32,927 - handlers.callback_handlers - ERROR - Error in internal callback handler for data 'myWallet': Query is too old and response timeout expired or query id is invalid
2025-07-20 17:25:32,927 - handlers.callback_handlers - ERROR - Error in callback handler for data 'myWallet': Query is too old and response timeout expired or query id is invalid
2025-07-20 17:25:33,662 - handlers.user_handlers - ERROR - Error in handle_joined_channel: Query is too old and response timeout expired or query id is invalid
2025-07-20 17:25:34,435 - handlers.callback_handlers - ERROR - Error in internal callback handler for data 'joined': Message is not modified: specified new message content and reply markup are exactly the same as a current content and reply markup of the message
2025-07-20 17:25:34,436 - handlers.callback_handlers - ERROR - Error in callback handler for data 'joined': Message is not modified: specified new message content and reply markup are exactly the same as a current content and reply markup of the message
2025-07-20 17:25:35,183 - handlers.user_handlers - ERROR - Error in handle_joined_channel: Query is too old and response timeout expired or query id is invalid
2025-07-20 17:25:35,576 - handlers.callback_handlers - ERROR - Error in internal callback handler for data 'joined': Message is not modified: specified new message content and reply markup are exactly the same as a current content and reply markup of the message
2025-07-20 17:25:35,576 - handlers.callback_handlers - ERROR - Error in callback handler for data 'joined': Message is not modified: specified new message content and reply markup are exactly the same as a current content and reply markup of the message
2025-07-20 17:25:36,347 - handlers.callback_handlers - ERROR - Error in internal callback handler for data 'viewTask_task_1752995334.fd08d118': Query is too old and response timeout expired or query id is invalid
2025-07-20 17:25:36,348 - handlers.callback_handlers - ERROR - Error in callback handler for data 'viewTask_task_1752995334.fd08d118': Query is too old and response timeout expired or query id is invalid
2025-07-20 17:25:37,108 - handlers.user_handlers - ERROR - Error in handle_joined_channel: Query is too old and response timeout expired or query id is invalid
2025-07-20 17:25:37,480 - handlers.callback_handlers - ERROR - Error in internal callback handler for data 'joined': Message is not modified: specified new message content and reply markup are exactly the same as a current content and reply markup of the message
2025-07-20 17:25:37,481 - handlers.callback_handlers - ERROR - Error in callback handler for data 'joined': Message is not modified: specified new message content and reply markup are exactly the same as a current content and reply markup of the message
2025-07-20 17:25:37,957 - handlers.user_handlers - INFO - Custom referral parameter used: from_channel -> User ID: **********
2025-07-20 17:25:39,732 - handlers.user_handlers - ERROR - Error in handle_joined_channel: Query is too old and response timeout expired or query id is invalid
2025-07-20 17:25:40,112 - handlers.callback_handlers - ERROR - Error in internal callback handler for data 'joined': Message is not modified: specified new message content and reply markup are exactly the same as a current content and reply markup of the message
2025-07-20 17:25:40,113 - handlers.callback_handlers - ERROR - Error in callback handler for data 'joined': Message is not modified: specified new message content and reply markup are exactly the same as a current content and reply markup of the message
2025-07-20 17:25:40,863 - handlers.callback_handlers - ERROR - Error in internal callback handler for data 'myWallet': Query is too old and response timeout expired or query id is invalid
2025-07-20 17:25:40,864 - handlers.callback_handlers - ERROR - Error in callback handler for data 'myWallet': Query is too old and response timeout expired or query id is invalid
2025-07-20 17:25:41,617 - handlers.callback_handlers - ERROR - Error in internal callback handler for data 'myWallet': Query is too old and response timeout expired or query id is invalid
2025-07-20 17:25:41,617 - handlers.callback_handlers - ERROR - Error in callback handler for data 'myWallet': Query is too old and response timeout expired or query id is invalid
2025-07-20 17:25:44,669 - handlers.callback_handlers - ERROR - Error in smart navigation: Message is not modified: specified new message content and reply markup are exactly the same as a current content and reply markup of the message
2025-07-20 17:25:58,561 - handlers.callback_handlers - INFO - Message contains media, sending new text message instead of editing
2025-07-20 17:26:02,635 - services.user_service - INFO - Created new user: 7779117837 (Tp)
2025-07-20 17:26:03,784 - handlers.user_handlers - ERROR - Error sending new user notification: Forbidden: bot is not a member of the channel chat
2025-07-20 17:26:05,037 - handlers.user_handlers - INFO - Notification sent to referrer ********** about new user Tp (ID: 7779117837): success
2025-07-20 17:26:06,643 - handlers.user_handlers - INFO - Custom referral parameter used: from_channel -> User ID: **********
2025-07-20 17:26:10,586 - services.user_service - INFO - Created new user: ********** (𝖁𝖊𝖙𝖗𝖎)
2025-07-20 17:26:11,788 - handlers.user_handlers - ERROR - Error sending new user notification: Forbidden: bot is not a member of the channel chat
2025-07-20 17:26:13,052 - handlers.user_handlers - INFO - Notification sent to referrer ********** about new user 𝖁𝖊𝖙𝖗𝖎 (ID: **********): success
2025-07-20 17:26:29,065 - handlers.session_handlers - INFO - === SESSION MESSAGE HANDLER for user **********, step: set_account_number ===
2025-07-20 17:26:29,065 - handlers.session_handlers - INFO - Session data: {'_id': ObjectId('687cd7c27104627ee203494c'), 'user_id': **********, 'created_at': **********, 'data': {}, 'step': 'set_account_number', 'updated_at': **********}
2025-07-20 17:26:29,065 - handlers.session_handlers - INFO - Message has text: True
2025-07-20 17:26:29,065 - handlers.session_handlers - INFO - Message has photo: False
2025-07-20 17:26:29,066 - handlers.session_handlers - INFO - Message has video: False
2025-07-20 17:26:29,066 - handlers.session_handlers - INFO - Message has document: False
2025-07-20 17:26:29,066 - handlers.session_handlers - INFO - Message text: 'Name: kiran 
IFSC: CBIN0285042
Email: a <EMAIL>
Account no: **********
Mobile no: **********'
2025-07-20 17:26:35,981 - handlers.user_handlers - INFO - Custom referral parameter used: Gift -> User ID: **********
2025-07-20 17:26:55,932 - services.referral_service - INFO - Referral reward processed: ********** earned ₹2 for referring 𝖁𝖊𝖙𝖗𝖎
2025-07-20 17:27:11,323 - handlers.session_handlers - INFO - === SESSION MESSAGE HANDLER for user **********, step: user_details_get_id ===
2025-07-20 17:27:11,323 - handlers.session_handlers - INFO - Session data: {'_id': ObjectId('687cd9907104627ee2034953'), 'user_id': **********, 'created_at': **********, 'data': {}, 'step': 'user_details_get_id', 'updated_at': **********}
2025-07-20 17:27:11,323 - handlers.session_handlers - INFO - Message has text: True
2025-07-20 17:27:11,324 - handlers.session_handlers - INFO - Message has photo: False
2025-07-20 17:27:11,324 - handlers.session_handlers - INFO - Message has video: False
2025-07-20 17:27:11,324 - handlers.session_handlers - INFO - Message has document: False
2025-07-20 17:27:11,324 - handlers.session_handlers - INFO - Message text: '**********'
2025-07-20 17:27:19,119 - handlers.user_handlers - INFO - Custom referral parameter used: from_channel -> User ID: **********
2025-07-20 17:27:26,885 - handlers.session_handlers - INFO - === SESSION MESSAGE HANDLER for user **********, step: add_user_balance ===
2025-07-20 17:27:26,885 - handlers.session_handlers - INFO - Session data: {'_id': ObjectId('687cd9a17104627ee2034954'), 'user_id': **********, 'created_at': 1753012641, 'data': {'target_user_id': **********}, 'step': 'add_user_balance', 'updated_at': 1753012641}
2025-07-20 17:27:26,886 - handlers.session_handlers - INFO - Message has text: True
2025-07-20 17:27:26,886 - handlers.session_handlers - INFO - Message has photo: False
2025-07-20 17:27:26,886 - handlers.session_handlers - INFO - Message has video: False
2025-07-20 17:27:26,886 - handlers.session_handlers - INFO - Message has document: False
2025-07-20 17:27:26,886 - handlers.session_handlers - INFO - Message text: '1000'
2025-07-20 17:27:28,699 - services.admin_user_service - INFO - Balance change notification sent to user **********: added Rs.1000
2025-07-20 17:27:32,393 - handlers.user_handlers - INFO - Custom referral parameter used: from_channel -> User ID: **********
2025-07-20 17:28:01,789 - handlers.session_handlers - INFO - === SESSION MESSAGE HANDLER for user **********, step: set_account_name ===
2025-07-20 17:28:01,790 - handlers.session_handlers - INFO - Session data: {'_id': ObjectId('687cd9b97104627ee2034955'), 'user_id': **********, 'created_at': **********, 'data': {}, 'step': 'set_account_name', 'updated_at': **********}
2025-07-20 17:28:01,790 - handlers.session_handlers - INFO - Message has text: True
2025-07-20 17:28:01,790 - handlers.session_handlers - INFO - Message has photo: False
2025-07-20 17:28:01,790 - handlers.session_handlers - INFO - Message has video: False
2025-07-20 17:28:01,791 - handlers.session_handlers - INFO - Message has document: False
2025-07-20 17:28:01,791 - handlers.session_handlers - INFO - Message text: 'Vetriselven'
2025-07-20 17:28:03,078 - handlers.session_handlers - INFO - === SESSION MESSAGE HANDLER for user **********, step: set_binance_id ===
2025-07-20 17:28:03,079 - handlers.session_handlers - INFO - Session data: {'_id': ObjectId('687cd9c67104627ee2034956'), 'user_id': **********, 'created_at': **********, 'data': {}, 'step': 'set_binance_id', 'updated_at': **********}
2025-07-20 17:28:03,079 - handlers.session_handlers - INFO - Message has text: True
2025-07-20 17:28:03,079 - handlers.session_handlers - INFO - Message has photo: False
2025-07-20 17:28:03,079 - handlers.session_handlers - INFO - Message has video: False
2025-07-20 17:28:03,079 - handlers.session_handlers - INFO - Message has document: False
2025-07-20 17:28:03,080 - handlers.session_handlers - INFO - Message text: '987897897'
2025-07-20 17:28:23,894 - services.withdrawal_service - INFO - Withdrawal record saved for user **********: withdrawal_1753012703.770c003b
2025-07-20 17:28:25,489 - handlers.session_handlers - INFO - === SESSION MESSAGE HANDLER for user **********, step: redeem_gift_code ===
2025-07-20 17:28:25,490 - handlers.session_handlers - INFO - Session data: {'_id': ObjectId('687cd7c27104627ee203494c'), 'user_id': **********, 'created_at': 1753012693, 'data': {}, 'step': 'redeem_gift_code', 'updated_at': 1753012693}
2025-07-20 17:28:25,490 - handlers.session_handlers - INFO - Message has text: True
2025-07-20 17:28:25,490 - handlers.session_handlers - INFO - Message has photo: False
2025-07-20 17:28:25,490 - handlers.session_handlers - INFO - Message has video: False
2025-07-20 17:28:25,490 - handlers.session_handlers - INFO - Message has document: False
2025-07-20 17:28:25,491 - handlers.session_handlers - INFO - Message text: 'Oficial 100'
2025-07-20 17:29:00,063 - services.withdrawal_service - INFO - Withdrawal rejected: User **********, Amount ₹100
2025-07-20 17:29:07,119 - handlers.session_handlers - INFO - === SESSION MESSAGE HANDLER for user **********, step: set_account_ifsc ===
2025-07-20 17:29:07,119 - handlers.session_handlers - INFO - Session data: {'_id': ObjectId('687cd9d47104627ee2034957'), 'user_id': **********, 'created_at': **********, 'data': {}, 'step': 'set_account_ifsc', 'updated_at': **********}
2025-07-20 17:29:07,119 - handlers.session_handlers - INFO - Message has text: True
2025-07-20 17:29:07,120 - handlers.session_handlers - INFO - Message has photo: False
2025-07-20 17:29:07,120 - handlers.session_handlers - INFO - Message has video: False
2025-07-20 17:29:07,120 - handlers.session_handlers - INFO - Message has document: False
2025-07-20 17:29:07,120 - handlers.session_handlers - INFO - Message text: 'SBIN0011062'
2025-07-20 17:29:08,368 - utils.helpers - INFO - Cached bank details for IFSC: SBIN0011062
2025-07-20 17:29:43,173 - handlers.session_handlers - INFO - === SESSION MESSAGE HANDLER for user **********, step: set_account_email ===
2025-07-20 17:29:43,173 - handlers.session_handlers - INFO - Session data: {'_id': ObjectId('687cda207104627ee203495a'), 'user_id': **********, 'created_at': **********, 'data': {}, 'step': 'set_account_email', 'updated_at': **********}
2025-07-20 17:29:43,174 - handlers.session_handlers - INFO - Message has text: True
2025-07-20 17:29:43,174 - handlers.session_handlers - INFO - Message has photo: False
2025-07-20 17:29:43,174 - handlers.session_handlers - INFO - Message has video: False
2025-07-20 17:29:43,174 - handlers.session_handlers - INFO - Message has document: False
2025-07-20 17:29:43,174 - handlers.session_handlers - INFO - Message text: '<EMAIL>'
2025-07-20 17:29:49,096 - __main__ - INFO - Received signal 2, initiating shutdown...
2025-07-20 17:29:49,096 - __main__ - INFO - Shutting down bot...
2025-07-20 17:29:52,584 - apscheduler.scheduler - INFO - Scheduler has been shut down
2025-07-20 17:29:52,632 - config.database - INFO - Disconnected from MongoDB
2025-07-20 17:29:52,633 - __main__ - INFO - Bot shutdown completed
2025-07-20 17:30:55,359 - __main__ - INFO - Connecting to MongoDB...
2025-07-20 17:31:09,242 - config.database - INFO - Successfully connected to MongoDB: referral_bot
2025-07-20 17:31:10,753 - config.database - INFO - Database collections and indexes initialized successfully
2025-07-20 17:31:10,753 - __main__ - INFO - Initializing Telegram bot...
2025-07-20 17:31:11,278 - __main__ - INFO - All handlers registered successfully
2025-07-20 17:31:12,125 - services.admin_service - INFO - Initialized admin settings for 4 admins
2025-07-20 17:31:12,160 - __main__ - INFO - Bot info updated: @InstantoPayBot (InstantoPay)
2025-07-20 17:31:12,160 - __main__ - INFO - Admin settings and level rewards system initialized
2025-07-20 17:31:12,161 - __main__ - INFO - Bot initialization completed successfully
2025-07-20 17:31:12,161 - __main__ - INFO - Starting bot polling...
2025-07-20 17:31:12,349 - apscheduler.scheduler - INFO - Scheduler started
2025-07-20 17:31:12,535 - __main__ - INFO - Bot is now running and polling for updates...
2025-07-20 17:31:13,489 - handlers.callback_handlers - ERROR - Error in internal callback handler for data 'set account_number': Query is too old and response timeout expired or query id is invalid
2025-07-20 17:31:13,489 - handlers.callback_handlers - ERROR - Error in callback handler for data 'set account_number': Query is too old and response timeout expired or query id is invalid
2025-07-20 17:31:14,224 - handlers.callback_handlers - ERROR - Error in internal callback handler for data 'set account_number': Query is too old and response timeout expired or query id is invalid
2025-07-20 17:31:14,225 - handlers.callback_handlers - ERROR - Error in callback handler for data 'set account_number': Query is too old and response timeout expired or query id is invalid
2025-07-20 17:31:14,939 - handlers.callback_handlers - ERROR - Error in internal callback handler for data 'customReferralLink': Query is too old and response timeout expired or query id is invalid
2025-07-20 17:31:14,939 - handlers.callback_handlers - ERROR - Error in callback handler for data 'customReferralLink': Query is too old and response timeout expired or query id is invalid
2025-07-20 17:31:15,644 - handlers.callback_handlers - ERROR - Error in internal callback handler for data 'customReferralLink': Query is too old and response timeout expired or query id is invalid
2025-07-20 17:31:15,644 - handlers.callback_handlers - ERROR - Error in callback handler for data 'customReferralLink': Query is too old and response timeout expired or query id is invalid
2025-07-20 17:31:53,859 - handlers.session_handlers - INFO - === SESSION MESSAGE HANDLER for user **********, step: set_account_number ===
2025-07-20 17:31:53,860 - handlers.session_handlers - INFO - Session data: {'_id': ObjectId('687cda8e7104627ee203495b'), 'user_id': **********, 'created_at': **********, 'data': {}, 'step': 'set_account_number', 'updated_at': **********}
2025-07-20 17:31:53,860 - handlers.session_handlers - INFO - Message has text: True
2025-07-20 17:31:53,860 - handlers.session_handlers - INFO - Message has photo: False
2025-07-20 17:31:53,860 - handlers.session_handlers - INFO - Message has video: False
2025-07-20 17:31:53,860 - handlers.session_handlers - INFO - Message has document: False
2025-07-20 17:31:53,860 - handlers.session_handlers - INFO - Message text: '***********'
2025-07-20 17:32:46,344 - handlers.session_handlers - INFO - === SESSION MESSAGE HANDLER for user **********, step: set_mobile_number ===
2025-07-20 17:32:46,345 - handlers.session_handlers - INFO - Session data: {'_id': ObjectId('687cdad37104627ee203495d'), 'user_id': **********, 'created_at': **********, 'data': {}, 'step': 'set_mobile_number', 'updated_at': **********}
2025-07-20 17:32:46,345 - handlers.session_handlers - INFO - Message has text: True
2025-07-20 17:32:46,345 - handlers.session_handlers - INFO - Message has photo: False
2025-07-20 17:32:46,345 - handlers.session_handlers - INFO - Message has video: False
2025-07-20 17:32:46,345 - handlers.session_handlers - INFO - Message has document: False
2025-07-20 17:32:46,345 - handlers.session_handlers - INFO - Message text: '+917092685825'
2025-07-20 17:32:46,925 - handlers.session_handlers - INFO - Attempting to send OTP to mobile number: +917092685825
2025-07-20 17:32:46,979 - services.withdrawal_service - INFO - Using OTP API key from environment variable
2025-07-20 17:32:46,979 - services.withdrawal_service - INFO - Converted +917092685825 to API format: 7092685825
2025-07-20 17:32:46,980 - services.withdrawal_service - INFO - Sending OTP to +917092685825 (API format: 7092685825) using URL: https://sms.renflair.in/V1.php
2025-07-20 17:32:48,395 - services.withdrawal_service - INFO - OTP API response for +917092685825: Status 200, Content: {"return":true,"request_id":"TV2kpXorcHDGNPl","message":"SMS sent successfully.","status":"SUCCESS"}
2025-07-20 17:32:48,395 - services.withdrawal_service - INFO - OTP API analysis for +917092685825 (API format: 7092685825): Status=200, HasFailure=False, HasSuccess=True, ResponseLength=100
2025-07-20 17:32:48,395 - services.withdrawal_service - INFO - OTP API call successful for +917092685825. OTP: 4842
2025-07-20 17:32:48,396 - services.withdrawal_service - INFO - Note: SMS delivery may take 1-5 minutes depending on carrier
2025-07-20 17:32:49,252 - handlers.session_handlers - INFO - OTP API call successful for +917092685825, user ********** moved to verification step
2025-07-20 17:33:51,545 - services.user_service - INFO - Created new user: 1774855481 (SYCO DEEP)
2025-07-20 17:33:52,788 - handlers.user_handlers - ERROR - Error sending new user notification: Forbidden: bot is not a member of the channel chat
2025-07-20 17:33:54,167 - handlers.user_handlers - INFO - Notification sent to referrer ********** about new user SYCO DEEP (ID: 1774855481): success
2025-07-20 17:34:01,412 - services.referral_service - INFO - Referral reward processed: 6520878121 earned ₹3 for referring Arsham
2025-07-20 17:34:16,917 - services.user_service - INFO - Created new user: 1147454455 (Ansh Gupta)
2025-07-20 17:34:18,093 - handlers.user_handlers - ERROR - Error sending new user notification: Forbidden: bot is not a member of the channel chat
2025-07-20 17:34:19,371 - handlers.user_handlers - INFO - Notification sent to referrer ********** about new user Ansh Gupta (ID: 1147454455): success
2025-07-20 17:34:46,792 - services.user_service - INFO - Created new user: 968202678 (Akhil)
2025-07-20 17:34:48,099 - handlers.user_handlers - ERROR - Error sending new user notification: Forbidden: bot is not a member of the channel chat
2025-07-20 17:34:49,589 - handlers.user_handlers - INFO - Notification sent to referrer ********** about new user Akhil (ID: 968202678): success
2025-07-20 17:35:16,676 - services.referral_service - INFO - Referral reward processed: ********** earned ₹5 for referring Ansh Gupta
2025-07-20 17:35:55,778 - telegram.ext.Updater - ERROR - Error while getting Updates: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-20 17:35:55,779 - telegram.ext.Updater - ERROR - Exception happened while polling for updates.
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_updater.py", line 715, in _network_loop_retry
    if not await action_cb():
           ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_updater.py", line 384, in polling_action_cb
    raise exc
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_updater.py", line 373, in polling_action_cb
    updates = await self.bot.get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_extbot.py", line 618, in get_updates
    updates = await super().get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 542, in decorator
    result = await func(self, *args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 4164, in get_updates
    await self._post(
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 630, in _post
    return await self._do_post(
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_extbot.py", line 347, in _do_post
    return await super()._do_post(
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 658, in _do_post
    return await request.post(
           ^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\request\_baserequest.py", line 200, in post
    result = await self._request_wrapper(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\request\_baserequest.py", line 381, in _request_wrapper
    raise Conflict(message)
telegram.error.Conflict: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-20 17:36:02,170 - telegram.ext.Updater - ERROR - Error while getting Updates: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-20 17:36:02,170 - telegram.ext.Updater - ERROR - Exception happened while polling for updates.
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_updater.py", line 715, in _network_loop_retry
    if not await action_cb():
           ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_updater.py", line 384, in polling_action_cb
    raise exc
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_updater.py", line 373, in polling_action_cb
    updates = await self.bot.get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_extbot.py", line 618, in get_updates
    updates = await super().get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 542, in decorator
    result = await func(self, *args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 4164, in get_updates
    await self._post(
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 630, in _post
    return await self._do_post(
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_extbot.py", line 347, in _do_post
    return await super()._do_post(
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 658, in _do_post
    return await request.post(
           ^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\request\_baserequest.py", line 200, in post
    result = await self._request_wrapper(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\request\_baserequest.py", line 381, in _request_wrapper
    raise Conflict(message)
telegram.error.Conflict: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-20 17:36:10,068 - telegram.ext.Updater - ERROR - Error while getting Updates: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-20 17:36:10,069 - telegram.ext.Updater - ERROR - Exception happened while polling for updates.
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_updater.py", line 715, in _network_loop_retry
    if not await action_cb():
           ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_updater.py", line 384, in polling_action_cb
    raise exc
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_updater.py", line 373, in polling_action_cb
    updates = await self.bot.get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_extbot.py", line 618, in get_updates
    updates = await super().get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 542, in decorator
    result = await func(self, *args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 4164, in get_updates
    await self._post(
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 630, in _post
    return await self._do_post(
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_extbot.py", line 347, in _do_post
    return await super()._do_post(
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 658, in _do_post
    return await request.post(
           ^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\request\_baserequest.py", line 200, in post
    result = await self._request_wrapper(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\request\_baserequest.py", line 381, in _request_wrapper
    raise Conflict(message)
telegram.error.Conflict: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-20 17:36:17,207 - telegram.ext.Updater - ERROR - Error while getting Updates: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-20 17:36:17,207 - telegram.ext.Updater - ERROR - Exception happened while polling for updates.
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_updater.py", line 715, in _network_loop_retry
    if not await action_cb():
           ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_updater.py", line 384, in polling_action_cb
    raise exc
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_updater.py", line 373, in polling_action_cb
    updates = await self.bot.get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_extbot.py", line 618, in get_updates
    updates = await super().get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 542, in decorator
    result = await func(self, *args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 4164, in get_updates
    await self._post(
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 630, in _post
    return await self._do_post(
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_extbot.py", line 347, in _do_post
    return await super()._do_post(
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 658, in _do_post
    return await request.post(
           ^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\request\_baserequest.py", line 200, in post
    result = await self._request_wrapper(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\request\_baserequest.py", line 381, in _request_wrapper
    raise Conflict(message)
telegram.error.Conflict: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-20 17:36:17,598 - __main__ - INFO - Received signal 2, initiating shutdown...
2025-07-20 17:36:17,599 - __main__ - INFO - Shutting down bot...
2025-07-20 17:36:20,326 - services.referral_service - INFO - Referral reward processed: ********** earned ₹1 for referring Akhil
2025-07-20 17:36:20,860 - apscheduler.scheduler - INFO - Scheduler has been shut down
2025-07-20 17:36:20,905 - config.database - INFO - Disconnected from MongoDB
2025-07-20 17:36:20,906 - __main__ - INFO - Bot shutdown completed
2025-07-20 20:56:26,882 - __main__ - INFO - Connecting to MongoDB...
2025-07-20 20:56:40,689 - config.database - INFO - Successfully connected to MongoDB: referral_bot
2025-07-20 20:56:42,340 - config.database - INFO - Database collections and indexes initialized successfully
2025-07-20 20:56:42,341 - __main__ - INFO - Initializing Telegram bot...
2025-07-20 20:56:42,842 - __main__ - INFO - All handlers registered successfully
2025-07-20 20:56:43,717 - services.admin_service - INFO - Initialized admin settings for 4 admins
2025-07-20 20:56:43,757 - __main__ - INFO - Bot info updated: @InstantoPayBot (InstantoPay)
2025-07-20 20:56:43,758 - __main__ - INFO - Admin settings and level rewards system initialized
2025-07-20 20:56:43,758 - __main__ - INFO - Bot initialization completed successfully
2025-07-20 20:56:43,758 - __main__ - INFO - Starting bot polling...
2025-07-20 20:56:43,957 - apscheduler.scheduler - INFO - Scheduler started
2025-07-20 20:56:44,155 - __main__ - INFO - Bot is now running and polling for updates...
2025-07-20 20:56:45,331 - handlers.user_handlers - ERROR - Error in handle_joined_channel: Query is too old and response timeout expired or query id is invalid
2025-07-20 20:56:46,112 - handlers.user_handlers - ERROR - Error in handle_joined_channel: Query is too old and response timeout expired or query id is invalid
2025-07-20 20:56:46,492 - handlers.callback_handlers - ERROR - Error in internal callback handler for data 'joined': Message is not modified: specified new message content and reply markup are exactly the same as a current content and reply markup of the message
2025-07-20 20:56:46,493 - handlers.callback_handlers - ERROR - Error in callback handler for data 'joined': Message is not modified: specified new message content and reply markup are exactly the same as a current content and reply markup of the message
2025-07-20 20:56:47,230 - handlers.user_handlers - ERROR - Error in handle_joined_channel: Query is too old and response timeout expired or query id is invalid
2025-07-20 20:56:47,599 - handlers.callback_handlers - ERROR - Error in internal callback handler for data 'joined': Message is not modified: specified new message content and reply markup are exactly the same as a current content and reply markup of the message
2025-07-20 20:56:47,599 - handlers.callback_handlers - ERROR - Error in callback handler for data 'joined': Message is not modified: specified new message content and reply markup are exactly the same as a current content and reply markup of the message
2025-07-20 20:56:48,328 - handlers.user_handlers - ERROR - Error in handle_joined_channel: Query is too old and response timeout expired or query id is invalid
2025-07-20 20:56:48,699 - handlers.callback_handlers - ERROR - Error in internal callback handler for data 'joined': Message is not modified: specified new message content and reply markup are exactly the same as a current content and reply markup of the message
2025-07-20 20:56:48,699 - handlers.callback_handlers - ERROR - Error in callback handler for data 'joined': Message is not modified: specified new message content and reply markup are exactly the same as a current content and reply markup of the message
2025-07-20 20:56:49,428 - handlers.user_handlers - ERROR - Error in handle_joined_channel: Query is too old and response timeout expired or query id is invalid
2025-07-20 20:56:49,803 - handlers.callback_handlers - ERROR - Error in internal callback handler for data 'joined': Message is not modified: specified new message content and reply markup are exactly the same as a current content and reply markup of the message
2025-07-20 20:56:49,803 - handlers.callback_handlers - ERROR - Error in callback handler for data 'joined': Message is not modified: specified new message content and reply markup are exactly the same as a current content and reply markup of the message
2025-07-20 20:56:50,639 - handlers.user_handlers - ERROR - Error in handle_joined_channel: Query is too old and response timeout expired or query id is invalid
2025-07-20 20:56:51,093 - handlers.callback_handlers - ERROR - Error in internal callback handler for data 'joined': Message is not modified: specified new message content and reply markup are exactly the same as a current content and reply markup of the message
2025-07-20 20:56:51,093 - handlers.callback_handlers - ERROR - Error in callback handler for data 'joined': Message is not modified: specified new message content and reply markup are exactly the same as a current content and reply markup of the message
2025-07-20 20:56:51,914 - handlers.user_handlers - ERROR - Error in handle_joined_channel: Query is too old and response timeout expired or query id is invalid
2025-07-20 20:56:52,277 - handlers.callback_handlers - ERROR - Error in internal callback handler for data 'joined': Message is not modified: specified new message content and reply markup are exactly the same as a current content and reply markup of the message
2025-07-20 20:56:52,277 - handlers.callback_handlers - ERROR - Error in callback handler for data 'joined': Message is not modified: specified new message content and reply markup are exactly the same as a current content and reply markup of the message
2025-07-20 20:56:53,038 - handlers.user_handlers - ERROR - Error in handle_joined_channel: Query is too old and response timeout expired or query id is invalid
2025-07-20 20:56:53,408 - handlers.callback_handlers - ERROR - Error in internal callback handler for data 'joined': Message is not modified: specified new message content and reply markup are exactly the same as a current content and reply markup of the message
2025-07-20 20:56:53,408 - handlers.callback_handlers - ERROR - Error in callback handler for data 'joined': Message is not modified: specified new message content and reply markup are exactly the same as a current content and reply markup of the message
2025-07-20 20:57:03,721 - services.referral_service - INFO - Referral reward processed: 7342085549 earned ₹4 for referring Kesu
2025-07-20 20:57:04,364 - handlers.user_handlers - INFO - Custom referral parameter used: Gift -> User ID: **********
2025-07-20 20:57:50,926 - services.user_service - INFO - Created new user: 7424323391 (☠️☠️)
2025-07-20 20:57:52,205 - handlers.user_handlers - ERROR - Error sending new user notification: Forbidden: bot is not a member of the channel chat
2025-07-20 20:57:53,499 - handlers.user_handlers - INFO - Notification sent to referrer ********** about new user ☠️☠️ (ID: 7424323391): success
2025-07-20 20:58:16,776 - handlers.user_handlers - INFO - Custom referral parameter used: from_channel -> User ID: **********
2025-07-20 20:58:27,878 - services.referral_service - INFO - Referral reward processed: ********** earned ₹3 for referring ☠️☠️
2025-07-20 20:59:16,233 - handlers.user_handlers - INFO - Custom referral parameter used: from_channel -> User ID: **********
2025-07-20 21:01:23,734 - services.user_service - INFO - Created new user: 7521618535 (Md)
2025-07-20 21:01:25,314 - handlers.user_handlers - ERROR - Error sending new user notification: Forbidden: bot is not a member of the channel chat
2025-07-20 21:01:27,101 - handlers.user_handlers - INFO - Notification sent to referrer 7807244479 about new user Md (ID: 7521618535): success
2025-07-20 21:01:58,744 - services.referral_service - INFO - Referral reward processed: 7807244479 earned ₹1 for referring Md
2025-07-20 21:02:27,769 - services.user_service - INFO - Created new user: 1944098804 (Fezin)
2025-07-20 21:02:29,103 - handlers.user_handlers - ERROR - Error sending new user notification: Forbidden: bot is not a member of the channel chat
2025-07-20 21:02:30,783 - handlers.user_handlers - ERROR - Error notifying referrer 5196027722: Forbidden: bot was blocked by the user
2025-07-20 21:02:48,926 - utils.helpers - WARNING - Sync is_admin() called from async context for user 6236422355 - consider using is_admin_async()
2025-07-20 21:02:48,928 - services.admin_service - ERROR - Error getting admin settings for **********: Task <Task pending name='Task-33' coro=<_check_admin_in_new_loop() running at C:\Users\<USER>\Desktop\referbot\python\utils\helpers.py:535> cb=[_run_until_complete_cb() at C:\Python312\Lib\asyncio\base_events.py:182]> got Future <Future pending cb=[_chain_future.<locals>._call_check_cancel() at C:\Python312\Lib\asyncio\futures.py:391]> attached to a different loop
2025-07-20 21:03:15,042 - __main__ - INFO - Connecting to MongoDB...
2025-07-20 21:03:28,654 - config.database - INFO - Successfully connected to MongoDB: referral_bot
2025-07-20 21:03:30,225 - utils.helpers - ERROR - Failed to send message to 5196027722: Forbidden: bot was blocked by the user
2025-07-20 21:03:30,270 - services.referral_service - INFO - Referral reward processed: 5196027722 earned ₹4 for referring Fezin
2025-07-20 21:03:30,280 - config.database - INFO - Database collections and indexes initialized successfully
2025-07-20 21:03:30,281 - __main__ - INFO - Initializing Telegram bot...
2025-07-20 21:03:30,688 - __main__ - INFO - All handlers registered successfully
2025-07-20 21:03:31,535 - services.admin_service - INFO - Initialized admin settings for 4 admins
2025-07-20 21:03:31,578 - __main__ - INFO - Bot info updated: @InstantoPayBot (InstantoPay)
2025-07-20 21:03:31,578 - __main__ - INFO - Admin settings and level rewards system initialized
2025-07-20 21:03:31,579 - __main__ - INFO - Bot initialization completed successfully
2025-07-20 21:03:31,579 - __main__ - INFO - Starting bot polling...
2025-07-20 21:03:31,780 - apscheduler.scheduler - INFO - Scheduler started
2025-07-20 21:03:31,980 - __main__ - INFO - Bot is now running and polling for updates...
2025-07-20 21:03:32,566 - telegram.ext.Updater - ERROR - Error while getting Updates: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-20 21:03:32,566 - telegram.ext.Updater - ERROR - Exception happened while polling for updates.
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_updater.py", line 715, in _network_loop_retry
    if not await action_cb():
           ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_updater.py", line 384, in polling_action_cb
    raise exc
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_updater.py", line 373, in polling_action_cb
    updates = await self.bot.get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_extbot.py", line 618, in get_updates
    updates = await super().get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 542, in decorator
    result = await func(self, *args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 4164, in get_updates
    await self._post(
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 630, in _post
    return await self._do_post(
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_extbot.py", line 347, in _do_post
    return await super()._do_post(
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 658, in _do_post
    return await request.post(
           ^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\request\_baserequest.py", line 200, in post
    result = await self._request_wrapper(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\request\_baserequest.py", line 381, in _request_wrapper
    raise Conflict(message)
telegram.error.Conflict: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-20 21:03:37,277 - telegram.ext.Updater - ERROR - Error while getting Updates: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-20 21:03:37,277 - telegram.ext.Updater - ERROR - Exception happened while polling for updates.
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_updater.py", line 715, in _network_loop_retry
    if not await action_cb():
           ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_updater.py", line 384, in polling_action_cb
    raise exc
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_updater.py", line 373, in polling_action_cb
    updates = await self.bot.get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_extbot.py", line 618, in get_updates
    updates = await super().get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 542, in decorator
    result = await func(self, *args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 4164, in get_updates
    await self._post(
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 630, in _post
    return await self._do_post(
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_extbot.py", line 347, in _do_post
    return await super()._do_post(
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 658, in _do_post
    return await request.post(
           ^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\request\_baserequest.py", line 200, in post
    result = await self._request_wrapper(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\request\_baserequest.py", line 381, in _request_wrapper
    raise Conflict(message)
telegram.error.Conflict: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-20 21:03:38,974 - telegram.ext.Updater - ERROR - Error while getting Updates: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-20 21:03:38,975 - telegram.ext.Updater - ERROR - Exception happened while polling for updates.
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_updater.py", line 715, in _network_loop_retry
    if not await action_cb():
           ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_updater.py", line 384, in polling_action_cb
    raise exc
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_updater.py", line 373, in polling_action_cb
    updates = await self.bot.get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_extbot.py", line 618, in get_updates
    updates = await super().get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 542, in decorator
    result = await func(self, *args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 4164, in get_updates
    await self._post(
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 630, in _post
    return await self._do_post(
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_extbot.py", line 347, in _do_post
    return await super()._do_post(
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 658, in _do_post
    return await request.post(
           ^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\request\_baserequest.py", line 200, in post
    result = await self._request_wrapper(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\request\_baserequest.py", line 381, in _request_wrapper
    raise Conflict(message)
telegram.error.Conflict: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-20 21:03:44,432 - telegram.ext.Updater - ERROR - Error while getting Updates: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-20 21:03:44,433 - telegram.ext.Updater - ERROR - Exception happened while polling for updates.
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_updater.py", line 715, in _network_loop_retry
    if not await action_cb():
           ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_updater.py", line 384, in polling_action_cb
    raise exc
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_updater.py", line 373, in polling_action_cb
    updates = await self.bot.get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_extbot.py", line 618, in get_updates
    updates = await super().get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 542, in decorator
    result = await func(self, *args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 4164, in get_updates
    await self._post(
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 630, in _post
    return await self._do_post(
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\ext\_extbot.py", line 347, in _do_post
    return await super()._do_post(
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\_bot.py", line 658, in _do_post
    return await request.post(
           ^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\request\_baserequest.py", line 200, in post
    result = await self._request_wrapper(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\telegram\request\_baserequest.py", line 381, in _request_wrapper
    raise Conflict(message)
telegram.error.Conflict: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-20 21:12:07,773 - __main__ - INFO - Connecting to MongoDB...
2025-07-20 21:12:20,876 - config.database - INFO - Successfully connected to MongoDB: referral_bot
2025-07-20 21:12:22,520 - config.database - INFO - Database collections and indexes initialized successfully
2025-07-20 21:12:22,520 - __main__ - INFO - Initializing Telegram bot...
2025-07-20 21:12:23,017 - __main__ - INFO - All handlers registered successfully
2025-07-20 21:12:23,923 - services.admin_service - INFO - Initialized admin settings for 4 admins
2025-07-20 21:12:23,965 - __main__ - INFO - Bot info updated: @InstantoPayBot (InstantoPay)
2025-07-20 21:12:23,965 - __main__ - INFO - Admin settings and level rewards system initialized
2025-07-20 21:12:23,965 - __main__ - INFO - Bot initialization completed successfully
2025-07-20 21:12:23,966 - __main__ - INFO - Starting bot polling...
2025-07-20 21:12:24,168 - apscheduler.scheduler - INFO - Scheduler started
2025-07-20 21:12:24,370 - __main__ - INFO - Bot is now running and polling for updates...
2025-07-20 21:12:35,517 - services.user_service - INFO - Created new user: 8063997884 (♛𝙆𝙞𝙣𝙜♛)
2025-07-20 21:12:36,691 - handlers.user_handlers - ERROR - Error sending new user notification: Forbidden: bot is not a member of the channel chat
2025-07-20 21:12:38,033 - handlers.user_handlers - INFO - Notification sent to referrer ********** about new user ♛𝙆𝙞𝙣𝙜♛ (ID: 8063997884): success
2025-07-20 21:12:41,855 - services.user_service - INFO - Created new user: 1176592730 (Dhanush)
2025-07-20 21:12:43,136 - handlers.user_handlers - ERROR - Error sending new user notification: Forbidden: bot is not a member of the channel chat
2025-07-20 21:12:44,457 - handlers.user_handlers - INFO - Notification sent to referrer 1297436752 about new user Dhanush (ID: 1176592730): success
2025-07-20 21:12:49,714 - handlers.user_handlers - INFO - Custom referral parameter used: from_channel -> User ID: **********
2025-07-20 21:12:51,736 - handlers.callback_handlers - ERROR - Error in internal callback handler for data 'set name': Query is too old and response timeout expired or query id is invalid
2025-07-20 21:12:51,736 - handlers.callback_handlers - ERROR - Error in callback handler for data 'set name': Query is too old and response timeout expired or query id is invalid
2025-07-20 21:12:52,464 - handlers.callback_handlers - ERROR - Error in internal callback handler for data 'set name': Query is too old and response timeout expired or query id is invalid
2025-07-20 21:12:52,465 - handlers.callback_handlers - ERROR - Error in callback handler for data 'set name': Query is too old and response timeout expired or query id is invalid
2025-07-20 21:12:52,956 - handlers.user_handlers - INFO - Custom referral parameter used: from_channel -> User ID: **********
2025-07-20 21:13:44,112 - services.user_service - INFO - Created new user: 5438287394 (Tejas)
2025-07-20 21:13:45,307 - handlers.user_handlers - ERROR - Error sending new user notification: Forbidden: bot is not a member of the channel chat
2025-07-20 21:13:46,829 - handlers.user_handlers - INFO - Notification sent to referrer ********** about new user Tejas (ID: 5438287394): success
2025-07-20 21:14:28,507 - handlers.callback_handlers - ERROR - Error in smart navigation: Message is not modified: specified new message content and reply markup are exactly the same as a current content and reply markup of the message
2025-07-20 21:14:32,525 - handlers.user_handlers - INFO - Custom referral parameter used: from_channel -> User ID: **********
2025-07-20 21:14:37,622 - handlers.callback_handlers - ERROR - Error showing withdrawal confirmation: Message is not modified: specified new message content and reply markup are exactly the same as a current content and reply markup of the message
2025-07-20 21:14:39,898 - services.withdrawal_service - INFO - Withdrawal record saved for user 7342085549: withdrawal_1753026279.549bc70f
2025-07-20 21:14:49,035 - handlers.callback_handlers - ERROR - Error in _handle_withdraw_amount: Message is not modified: specified new message content and reply markup are exactly the same as a current content and reply markup of the message
2025-07-20 21:14:51,168 - handlers.user_handlers - INFO - Custom referral parameter used: from_channel -> User ID: **********
2025-07-20 21:14:58,076 - services.withdrawal_service - INFO - Withdrawal record saved for user **********: withdrawal_1753026298.292e3144
2025-07-20 21:15:34,547 - services.referral_service - INFO - Referral reward processed: 783073136 earned ₹1 for referring Mukesh
2025-07-20 21:16:40,447 - handlers.admin_handlers - INFO - Loaded 8 pending withdrawals in 0.439s
2025-07-20 21:16:40,862 - handlers.admin_handlers - INFO - handle_view_pending_withdrawals completed in 0.853s
2025-07-20 21:17:30,467 - handlers.callback_handlers - INFO - Processing withdrawal reject for user 7342085549 by admin **********
2025-07-20 21:17:31,626 - handlers.callback_handlers - INFO - Processing reject for user 7342085549, amount: ₹100
2025-07-20 21:17:32,086 - services.withdrawal_service - INFO - Starting withdrawal rejection for user 7342085549 by admin **********
2025-07-20 21:17:32,159 - services.withdrawal_service - INFO - Rejecting withdrawal: User 7342085549, Amount ₹100
2025-07-20 21:17:32,253 - services.withdrawal_service - INFO - Successfully updated user balance for rejection: User 7342085549
2025-07-20 21:17:32,296 - services.withdrawal_service - INFO - Successfully updated withdrawal record status for user 7342085549
2025-07-20 21:17:33,231 - services.withdrawal_service - INFO - Successfully sent rejection notification to user 7342085549
2025-07-20 21:17:33,232 - services.withdrawal_service - INFO - Withdrawal rejected successfully: User 7342085549, Amount ₹100
2025-07-20 21:17:34,062 - handlers.callback_handlers - INFO - Withdrawal rejected successfully: User 7342085549, Amount ₹100
2025-07-20 21:17:48,259 - handlers.callback_handlers - INFO - Processing withdrawal reject for user ********** by admin **********
2025-07-20 21:17:49,321 - handlers.callback_handlers - INFO - Processing reject for user **********, amount: ₹100
2025-07-20 21:17:49,756 - services.withdrawal_service - INFO - Starting withdrawal rejection for user ********** by admin **********
2025-07-20 21:17:49,834 - services.withdrawal_service - INFO - Rejecting withdrawal: User **********, Amount ₹100
2025-07-20 21:17:49,928 - services.withdrawal_service - INFO - Successfully updated user balance for rejection: User **********
2025-07-20 21:17:49,982 - services.withdrawal_service - INFO - Successfully updated withdrawal record status for user **********
2025-07-20 21:17:50,852 - services.withdrawal_service - INFO - Successfully sent rejection notification to user **********
2025-07-20 21:17:50,852 - services.withdrawal_service - INFO - Withdrawal rejected successfully: User **********, Amount ₹100
2025-07-20 21:17:51,598 - handlers.callback_handlers - INFO - Withdrawal rejected successfully: User **********, Amount ₹100
2025-07-20 21:17:53,822 - __main__ - INFO - Received signal 2, initiating shutdown...
2025-07-20 21:17:53,824 - __main__ - INFO - Shutting down bot...
2025-07-20 21:17:54,539 - apscheduler.scheduler - INFO - Scheduler has been shut down
2025-07-20 21:17:54,581 - config.database - INFO - Disconnected from MongoDB
2025-07-20 21:17:54,582 - __main__ - INFO - Bot shutdown completed
2025-07-20 21:18:00,827 - __main__ - INFO - Connecting to MongoDB...
2025-07-20 21:18:13,494 - __main__ - INFO - Bot stopped by user
2025-07-20 21:18:24,297 - __main__ - INFO - Connecting to MongoDB...
2025-07-20 21:18:37,163 - config.database - INFO - Successfully connected to MongoDB: referral_bot
2025-07-20 21:18:38,738 - config.database - INFO - Database collections and indexes initialized successfully
2025-07-20 21:18:38,739 - __main__ - INFO - Initializing Telegram bot...
2025-07-20 21:18:39,180 - __main__ - INFO - All handlers registered successfully
2025-07-20 21:18:40,043 - services.admin_service - INFO - Initialized admin settings for 4 admins
2025-07-20 21:18:40,082 - __main__ - INFO - Bot info updated: @InstantoPayBot (InstantoPay)
2025-07-20 21:18:40,083 - __main__ - INFO - Admin settings and level rewards system initialized
2025-07-20 21:18:40,084 - __main__ - INFO - Bot initialization completed successfully
2025-07-20 21:18:40,085 - __main__ - INFO - Starting bot polling...
2025-07-20 21:18:40,289 - apscheduler.scheduler - INFO - Scheduler started
2025-07-20 21:18:40,486 - __main__ - INFO - Bot is now running and polling for updates...
2025-07-20 21:18:44,724 - handlers.callback_handlers - ERROR - Error in internal callback handler for data 'myWallet': Query is too old and response timeout expired or query id is invalid
2025-07-20 21:18:44,725 - handlers.callback_handlers - ERROR - Error in callback handler for data 'myWallet': Query is too old and response timeout expired or query id is invalid
2025-07-20 21:18:45,452 - handlers.callback_handlers - ERROR - Error in internal callback handler for data 'submitTask_task_1752995334.fd08d118': Query is too old and response timeout expired or query id is invalid
2025-07-20 21:18:45,452 - handlers.callback_handlers - ERROR - Error in callback handler for data 'submitTask_task_1752995334.fd08d118': Query is too old and response timeout expired or query id is invalid
2025-07-20 21:18:46,229 - handlers.user_handlers - ERROR - Error in handle_joined_channel: Query is too old and response timeout expired or query id is invalid
2025-07-20 21:18:48,071 - handlers.callback_handlers - INFO - Message contains media, sending new text message instead of editing
2025-07-20 21:18:51,293 - handlers.admin_handlers - INFO - Loaded 6 pending withdrawals in 0.611s
2025-07-20 21:18:51,691 - handlers.admin_handlers - INFO - handle_view_pending_withdrawals completed in 1.009s
2025-07-20 21:18:57,786 - handlers.admin_handlers - INFO - Loaded 6 pending withdrawals in 0.714s
2025-07-20 21:18:58,162 - handlers.admin_handlers - ERROR - Error sending pending withdrawals message: Message is not modified: specified new message content and reply markup are exactly the same as a current content and reply markup of the message
2025-07-20 21:19:27,790 - handlers.callback_handlers - INFO - Message contains media, sending new text message instead of editing
2025-07-20 21:19:34,122 - services.user_service - INFO - Created new user: 7784078311 (Aryannk)
2025-07-20 21:19:35,305 - handlers.user_handlers - ERROR - Error sending new user notification: Forbidden: bot is not a member of the channel chat
2025-07-20 21:19:36,741 - handlers.user_handlers - INFO - Notification sent to referrer ********** about new user Aryannk (ID: 7784078311): success
2025-07-20 21:20:00,945 - services.user_service - INFO - Created new user: ********** (Unique boy)
2025-07-20 21:20:02,179 - handlers.user_handlers - ERROR - Error sending new user notification: Forbidden: bot is not a member of the channel chat
2025-07-20 21:20:03,586 - handlers.user_handlers - INFO - Notification sent to referrer ********** about new user Unique boy (ID: **********): success
2025-07-20 21:20:29,907 - handlers.user_handlers - INFO - Custom referral parameter used: from_channel -> User ID: **********
2025-07-20 21:20:37,396 - services.referral_service - INFO - Referral reward processed: ********** earned ₹3 for referring Unique boy
2025-07-20 21:20:49,806 - services.withdrawal_service - INFO - Withdrawal record saved for user 7342085549: withdrawal_1753026649.6fc43388
2025-07-20 21:22:16,739 - services.withdrawal_service - INFO - Withdrawal approved: User 7342085549, Amount ₹100
2025-07-20 21:22:17,493 - handlers.admin_handlers - INFO - Admin ********** (Kêviñ) approved withdrawal for user 7342085549 (₹100)
2025-07-20 21:23:02,997 - services.user_service - INFO - Created new user: 8157632244 (Sssssssss)
2025-07-20 21:23:04,156 - handlers.user_handlers - ERROR - Error sending new user notification: Forbidden: bot is not a member of the channel chat
2025-07-20 21:23:05,391 - handlers.user_handlers - INFO - Notification sent to referrer ********** about new user Sssssssss (ID: 8157632244): success
2025-07-20 21:23:30,366 - services.referral_service - INFO - Referral reward processed: ********** earned ₹5 for referring Sssssssss
2025-07-20 21:24:16,850 - handlers.callback_handlers - ERROR - Error in smart navigation: Message is not modified: specified new message content and reply markup are exactly the same as a current content and reply markup of the message
2025-07-20 21:24:34,320 - services.withdrawal_service - INFO - Withdrawal record saved for user 7596351024: withdrawal_1753026874.06343bf0
2025-07-20 21:25:49,942 - services.user_service - INFO - Created new user: 5311991558 (rocky)
2025-07-20 21:25:51,102 - handlers.user_handlers - ERROR - Error sending new user notification: Forbidden: bot is not a member of the channel chat
2025-07-20 21:25:52,399 - handlers.user_handlers - INFO - Notification sent to referrer 5204494071 about new user rocky (ID: 5311991558): success
2025-07-20 21:26:56,721 - services.user_service - INFO - Created new user: ********** (Diya)
2025-07-20 21:26:57,915 - handlers.user_handlers - ERROR - Error sending new user notification: Forbidden: bot is not a member of the channel chat
2025-07-20 21:26:59,210 - handlers.user_handlers - INFO - Notification sent to referrer ********** about new user Diya (ID: **********): success
2025-07-20 21:27:01,880 - handlers.session_handlers - INFO - === SESSION MESSAGE HANDLER for user **********, step: set_account_number ===
2025-07-20 21:27:01,881 - handlers.session_handlers - INFO - Session data: {'_id': ObjectId('687d11b97104627ee20349ea'), 'user_id': **********, 'created_at': **********, 'data': {}, 'step': 'set_account_number', 'updated_at': **********}
2025-07-20 21:27:01,881 - handlers.session_handlers - INFO - Message has text: True
2025-07-20 21:27:01,881 - handlers.session_handlers - INFO - Message has photo: False
2025-07-20 21:27:01,881 - handlers.session_handlers - INFO - Message has video: False
2025-07-20 21:27:01,881 - handlers.session_handlers - INFO - Message has document: False
2025-07-20 21:27:01,881 - handlers.session_handlers - INFO - Message text: '************'
2025-07-20 21:27:42,659 - services.user_service - INFO - Created new user: ********** (Dhanush)
2025-07-20 21:27:43,823 - handlers.user_handlers - ERROR - Error sending new user notification: Forbidden: bot is not a member of the channel chat
2025-07-20 21:27:45,123 - handlers.user_handlers - INFO - Notification sent to referrer ********** about new user Dhanush (ID: **********): success
2025-07-20 21:27:46,609 - utils.helpers - WARNING - Sync is_admin() called from async context for user ********** - consider using is_admin_async()
2025-07-20 21:27:46,612 - services.admin_service - ERROR - Error getting admin settings for **********: Task <Task pending name='Task-48' coro=<_check_admin_in_new_loop() running at C:\Users\<USER>\Desktop\referbot\python\utils\helpers.py:535> cb=[_run_until_complete_cb() at C:\Python312\Lib\asyncio\base_events.py:182]> got Future <Future pending cb=[_chain_future.<locals>._call_check_cancel() at C:\Python312\Lib\asyncio\futures.py:391]> attached to a different loop
2025-07-20 21:28:36,798 - services.referral_service - INFO - Referral reward processed: ********** earned ₹1 for referring Diya
2025-07-20 21:28:37,362 - handlers.session_handlers - INFO - === SESSION MESSAGE HANDLER for user **********, step: set_mobile_number ===
2025-07-20 21:28:37,363 - handlers.session_handlers - INFO - Session data: {'_id': ObjectId('687d12117104627ee20349eb'), 'user_id': **********, 'created_at': 1753027088, 'data': {}, 'step': 'set_mobile_number', 'updated_at': 1753027088}
2025-07-20 21:28:37,363 - handlers.session_handlers - INFO - Message has text: True
2025-07-20 21:28:37,363 - handlers.session_handlers - INFO - Message has photo: False
2025-07-20 21:28:37,363 - handlers.session_handlers - INFO - Message has video: False
2025-07-20 21:28:37,364 - handlers.session_handlers - INFO - Message has document: False
2025-07-20 21:28:37,364 - handlers.session_handlers - INFO - Message text: '9257632410'
2025-07-20 21:29:13,539 - handlers.session_handlers - INFO - === SESSION MESSAGE HANDLER for user **********, step: set_mobile_number ===
2025-07-20 21:29:13,540 - handlers.session_handlers - INFO - Session data: {'_id': ObjectId('687d12117104627ee20349eb'), 'user_id': **********, 'created_at': 1753027143, 'data': {}, 'step': 'set_mobile_number', 'updated_at': 1753027143}
2025-07-20 21:29:13,540 - handlers.session_handlers - INFO - Message has text: True
2025-07-20 21:29:13,540 - handlers.session_handlers - INFO - Message has photo: False
2025-07-20 21:29:13,540 - handlers.session_handlers - INFO - Message has video: False
2025-07-20 21:29:13,541 - handlers.session_handlers - INFO - Message has document: False
2025-07-20 21:29:13,541 - handlers.session_handlers - INFO - Message text: '9257432410'
2025-07-20 21:29:21,811 - handlers.admin_handlers - INFO - Loaded 7 pending withdrawals for approval in 0.414s
2025-07-20 21:29:22,245 - handlers.session_handlers - INFO - === SESSION MESSAGE HANDLER for user **********, step: set_mobile_number ===
2025-07-20 21:29:22,245 - handlers.session_handlers - INFO - Session data: {'_id': ObjectId('687d12117104627ee20349eb'), 'user_id': **********, 'created_at': 1753027158, 'data': {}, 'step': 'set_mobile_number', 'updated_at': 1753027158}
2025-07-20 21:29:22,245 - handlers.session_handlers - INFO - Message has text: True
2025-07-20 21:29:22,245 - handlers.session_handlers - INFO - Message has photo: False
2025-07-20 21:29:22,245 - handlers.session_handlers - INFO - Message has video: False
2025-07-20 21:29:22,246 - handlers.session_handlers - INFO - Message has document: False
2025-07-20 21:29:22,246 - handlers.session_handlers - INFO - Message text: '**********'
2025-07-20 21:29:53,560 - handlers.session_handlers - INFO - === SESSION MESSAGE HANDLER for user **********, step: set_account_ifsc ===
2025-07-20 21:29:53,560 - handlers.session_handlers - INFO - Session data: {'_id': ObjectId('687d12117104627ee20349eb'), 'user_id': **********, 'created_at': **********, 'data': {}, 'step': 'set_account_ifsc', 'updated_at': **********}
2025-07-20 21:29:53,561 - handlers.session_handlers - INFO - Message has text: True
2025-07-20 21:29:53,561 - handlers.session_handlers - INFO - Message has photo: False
2025-07-20 21:29:53,561 - handlers.session_handlers - INFO - Message has video: False
2025-07-20 21:29:53,562 - handlers.session_handlers - INFO - Message has document: False
2025-07-20 21:29:53,562 - handlers.session_handlers - INFO - Message text: 'IPOS0000001'
2025-07-20 21:29:55,040 - utils.helpers - INFO - Cached bank details for IFSC: IPOS0000001
2025-07-20 21:31:01,426 - services.user_service - INFO - Created new user: ********* (Lara)
2025-07-20 21:31:02,560 - handlers.user_handlers - ERROR - Error sending new user notification: Forbidden: bot is not a member of the channel chat
2025-07-20 21:31:03,814 - handlers.user_handlers - INFO - Notification sent to referrer ********* about new user Lara (ID: *********): success
2025-07-20 21:31:49,595 - handlers.session_handlers - INFO - === SESSION MESSAGE HANDLER for user **********, step: set_account_number ===
2025-07-20 21:31:49,596 - handlers.session_handlers - INFO - Session data: {'_id': ObjectId('687d12e47104627ee20349ed'), 'user_id': **********, 'created_at': **********, 'data': {}, 'step': 'set_account_number', 'updated_at': **********}
2025-07-20 21:31:49,597 - handlers.session_handlers - INFO - Message has text: True
2025-07-20 21:31:49,597 - handlers.session_handlers - INFO - Message has photo: False
2025-07-20 21:31:49,597 - handlers.session_handlers - INFO - Message has video: False
2025-07-20 21:31:49,598 - handlers.session_handlers - INFO - Message has document: False
2025-07-20 21:31:49,598 - handlers.session_handlers - INFO - Message text: '************'
2025-07-20 21:32:40,604 - services.user_service - INFO - Created new user: ********** (KANHU)
2025-07-20 21:32:41,769 - handlers.user_handlers - ERROR - Error sending new user notification: Forbidden: bot is not a member of the channel chat
2025-07-20 21:32:43,169 - handlers.user_handlers - INFO - Notification sent to referrer ********** about new user KANHU (ID: **********): success
2025-07-20 21:32:57,922 - __main__ - INFO - Received signal 2, initiating shutdown...
2025-07-20 21:32:57,923 - __main__ - INFO - Shutting down bot...
2025-07-20 21:32:58,533 - apscheduler.scheduler - INFO - Scheduler has been shut down
2025-07-20 21:32:58,576 - config.database - INFO - Disconnected from MongoDB
2025-07-20 21:32:58,577 - __main__ - INFO - Bot shutdown completed
2025-07-20 21:33:05,606 - __main__ - INFO - Connecting to MongoDB...
2025-07-20 21:33:18,561 - config.database - INFO - Successfully connected to MongoDB: referral_bot
2025-07-20 21:33:20,235 - config.database - INFO - Database collections and indexes initialized successfully
2025-07-20 21:33:20,235 - __main__ - INFO - Initializing Telegram bot...
2025-07-20 21:33:20,776 - __main__ - INFO - All handlers registered successfully
2025-07-20 21:33:21,661 - services.admin_service - INFO - Initialized admin settings for 4 admins
2025-07-20 21:33:21,702 - __main__ - INFO - Bot info updated: @InstantoPayBot (InstantoPay)
2025-07-20 21:33:21,702 - __main__ - INFO - Admin settings and level rewards system initialized
2025-07-20 21:33:21,702 - __main__ - INFO - Bot initialization completed successfully
2025-07-20 21:33:21,702 - __main__ - INFO - Starting bot polling...
2025-07-20 21:33:21,909 - apscheduler.scheduler - INFO - Scheduler started
2025-07-20 21:33:22,118 - __main__ - INFO - Bot is now running and polling for updates...
2025-07-20 21:33:22,791 - handlers.session_handlers - INFO - === SESSION MESSAGE HANDLER for user **********, step: set_account_email ===
2025-07-20 21:33:22,791 - handlers.session_handlers - INFO - Session data: {'_id': ObjectId('687d132e7104627ee20349ee'), 'user_id': **********, 'created_at': **********, 'data': {}, 'step': 'set_account_email', 'updated_at': **********}
2025-07-20 21:33:22,793 - handlers.session_handlers - INFO - Message has text: True
2025-07-20 21:33:22,793 - handlers.session_handlers - INFO - Message has photo: False
2025-07-20 21:33:22,793 - handlers.session_handlers - INFO - Message has video: False
2025-07-20 21:33:22,793 - handlers.session_handlers - INFO - Message has document: False
2025-07-20 21:33:22,793 - handlers.session_handlers - INFO - Message text: '<EMAIL>'
2025-07-20 21:33:24,672 - handlers.user_handlers - ERROR - Error in handle_joined_channel: Query is too old and response timeout expired or query id is invalid
2025-07-20 21:33:29,202 - handlers.admin_handlers - ERROR - Error in handle_bot_maintenance: Message is not modified: specified new message content and reply markup are exactly the same as a current content and reply markup of the message
2025-07-20 21:33:30,349 - handlers.admin_handlers - ERROR - Error in handle_bot_maintenance: Query is too old and response timeout expired or query id is invalid
2025-07-20 21:33:30,732 - handlers.callback_handlers - ERROR - Error in internal callback handler for data 'bot_maintenance': Message is not modified: specified new message content and reply markup are exactly the same as a current content and reply markup of the message
2025-07-20 21:33:30,732 - handlers.callback_handlers - ERROR - Error in callback handler for data 'bot_maintenance': Message is not modified: specified new message content and reply markup are exactly the same as a current content and reply markup of the message
2025-07-20 21:33:35,148 - services.referral_service - INFO - Referral reward processed: ********** earned ₹2 for referring KANHU
2025-07-20 21:34:04,453 - handlers.session_handlers - INFO - === SESSION MESSAGE HANDLER for user **********, step: set_mobile_number ===
2025-07-20 21:34:04,455 - handlers.session_handlers - INFO - Session data: {'_id': ObjectId('687d13647104627ee20349f1'), 'user_id': **********, 'created_at': 1753027427, 'data': {}, 'step': 'set_mobile_number', 'updated_at': 1753027427}
2025-07-20 21:34:04,455 - handlers.session_handlers - INFO - Message has text: True
2025-07-20 21:34:04,455 - handlers.session_handlers - INFO - Message has photo: False
2025-07-20 21:34:04,456 - handlers.session_handlers - INFO - Message has video: False
2025-07-20 21:34:04,457 - handlers.session_handlers - INFO - Message has document: False
2025-07-20 21:34:04,457 - handlers.session_handlers - INFO - Message text: '9257432410'
2025-07-20 21:34:15,267 - handlers.session_handlers - INFO - === SESSION MESSAGE HANDLER for user **********, step: set_mobile_number ===
2025-07-20 21:34:15,268 - handlers.session_handlers - INFO - Session data: {'_id': ObjectId('687d13647104627ee20349f1'), 'user_id': **********, 'created_at': 1753027448, 'data': {}, 'step': 'set_mobile_number', 'updated_at': 1753027448}
2025-07-20 21:34:15,268 - handlers.session_handlers - INFO - Message has text: True
2025-07-20 21:34:15,268 - handlers.session_handlers - INFO - Message has photo: False
2025-07-20 21:34:15,268 - handlers.session_handlers - INFO - Message has video: False
2025-07-20 21:34:15,268 - handlers.session_handlers - INFO - Message has document: False
2025-07-20 21:34:15,268 - handlers.session_handlers - INFO - Message text: '**********'
2025-07-20 21:34:21,516 - handlers.session_handlers - INFO - === SESSION MESSAGE HANDLER for user **********, step: set_mobile_number ===
2025-07-20 21:34:21,516 - handlers.session_handlers - INFO - Session data: {'_id': ObjectId('687d13647104627ee20349f1'), 'user_id': **********, 'created_at': 1753027458, 'data': {}, 'step': 'set_mobile_number', 'updated_at': 1753027458}
2025-07-20 21:34:21,516 - handlers.session_handlers - INFO - Message has text: True
2025-07-20 21:34:21,516 - handlers.session_handlers - INFO - Message has photo: False
2025-07-20 21:34:21,516 - handlers.session_handlers - INFO - Message has video: False
2025-07-20 21:34:21,516 - handlers.session_handlers - INFO - Message has document: False
2025-07-20 21:34:21,518 - handlers.session_handlers - INFO - Message text: '7878260014'
2025-07-20 21:34:38,015 - handlers.session_handlers - INFO - === SESSION MESSAGE HANDLER for user **********, step: set_mobile_number ===
2025-07-20 21:34:38,016 - handlers.session_handlers - INFO - Session data: {'_id': ObjectId('687d13647104627ee20349f1'), 'user_id': **********, 'created_at': 1753027465, 'data': {}, 'step': 'set_mobile_number', 'updated_at': 1753027465}
2025-07-20 21:34:38,016 - handlers.session_handlers - INFO - Message has text: True
2025-07-20 21:34:38,016 - handlers.session_handlers - INFO - Message has photo: False
2025-07-20 21:34:38,017 - handlers.session_handlers - INFO - Message has video: False
2025-07-20 21:34:38,017 - handlers.session_handlers - INFO - Message has document: False
2025-07-20 21:34:38,017 - handlers.session_handlers - INFO - Message text: '919257432410'
2025-07-20 21:35:01,624 - handlers.session_handlers - INFO - === SESSION MESSAGE HANDLER for user **********, step: set_mobile_number ===
2025-07-20 21:35:01,626 - handlers.session_handlers - INFO - Session data: {'_id': ObjectId('687d13647104627ee20349f1'), 'user_id': **********, 'created_at': 1753027484, 'data': {}, 'step': 'set_mobile_number', 'updated_at': 1753027484}
2025-07-20 21:35:01,627 - handlers.session_handlers - INFO - Message has text: True
2025-07-20 21:35:01,627 - handlers.session_handlers - INFO - Message has photo: False
2025-07-20 21:35:01,628 - handlers.session_handlers - INFO - Message has video: False
2025-07-20 21:35:01,629 - handlers.session_handlers - INFO - Message has document: False
2025-07-20 21:35:01,629 - handlers.session_handlers - INFO - Message text: '+919257432410'
2025-07-20 21:35:02,099 - handlers.session_handlers - INFO - Attempting to send OTP to mobile number: +919257432410
2025-07-20 21:35:02,143 - services.withdrawal_service - INFO - Using OTP API key from environment variable
2025-07-20 21:35:02,144 - services.withdrawal_service - INFO - Converted +919257432410 to API format: 9257432410
2025-07-20 21:35:02,145 - services.withdrawal_service - INFO - Sending OTP to +919257432410 (API format: 9257432410) using URL: https://sms.renflair.in/V1.php
2025-07-20 21:35:03,993 - services.withdrawal_service - INFO - OTP API response for +919257432410: Status 200, Content: {"return":true,"request_id":"GOwRmD0iNexW3h7","message":"SMS sent successfully.","status":"SUCCESS"}
2025-07-20 21:35:03,994 - services.withdrawal_service - INFO - OTP API analysis for +919257432410 (API format: 9257432410): Status=200, HasFailure=False, HasSuccess=True, ResponseLength=100
2025-07-20 21:35:03,995 - services.withdrawal_service - INFO - OTP API call successful for +919257432410. OTP: 7017
2025-07-20 21:35:03,995 - services.withdrawal_service - INFO - Note: SMS delivery may take 1-5 minutes depending on carrier
2025-07-20 21:35:04,487 - handlers.session_handlers - INFO - OTP API call successful for +919257432410, user ********** moved to verification step
2025-07-20 21:35:10,160 - services.user_service - INFO - Created new user: 7801596812 (Ankit)
2025-07-20 21:35:11,327 - handlers.user_handlers - ERROR - Error sending new user notification: Forbidden: bot is not a member of the channel chat
2025-07-20 21:35:12,530 - handlers.user_handlers - INFO - Notification sent to referrer 1297436752 about new user Ankit (ID: 7801596812): success
2025-07-20 21:35:28,447 - handlers.admin_handlers - INFO - Loaded 7 pending withdrawals for approval in 0.408s
2025-07-20 21:35:28,967 - handlers.user_handlers - INFO - Custom referral parameter used: from_channel -> User ID: **********
2025-07-20 21:35:40,570 - services.withdrawal_service - INFO - Withdrawal approved: User **********, Amount ₹100
2025-07-20 21:35:41,361 - handlers.admin_handlers - INFO - Admin ********** (Titanium Bots) approved withdrawal for user ********** (₹100)
2025-07-20 21:35:45,494 - handlers.callback_handlers - ERROR - Error in cancel OTP handler: 'CallbackHandlers' object has no attribute 'session_handlers'
2025-07-20 21:35:49,379 - handlers.user_handlers - INFO - Custom referral parameter used: Gift -> User ID: **********
2025-07-20 21:36:28,221 - handlers.callback_handlers - ERROR - Callback query timeout for data 'promotionReport' from user **********
2025-07-20 21:36:29,400 - handlers.callback_handlers - ERROR - Error in internal callback handler for data 'setAccountInfo': Query is too old and response timeout expired or query id is invalid
2025-07-20 21:36:29,401 - handlers.callback_handlers - ERROR - Error in callback handler for data 'setAccountInfo': Query is too old and response timeout expired or query id is invalid
2025-07-20 21:36:29,895 - handlers.user_handlers - INFO - Custom referral parameter used: Gift -> User ID: **********
2025-07-20 21:36:31,899 - handlers.callback_handlers - ERROR - Error in internal callback handler for data 'withdrawalRecord': Query is too old and response timeout expired or query id is invalid
2025-07-20 21:36:31,900 - handlers.callback_handlers - ERROR - Error in callback handler for data 'withdrawalRecord': Query is too old and response timeout expired or query id is invalid
2025-07-20 21:36:32,403 - handlers.user_handlers - INFO - Custom referral parameter used: Gift -> User ID: **********
2025-07-20 21:36:34,278 - services.user_service - INFO - Created new user: ********* (Diva)
2025-07-20 21:36:35,498 - handlers.user_handlers - ERROR - Error sending new user notification: Forbidden: bot is not a member of the channel chat
2025-07-20 21:36:36,793 - handlers.user_handlers - INFO - Notification sent to referrer 5204494071 about new user Diva (ID: *********): success
2025-07-20 21:36:40,695 - services.user_service - INFO - Created new user: 7336845168 (Reyajul)
2025-07-20 21:36:41,905 - handlers.user_handlers - ERROR - Error sending new user notification: Forbidden: bot is not a member of the channel chat
2025-07-20 21:36:43,345 - handlers.user_handlers - INFO - Notification sent to referrer ********** about new user Reyajul (ID: 7336845168): success
2025-07-20 21:36:50,866 - handlers.callback_handlers - ERROR - Error in smart navigation: Message is not modified: specified new message content and reply markup are exactly the same as a current content and reply markup of the message
2025-07-20 21:37:01,475 - handlers.session_handlers - INFO - === SESSION MESSAGE HANDLER for user **********, step: set_mobile_number ===
2025-07-20 21:37:01,475 - handlers.session_handlers - INFO - Session data: {'_id': ObjectId('687d13647104627ee20349f1'), 'user_id': **********, 'created_at': 1753027613, 'data': {}, 'step': 'set_mobile_number', 'updated_at': 1753027613}
2025-07-20 21:37:01,476 - handlers.session_handlers - INFO - Message has text: True
2025-07-20 21:37:01,476 - handlers.session_handlers - INFO - Message has photo: False
2025-07-20 21:37:01,476 - handlers.session_handlers - INFO - Message has video: False
2025-07-20 21:37:01,477 - handlers.session_handlers - INFO - Message has document: False
2025-07-20 21:37:01,477 - handlers.session_handlers - INFO - Message text: '+91**********'
2025-07-20 21:37:01,910 - handlers.session_handlers - INFO - Attempting to send OTP to mobile number: +91**********
2025-07-20 21:37:01,951 - services.withdrawal_service - INFO - Using OTP API key from environment variable
2025-07-20 21:37:01,951 - services.withdrawal_service - INFO - Converted +91********** to API format: **********
2025-07-20 21:37:01,951 - services.withdrawal_service - INFO - Sending OTP to +91********** (API format: **********) using URL: https://sms.renflair.in/V1.php
2025-07-20 21:37:03,395 - services.withdrawal_service - INFO - OTP API response for +91**********: Status 200, Content: {"return":true,"request_id":"KCEob94ymvIdpeM","message":"SMS sent successfully.","status":"SUCCESS"}
2025-07-20 21:37:03,396 - services.withdrawal_service - INFO - OTP API analysis for +91********** (API format: **********): Status=200, HasFailure=False, HasSuccess=True, ResponseLength=100
2025-07-20 21:37:03,396 - services.withdrawal_service - INFO - OTP API call successful for +91**********. OTP: 5489
2025-07-20 21:37:03,396 - services.withdrawal_service - INFO - Note: SMS delivery may take 1-5 minutes depending on carrier
2025-07-20 21:37:03,918 - handlers.session_handlers - INFO - OTP API call successful for +91**********, user ********** moved to verification step
2025-07-20 21:37:34,206 - handlers.session_handlers - INFO - === SESSION MESSAGE HANDLER for user **********, step: verify_otp ===
2025-07-20 21:37:34,206 - handlers.session_handlers - INFO - Session data: {'_id': ObjectId('687d13647104627ee20349f1'), 'user_id': **********, 'created_at': 1753027623, 'data': {'mobile_number': '+91**********', 'otp': '5489', 'otp_timestamp': 1753027623}, 'step': 'verify_otp', 'updated_at': 1753027623}
2025-07-20 21:37:34,207 - handlers.session_handlers - INFO - Message has text: True
2025-07-20 21:37:34,207 - handlers.session_handlers - INFO - Message has photo: False
2025-07-20 21:37:34,208 - handlers.session_handlers - INFO - Message has video: False
2025-07-20 21:37:34,208 - handlers.session_handlers - INFO - Message has document: False
2025-07-20 21:37:34,208 - handlers.session_handlers - INFO - Message text: '5489'
2025-07-20 21:37:51,794 - handlers.session_handlers - INFO - === SESSION MESSAGE HANDLER for user **********, step: set_account_name ===
2025-07-20 21:37:51,794 - handlers.session_handlers - INFO - Session data: {'_id': ObjectId('687d14507104627ee20349f3'), 'user_id': **********, 'created_at': **********, 'data': {}, 'step': 'set_account_name', 'updated_at': **********}
2025-07-20 21:37:51,795 - handlers.session_handlers - INFO - Message has text: True
2025-07-20 21:37:51,795 - handlers.session_handlers - INFO - Message has photo: False
2025-07-20 21:37:51,795 - handlers.session_handlers - INFO - Message has video: False
2025-07-20 21:37:51,796 - handlers.session_handlers - INFO - Message has document: False
2025-07-20 21:37:51,796 - handlers.session_handlers - INFO - Message text: 'Pankaj bose'
2025-07-20 21:38:59,445 - __main__ - INFO - Received signal 2, initiating shutdown...
2025-07-20 21:38:59,447 - __main__ - INFO - Shutting down bot...
2025-07-20 21:39:00,057 - apscheduler.scheduler - INFO - Scheduler has been shut down
2025-07-20 21:39:00,103 - config.database - INFO - Disconnected from MongoDB
2025-07-20 21:39:00,103 - __main__ - INFO - Bot shutdown completed
2025-07-20 21:39:05,439 - __main__ - INFO - Connecting to MongoDB...
2025-07-20 21:39:18,137 - config.database - INFO - Successfully connected to MongoDB: referral_bot
2025-07-20 21:39:19,955 - config.database - INFO - Database collections and indexes initialized successfully
2025-07-20 21:39:19,955 - __main__ - INFO - Initializing Telegram bot...
2025-07-20 21:39:20,581 - __main__ - INFO - All handlers registered successfully
2025-07-20 21:39:21,464 - services.admin_service - INFO - Initialized admin settings for 4 admins
2025-07-20 21:39:21,507 - __main__ - INFO - Bot info updated: @InstantoPayBot (InstantoPay)
2025-07-20 21:39:21,508 - __main__ - INFO - Admin settings and level rewards system initialized
2025-07-20 21:39:21,508 - __main__ - INFO - Bot initialization completed successfully
2025-07-20 21:39:21,508 - __main__ - INFO - Starting bot polling...
2025-07-20 21:39:21,708 - apscheduler.scheduler - INFO - Scheduler started
2025-07-20 21:39:21,911 - __main__ - INFO - Bot is now running and polling for updates...
2025-07-20 21:39:22,892 - handlers.callback_handlers - ERROR - Error in internal callback handler for data 'cashOut': Query is too old and response timeout expired or query id is invalid
2025-07-20 21:39:22,893 - handlers.callback_handlers - ERROR - Error in callback handler for data 'cashOut': Query is too old and response timeout expired or query id is invalid
2025-07-20 21:39:25,451 - services.user_service - INFO - Created new user: 7255715206 (Sunny)
2025-07-20 21:39:26,717 - handlers.user_handlers - ERROR - Error sending new user notification: Forbidden: bot is not a member of the channel chat
2025-07-20 21:39:28,108 - handlers.user_handlers - INFO - Notification sent to referrer 1297436752 about new user Sunny (ID: 7255715206): success
2025-07-20 21:39:31,956 - services.withdrawal_service - INFO - Withdrawal record saved for user **********: withdrawal_1753027771.fff20088
2025-07-20 21:39:37,101 - handlers.callback_handlers - ERROR - Error in smart navigation: Message is not modified: specified new message content and reply markup are exactly the same as a current content and reply markup of the message
2025-07-20 21:39:43,141 - services.user_service - INFO - Created new user: 7221180786 (Moritz)
2025-07-20 21:39:44,479 - handlers.user_handlers - ERROR - Error sending new user notification: Forbidden: bot is not a member of the channel chat
2025-07-20 21:39:45,833 - handlers.user_handlers - INFO - Notification sent to referrer ********** about new user Moritz (ID: 7221180786): success
2025-07-20 21:39:48,007 - handlers.callback_handlers - INFO - Processing withdrawal reject for user ********** by admin **********
2025-07-20 21:39:49,202 - handlers.callback_handlers - INFO - Processing reject for user **********, amount: ₹100
2025-07-20 21:39:49,656 - services.withdrawal_service - INFO - Starting withdrawal rejection for user ********** by admin **********
2025-07-20 21:39:49,735 - services.withdrawal_service - INFO - Rejecting withdrawal: User **********, Amount ₹100
2025-07-20 21:39:49,832 - services.withdrawal_service - INFO - Successfully updated user balance for rejection: User **********
2025-07-20 21:39:49,880 - services.withdrawal_service - INFO - Successfully updated withdrawal record status for user **********
2025-07-20 21:39:50,682 - services.withdrawal_service - INFO - Successfully sent rejection notification to user **********
2025-07-20 21:39:50,682 - services.withdrawal_service - INFO - Withdrawal rejected successfully: User **********, Amount ₹100
2025-07-20 21:39:51,430 - handlers.callback_handlers - INFO - Withdrawal rejected successfully: User **********, Amount ₹100
2025-07-20 21:40:00,925 - services.referral_service - INFO - Referral reward processed: 1297436752 earned ₹4 for referring Sunny
2025-07-20 21:40:12,240 - services.user_service - INFO - Created new user: 7476898641 (Anita)
2025-07-20 21:40:13,594 - handlers.user_handlers - ERROR - Error sending new user notification: Forbidden: bot is not a member of the channel chat
2025-07-20 21:40:14,998 - handlers.user_handlers - INFO - Notification sent to referrer ********** about new user Anita (ID: 7476898641): success
2025-07-20 21:40:21,442 - services.referral_service - INFO - Referral reward processed: ********** earned ₹5 for referring Moritz
2025-07-20 21:40:53,396 - handlers.callback_handlers - ERROR - Callback query timeout for data 'promotionReport' from user **********
2025-07-20 21:40:54,613 - handlers.user_handlers - ERROR - Error in handle_joined_channel: Query is too old and response timeout expired or query id is invalid
2025-07-20 21:40:55,404 - handlers.callback_handlers - ERROR - Error in internal callback handler for data 'withdrawalRecord': Query is too old and response timeout expired or query id is invalid
2025-07-20 21:40:55,405 - handlers.callback_handlers - ERROR - Error in callback handler for data 'withdrawalRecord': Query is too old and response timeout expired or query id is invalid
2025-07-20 21:40:55,961 - handlers.user_handlers - INFO - Custom referral parameter used: Gift -> User ID: **********
2025-07-20 21:40:57,816 - handlers.callback_handlers - ERROR - Error in internal callback handler for data 'customReferralLink': Query is too old and response timeout expired or query id is invalid
2025-07-20 21:40:57,817 - handlers.callback_handlers - ERROR - Error in callback handler for data 'customReferralLink': Query is too old and response timeout expired or query id is invalid
2025-07-20 21:41:29,466 - __main__ - INFO - Received signal 2, initiating shutdown...
2025-07-20 21:41:29,466 - __main__ - INFO - Shutting down bot...
2025-07-20 21:41:30,106 - apscheduler.scheduler - INFO - Scheduler has been shut down
2025-07-20 21:41:30,143 - config.database - INFO - Disconnected from MongoDB
2025-07-20 21:41:30,143 - __main__ - INFO - Bot shutdown completed
2025-07-20 21:41:35,877 - __main__ - INFO - Connecting to MongoDB...
2025-07-20 21:41:48,656 - config.database - INFO - Successfully connected to MongoDB: referral_bot
2025-07-20 21:41:50,371 - config.database - INFO - Database collections and indexes initialized successfully
2025-07-20 21:41:50,373 - __main__ - INFO - Initializing Telegram bot...
2025-07-20 21:41:50,779 - __main__ - INFO - All handlers registered successfully
2025-07-20 21:41:51,670 - services.admin_service - INFO - Initialized admin settings for 4 admins
2025-07-20 21:41:51,715 - __main__ - INFO - Bot info updated: @InstantoPayBot (InstantoPay)
2025-07-20 21:41:51,715 - __main__ - INFO - Admin settings and level rewards system initialized
2025-07-20 21:41:51,715 - __main__ - INFO - Bot initialization completed successfully
2025-07-20 21:41:51,716 - __main__ - INFO - Starting bot polling...
2025-07-20 21:41:51,910 - apscheduler.scheduler - INFO - Scheduler started
2025-07-20 21:41:52,113 - __main__ - INFO - Bot is now running and polling for updates...
2025-07-20 21:41:52,891 - handlers.user_handlers - INFO - Custom referral parameter used: Gift -> User ID: **********
2025-07-20 21:41:54,743 - handlers.user_handlers - ERROR - Error in handle_joined_channel: Query is too old and response timeout expired or query id is invalid
2025-07-20 21:42:16,930 - handlers.session_handlers - INFO - === SESSION MESSAGE HANDLER for user **********, step: redeem_gift_code ===
2025-07-20 21:42:16,931 - handlers.session_handlers - INFO - Session data: {'_id': ObjectId('687d14d27104627ee20349f5'), 'user_id': **********, 'created_at': 1753027933, 'data': {}, 'step': 'redeem_gift_code', 'updated_at': 1753027933}
2025-07-20 21:42:16,931 - handlers.session_handlers - INFO - Message has text: True
2025-07-20 21:42:16,931 - handlers.session_handlers - INFO - Message has photo: False
2025-07-20 21:42:16,931 - handlers.session_handlers - INFO - Message has video: False
2025-07-20 21:42:16,932 - handlers.session_handlers - INFO - Message has document: False
2025-07-20 21:42:16,932 - handlers.session_handlers - INFO - Message text: 'B7XKQ9E2T6LZ1FVC34YM'
2025-07-20 21:42:26,369 - __main__ - INFO - Received signal 2, initiating shutdown...
2025-07-20 21:42:26,369 - __main__ - INFO - Shutting down bot...
2025-07-20 21:42:26,979 - apscheduler.scheduler - INFO - Scheduler has been shut down
2025-07-20 21:42:27,019 - config.database - INFO - Disconnected from MongoDB
2025-07-20 21:42:27,020 - __main__ - INFO - Bot shutdown completed
2025-07-20 21:42:32,318 - __main__ - INFO - Connecting to MongoDB...
2025-07-20 21:42:44,988 - config.database - INFO - Successfully connected to MongoDB: referral_bot
2025-07-20 21:42:46,579 - config.database - INFO - Database collections and indexes initialized successfully
2025-07-20 21:42:46,580 - __main__ - INFO - Initializing Telegram bot...
2025-07-20 21:42:47,083 - __main__ - INFO - All handlers registered successfully
2025-07-20 21:42:47,966 - services.admin_service - INFO - Initialized admin settings for 4 admins
2025-07-20 21:42:48,025 - __main__ - INFO - Bot info updated: @InstantoPayBot (InstantoPay)
2025-07-20 21:42:48,025 - __main__ - INFO - Admin settings and level rewards system initialized
2025-07-20 21:42:48,026 - __main__ - INFO - Bot initialization completed successfully
2025-07-20 21:42:48,026 - __main__ - INFO - Starting bot polling...
2025-07-20 21:42:48,229 - apscheduler.scheduler - INFO - Scheduler started
2025-07-20 21:42:48,433 - __main__ - INFO - Bot is now running and polling for updates...
2025-07-20 21:42:49,441 - handlers.user_handlers - ERROR - Error in handle_joined_channel: Query is too old and response timeout expired or query id is invalid
2025-07-20 21:42:55,233 - handlers.user_handlers - INFO - Custom referral parameter used: Gift -> User ID: **********
2025-07-20 21:44:16,751 - services.user_service - INFO - Created new user: 7939913686 (Murad)
2025-07-20 21:44:17,921 - handlers.user_handlers - ERROR - Error sending new user notification: Forbidden: bot is not a member of the channel chat
2025-07-20 21:44:19,322 - handlers.user_handlers - INFO - Notification sent to referrer ********** about new user Murad (ID: 7939913686): success
2025-07-20 21:44:29,831 - __main__ - INFO - Received signal 2, initiating shutdown...
2025-07-20 21:44:29,831 - __main__ - INFO - Shutting down bot...
2025-07-20 21:44:31,215 - apscheduler.scheduler - INFO - Scheduler has been shut down
2025-07-20 21:44:31,261 - config.database - INFO - Disconnected from MongoDB
2025-07-20 21:44:31,262 - __main__ - INFO - Bot shutdown completed
2025-07-20 21:44:36,502 - __main__ - INFO - Connecting to MongoDB...
2025-07-20 21:44:49,381 - config.database - INFO - Successfully connected to MongoDB: referral_bot
2025-07-20 21:44:51,381 - config.database - INFO - Database collections and indexes initialized successfully
2025-07-20 21:44:51,381 - __main__ - INFO - Initializing Telegram bot...
2025-07-20 21:44:51,952 - __main__ - INFO - All handlers registered successfully
2025-07-20 21:44:52,861 - services.admin_service - INFO - Initialized admin settings for 4 admins
2025-07-20 21:44:52,905 - __main__ - INFO - Bot info updated: @InstantoPayBot (InstantoPay)
2025-07-20 21:44:52,907 - __main__ - INFO - Admin settings and level rewards system initialized
2025-07-20 21:44:52,907 - __main__ - INFO - Bot initialization completed successfully
2025-07-20 21:44:52,907 - __main__ - INFO - Starting bot polling...
2025-07-20 21:44:53,118 - apscheduler.scheduler - INFO - Scheduler started
2025-07-20 21:44:53,327 - __main__ - INFO - Bot is now running and polling for updates...
2025-07-20 21:44:54,341 - handlers.callback_handlers - ERROR - Error in internal callback handler for data 'viewTask_task_1752995334.fd08d118': Query is too old and response timeout expired or query id is invalid
2025-07-20 21:44:54,343 - handlers.callback_handlers - ERROR - Error in callback handler for data 'viewTask_task_1752995334.fd08d118': Query is too old and response timeout expired or query id is invalid
2025-07-20 21:45:26,836 - handlers.callback_handlers - INFO - Message contains media, sending new text message instead of editing
2025-07-20 21:45:42,387 - handlers.session_handlers - INFO - === SESSION MESSAGE HANDLER for user **********, step: submit_task_screenshot ===
2025-07-20 21:45:42,387 - handlers.session_handlers - INFO - Session data: {'_id': ObjectId('687d16207104627ee20349f8'), 'user_id': **********, 'created_at': 1753028127, 'data': {'task_id': 'task_1752995334.fd08d118'}, 'step': 'submit_task_screenshot', 'updated_at': 1753028127}
2025-07-20 21:45:42,387 - handlers.session_handlers - INFO - Message has text: False
2025-07-20 21:45:42,388 - handlers.session_handlers - INFO - Message has photo: True
2025-07-20 21:45:42,388 - handlers.session_handlers - INFO - Message has video: False
2025-07-20 21:45:42,388 - handlers.session_handlers - INFO - Message has document: False
2025-07-20 21:45:42,389 - handlers.session_handlers - INFO - Message text: 'None'
2025-07-20 21:45:43,695 - services.task_service - INFO - Created new task submission: submission_1753028143.3ed4c289
2025-07-20 21:46:13,375 - services.user_service - INFO - Created new user: 7545077608 (Кувончбек)
2025-07-20 21:46:14,766 - handlers.user_handlers - ERROR - Error sending new user notification: Forbidden: bot is not a member of the channel chat
2025-07-20 21:46:16,294 - handlers.user_handlers - INFO - Notification sent to referrer ********** about new user Кувончбек (ID: 7545077608): success
2025-07-20 21:48:08,598 - services.user_service - INFO - Created new user: 7879031742 (Manoj)
2025-07-20 21:48:09,842 - handlers.user_handlers - ERROR - Error sending new user notification: Forbidden: bot is not a member of the channel chat
2025-07-20 21:48:11,376 - handlers.user_handlers - INFO - Notification sent to referrer 7255715206 about new user Manoj (ID: 7879031742): success
2025-07-20 21:48:59,919 - services.referral_service - INFO - Referral reward processed: 7255715206 earned ₹4 for referring Manoj
2025-07-20 21:49:15,628 - services.user_service - INFO - Created new user: 8043479552 (arjun)
2025-07-20 21:49:16,847 - handlers.user_handlers - ERROR - Error sending new user notification: Forbidden: bot is not a member of the channel chat
2025-07-20 21:49:18,263 - handlers.user_handlers - INFO - Notification sent to referrer ********** about new user arjun (ID: 8043479552): success
2025-07-20 21:49:41,792 - services.referral_service - INFO - Referral reward processed: ********** earned ₹2 for referring arjun
2025-07-20 21:50:19,521 - handlers.callback_handlers - INFO - Message contains media, sending new text message instead of editing
2025-07-20 21:50:41,612 - handlers.session_handlers - INFO - === SESSION MESSAGE HANDLER for user 7255715206, step: submit_task_screenshot ===
2025-07-20 21:50:41,612 - handlers.session_handlers - INFO - Session data: {'_id': ObjectId('687d17457104627ee20349fc'), 'user_id': 7255715206, 'created_at': 1753028420, 'data': {'task_id': 'task_1752995334.fd08d118'}, 'step': 'submit_task_screenshot', 'updated_at': 1753028420}
2025-07-20 21:50:41,612 - handlers.session_handlers - INFO - Message has text: False
2025-07-20 21:50:41,613 - handlers.session_handlers - INFO - Message has photo: True
2025-07-20 21:50:41,613 - handlers.session_handlers - INFO - Message has video: False
2025-07-20 21:50:41,613 - handlers.session_handlers - INFO - Message has document: False
2025-07-20 21:50:41,613 - handlers.session_handlers - INFO - Message text: 'None'
2025-07-20 21:50:42,554 - __main__ - INFO - Received signal 2, initiating shutdown...
2025-07-20 21:50:42,554 - __main__ - INFO - Shutting down bot...
2025-07-20 21:50:42,674 - services.task_service - INFO - Created new task submission: submission_1753028442.4ac8117d
2025-07-20 21:50:44,597 - apscheduler.scheduler - INFO - Scheduler has been shut down
2025-07-20 21:50:44,639 - config.database - INFO - Disconnected from MongoDB
2025-07-20 21:50:44,639 - __main__ - INFO - Bot shutdown completed
2025-07-20 21:50:50,449 - __main__ - INFO - Connecting to MongoDB...
2025-07-20 21:51:03,602 - config.database - INFO - Successfully connected to MongoDB: referral_bot
2025-07-20 21:51:05,218 - config.database - INFO - Database collections and indexes initialized successfully
2025-07-20 21:51:05,218 - __main__ - INFO - Initializing Telegram bot...
2025-07-20 21:51:05,653 - __main__ - INFO - All handlers registered successfully
2025-07-20 21:51:06,543 - services.admin_service - INFO - Initialized admin settings for 4 admins
2025-07-20 21:51:06,586 - __main__ - INFO - Bot info updated: @InstantoPayBot (InstantoPay)
2025-07-20 21:51:06,587 - __main__ - INFO - Admin settings and level rewards system initialized
2025-07-20 21:51:06,587 - __main__ - INFO - Bot initialization completed successfully
2025-07-20 21:51:06,587 - __main__ - INFO - Starting bot polling...
2025-07-20 21:51:06,796 - apscheduler.scheduler - INFO - Scheduler started
2025-07-20 21:51:07,006 - __main__ - INFO - Bot is now running and polling for updates...
2025-07-20 21:51:08,097 - handlers.callback_handlers - ERROR - Error in internal callback handler for data 'myWallet': Query is too old and response timeout expired or query id is invalid
2025-07-20 21:51:08,097 - handlers.callback_handlers - ERROR - Error in callback handler for data 'myWallet': Query is too old and response timeout expired or query id is invalid
2025-07-20 21:53:18,334 - services.user_service - INFO - Created new user: 7156912543 (Khan)
2025-07-20 21:53:19,505 - handlers.user_handlers - ERROR - Error sending new user notification: Forbidden: bot is not a member of the channel chat
2025-07-20 21:53:20,841 - handlers.user_handlers - INFO - Notification sent to referrer 6221036040 about new user Khan (ID: 7156912543): success
2025-07-20 21:54:06,107 - __main__ - INFO - Received signal 2, initiating shutdown...
2025-07-20 21:54:06,107 - __main__ - INFO - Shutting down bot...
2025-07-20 21:54:09,812 - apscheduler.scheduler - INFO - Scheduler has been shut down
2025-07-20 21:54:09,857 - config.database - INFO - Disconnected from MongoDB
2025-07-20 21:54:09,863 - __main__ - INFO - Bot shutdown completed
2025-07-20 21:54:32,358 - __main__ - INFO - Connecting to MongoDB...
2025-07-20 21:54:45,338 - config.database - INFO - Successfully connected to MongoDB: referral_bot
2025-07-20 21:54:47,303 - config.database - INFO - Database collections and indexes initialized successfully
2025-07-20 21:54:47,303 - __main__ - INFO - Initializing Telegram bot...
2025-07-20 21:54:47,905 - __main__ - INFO - All handlers registered successfully
2025-07-20 21:54:48,794 - services.admin_service - INFO - Initialized admin settings for 4 admins
2025-07-20 21:54:48,842 - __main__ - INFO - Bot info updated: @InstantoPayBot (InstantoPay)
2025-07-20 21:54:48,842 - __main__ - INFO - Admin settings and level rewards system initialized
2025-07-20 21:54:48,843 - __main__ - INFO - Bot initialization completed successfully
2025-07-20 21:54:48,843 - __main__ - INFO - Starting bot polling...
2025-07-20 21:54:49,046 - apscheduler.scheduler - INFO - Scheduler started
2025-07-20 21:54:49,254 - __main__ - INFO - Bot is now running and polling for updates...
2025-07-20 21:55:10,535 - services.user_service - INFO - Created new user: 7351271540 (Rohan)
2025-07-20 21:55:11,731 - handlers.user_handlers - ERROR - Error sending new user notification: Forbidden: bot is not a member of the channel chat
2025-07-20 21:55:13,176 - handlers.user_handlers - INFO - Notification sent to referrer 1495569338 about new user Rohan (ID: 7351271540): success
2025-07-20 21:56:01,551 - services.referral_service - INFO - Referral reward processed: 1495569338 earned ₹5 for referring Rohan
2025-07-20 21:56:32,069 - handlers.callback_handlers - ERROR - Callback query timeout for data 'promotionReport' from user **********
2025-07-20 21:56:34,863 - handlers.callback_handlers - ERROR - Error in internal callback handler for data 'myWallet': Query is too old and response timeout expired or query id is invalid
2025-07-20 21:56:34,863 - handlers.callback_handlers - ERROR - Error in callback handler for data 'myWallet': Query is too old and response timeout expired or query id is invalid
2025-07-20 21:56:36,904 - utils.helpers - WARNING - Sync is_admin() called from async context for user ********** - consider using is_admin_async()
2025-07-20 21:56:36,906 - services.admin_service - ERROR - Error getting admin settings for **********: Task <Task pending name='Task-17' coro=<_check_admin_in_new_loop() running at C:\Users\<USER>\Desktop\referbot\python\utils\helpers.py:535> cb=[_run_until_complete_cb() at C:\Python312\Lib\asyncio\base_events.py:182]> got Future <Future pending cb=[_chain_future.<locals>._call_check_cancel() at C:\Python312\Lib\asyncio\futures.py:391]> attached to a different loop
2025-07-20 21:56:41,239 - utils.helpers - WARNING - Sync is_admin() called from async context for user ********** - consider using is_admin_async()
2025-07-20 21:56:41,242 - services.admin_service - ERROR - Error getting admin settings for **********: Task <Task pending name='Task-20' coro=<_check_admin_in_new_loop() running at C:\Users\<USER>\Desktop\referbot\python\utils\helpers.py:535> cb=[_run_until_complete_cb() at C:\Python312\Lib\asyncio\base_events.py:182]> got Future <Future pending cb=[_chain_future.<locals>._call_check_cancel() at C:\Python312\Lib\asyncio\futures.py:391]> attached to a different loop
2025-07-20 21:56:43,251 - utils.helpers - WARNING - Sync is_admin() called from async context for user ********** - consider using is_admin_async()
2025-07-20 21:56:43,254 - services.admin_service - ERROR - Error getting admin settings for **********: Task <Task pending name='Task-23' coro=<_check_admin_in_new_loop() running at C:\Users\<USER>\Desktop\referbot\python\utils\helpers.py:535> cb=[_run_until_complete_cb() at C:\Python312\Lib\asyncio\base_events.py:182]> got Future <Future pending cb=[_chain_future.<locals>._call_check_cancel() at C:\Python312\Lib\asyncio\futures.py:391]> attached to a different loop
2025-07-20 21:56:44,482 - utils.helpers - WARNING - Sync is_admin() called from async context for user ********** - consider using is_admin_async()
2025-07-20 21:56:44,486 - services.admin_service - ERROR - Error getting admin settings for **********: Task <Task pending name='Task-26' coro=<_check_admin_in_new_loop() running at C:\Users\<USER>\Desktop\referbot\python\utils\helpers.py:535> cb=[_run_until_complete_cb() at C:\Python312\Lib\asyncio\base_events.py:182]> got Future <Future pending cb=[_chain_future.<locals>._call_check_cancel() at C:\Python312\Lib\asyncio\futures.py:391]> attached to a different loop
2025-07-20 21:56:45,745 - utils.helpers - WARNING - Sync is_admin() called from async context for user ********** - consider using is_admin_async()
2025-07-20 21:56:45,747 - services.admin_service - ERROR - Error getting admin settings for **********: Task <Task pending name='Task-29' coro=<_check_admin_in_new_loop() running at C:\Users\<USER>\Desktop\referbot\python\utils\helpers.py:535> cb=[_run_until_complete_cb() at C:\Python312\Lib\asyncio\base_events.py:182]> got Future <Future pending cb=[_chain_future.<locals>._call_check_cancel() at C:\Python312\Lib\asyncio\futures.py:391]> attached to a different loop
2025-07-20 21:56:47,858 - utils.helpers - WARNING - Sync is_admin() called from async context for user ********** - consider using is_admin_async()
2025-07-20 21:56:47,861 - services.admin_service - ERROR - Error getting admin settings for **********: Task <Task pending name='Task-32' coro=<_check_admin_in_new_loop() running at C:\Users\<USER>\Desktop\referbot\python\utils\helpers.py:535> cb=[_run_until_complete_cb() at C:\Python312\Lib\asyncio\base_events.py:182]> got Future <Future pending cb=[_chain_future.<locals>._call_check_cancel() at C:\Python312\Lib\asyncio\futures.py:391]> attached to a different loop
2025-07-20 21:56:49,216 - utils.helpers - WARNING - Sync is_admin() called from async context for user ********** - consider using is_admin_async()
2025-07-20 21:56:49,218 - services.admin_service - ERROR - Error getting admin settings for **********: Task <Task pending name='Task-35' coro=<_check_admin_in_new_loop() running at C:\Users\<USER>\Desktop\referbot\python\utils\helpers.py:535> cb=[_run_until_complete_cb() at C:\Python312\Lib\asyncio\base_events.py:182]> got Future <Future pending cb=[_chain_future.<locals>._call_check_cancel() at C:\Python312\Lib\asyncio\futures.py:391]> attached to a different loop
2025-07-20 21:56:50,354 - utils.helpers - WARNING - Sync is_admin() called from async context for user ********** - consider using is_admin_async()
2025-07-20 21:56:50,357 - services.admin_service - ERROR - Error getting admin settings for **********: Task <Task pending name='Task-38' coro=<_check_admin_in_new_loop() running at C:\Users\<USER>\Desktop\referbot\python\utils\helpers.py:535> cb=[_run_until_complete_cb() at C:\Python312\Lib\asyncio\base_events.py:182]> got Future <Future pending cb=[_chain_future.<locals>._call_check_cancel() at C:\Python312\Lib\asyncio\futures.py:391]> attached to a different loop
2025-07-20 21:56:51,495 - utils.helpers - WARNING - Sync is_admin() called from async context for user ********** - consider using is_admin_async()
2025-07-20 21:56:51,499 - services.admin_service - ERROR - Error getting admin settings for **********: Task <Task pending name='Task-41' coro=<_check_admin_in_new_loop() running at C:\Users\<USER>\Desktop\referbot\python\utils\helpers.py:535> cb=[_run_until_complete_cb() at C:\Python312\Lib\asyncio\base_events.py:182]> got Future <Future pending cb=[_chain_future.<locals>._call_check_cancel() at C:\Python312\Lib\asyncio\futures.py:391]> attached to a different loop
2025-07-20 21:57:17,171 - utils.helpers - WARNING - Sync is_admin() called from async context for user ********** - consider using is_admin_async()
2025-07-20 21:57:17,173 - services.admin_service - ERROR - Error getting admin settings for **********: Task <Task pending name='Task-45' coro=<_check_admin_in_new_loop() running at C:\Users\<USER>\Desktop\referbot\python\utils\helpers.py:535> cb=[_run_until_complete_cb() at C:\Python312\Lib\asyncio\base_events.py:182]> got Future <Future pending cb=[_chain_future.<locals>._call_check_cancel() at C:\Python312\Lib\asyncio\futures.py:391]> attached to a different loop
2025-07-20 21:57:18,373 - utils.helpers - WARNING - Sync is_admin() called from async context for user ********** - consider using is_admin_async()
2025-07-20 21:57:18,375 - services.admin_service - ERROR - Error getting admin settings for **********: Task <Task pending name='Task-48' coro=<_check_admin_in_new_loop() running at C:\Users\<USER>\Desktop\referbot\python\utils\helpers.py:535> cb=[_run_until_complete_cb() at C:\Python312\Lib\asyncio\base_events.py:182]> got Future <Future pending cb=[_chain_future.<locals>._call_check_cancel() at C:\Python312\Lib\asyncio\futures.py:391]> attached to a different loop
2025-07-20 21:59:33,061 - services.user_service - INFO - Created new user: 1550319630 (TG)
2025-07-20 21:59:34,621 - handlers.user_handlers - ERROR - Error sending new user notification: Forbidden: bot is not a member of the channel chat
2025-07-20 21:59:36,045 - handlers.user_handlers - INFO - Notification sent to referrer 7567747492 about new user TG (ID: 1550319630): success
2025-07-20 22:00:43,443 - handlers.session_handlers - INFO - === SESSION MESSAGE HANDLER for user **********, step: add_redeem_code ===
2025-07-20 22:00:43,443 - handlers.session_handlers - INFO - Session data: {'_id': ObjectId('687ccc0c7104627ee2034931'), 'user_id': **********, 'created_at': 1753029011, 'data': {}, 'step': 'add_redeem_code', 'updated_at': 1753029011}
2025-07-20 22:00:43,444 - handlers.session_handlers - INFO - Message has text: True
2025-07-20 22:00:43,444 - handlers.session_handlers - INFO - Message has photo: False
2025-07-20 22:00:43,444 - handlers.session_handlers - INFO - Message has video: False
2025-07-20 22:00:43,444 - handlers.session_handlers - INFO - Message has document: False
2025-07-20 22:00:43,445 - handlers.session_handlers - INFO - Message text: '1-5,1,TEST'
2025-07-20 22:00:54,028 - handlers.session_handlers - INFO - === SESSION MESSAGE HANDLER for user **********, step: redeem_gift_code ===
2025-07-20 22:00:54,028 - handlers.session_handlers - INFO - Session data: {'_id': ObjectId('687d19bb7104627ee20349fe'), 'user_id': **********, 'created_at': 1753029050, 'data': {}, 'step': 'redeem_gift_code', 'updated_at': 1753029050}
2025-07-20 22:00:54,029 - handlers.session_handlers - INFO - Message has text: True
2025-07-20 22:00:54,029 - handlers.session_handlers - INFO - Message has photo: False
2025-07-20 22:00:54,029 - handlers.session_handlers - INFO - Message has video: False
2025-07-20 22:00:54,029 - handlers.session_handlers - INFO - Message has document: False
2025-07-20 22:00:54,029 - handlers.session_handlers - INFO - Message text: 'Test'
2025-07-20 22:01:10,014 - handlers.session_handlers - INFO - === SESSION MESSAGE HANDLER for user **********, step: withdrawal_pagination ===
2025-07-20 22:01:10,015 - handlers.session_handlers - INFO - Session data: {'_id': ObjectId('687d15167104627ee20349f6'), 'user_id': **********, 'created_at': 1753028696, 'data': {'current_page': 1}, 'step': 'withdrawal_pagination', 'updated_at': 1753028696}
2025-07-20 22:01:10,015 - handlers.session_handlers - INFO - Message has text: True
2025-07-20 22:01:10,015 - handlers.session_handlers - INFO - Message has photo: False
2025-07-20 22:01:10,015 - handlers.session_handlers - INFO - Message has video: False
2025-07-20 22:01:10,016 - handlers.session_handlers - INFO - Message has document: False
2025-07-20 22:01:10,016 - handlers.session_handlers - INFO - Message text: 'Test'
2025-07-20 22:01:17,259 - handlers.user_handlers - INFO - Custom referral parameter used: from_channel -> User ID: **********
2025-07-20 22:01:47,431 - handlers.session_handlers - INFO - === SESSION MESSAGE HANDLER for user **********, step: redeem_gift_code ===
2025-07-20 22:01:47,431 - handlers.session_handlers - INFO - Session data: {'_id': ObjectId('687d19f17104627ee2034a00'), 'user_id': **********, 'created_at': 1753029105, 'data': {}, 'step': 'redeem_gift_code', 'updated_at': 1753029105}
2025-07-20 22:01:47,431 - handlers.session_handlers - INFO - Message has text: True
2025-07-20 22:01:47,432 - handlers.session_handlers - INFO - Message has photo: False
2025-07-20 22:01:47,432 - handlers.session_handlers - INFO - Message has video: False
2025-07-20 22:01:47,433 - handlers.session_handlers - INFO - Message has document: False
2025-07-20 22:01:47,433 - handlers.session_handlers - INFO - Message text: 'Test'
2025-07-20 22:02:29,052 - handlers.session_handlers - INFO - === SESSION MESSAGE HANDLER for user **********, step: redeem_gift_code ===
2025-07-20 22:02:29,053 - handlers.session_handlers - INFO - Session data: {'_id': ObjectId('687d1a1d7104627ee2034a02'), 'user_id': **********, 'created_at': 1753029148, 'data': {}, 'step': 'redeem_gift_code', 'updated_at': 1753029148}
2025-07-20 22:02:29,053 - handlers.session_handlers - INFO - Message has text: True
2025-07-20 22:02:29,054 - handlers.session_handlers - INFO - Message has photo: False
2025-07-20 22:02:29,054 - handlers.session_handlers - INFO - Message has video: False
2025-07-20 22:02:29,054 - handlers.session_handlers - INFO - Message has document: False
2025-07-20 22:02:29,054 - handlers.session_handlers - INFO - Message text: 'Test'
2025-07-20 22:02:34,185 - handlers.user_handlers - INFO - Custom referral parameter used: from_channel -> User ID: **********
2025-07-20 22:02:51,716 - services.user_service - INFO - Created new user: 6376146181 (Ankit Sour)
2025-07-20 22:02:53,064 - handlers.user_handlers - ERROR - Error sending new user notification: Forbidden: bot is not a member of the channel chat
2025-07-20 22:02:54,558 - handlers.user_handlers - INFO - Notification sent to referrer ********** about new user Ankit Sour (ID: 6376146181): success
2025-07-20 22:02:57,410 - services.user_service - INFO - Created new user: 6190186508 (Abhi)
2025-07-20 22:02:58,704 - handlers.user_handlers - ERROR - Error sending new user notification: Forbidden: bot is not a member of the channel chat
2025-07-20 22:03:00,143 - handlers.user_handlers - INFO - Notification sent to referrer 5713630710 about new user Abhi (ID: 6190186508): success
2025-07-20 22:03:34,877 - handlers.user_handlers - INFO - Custom referral parameter used: from_channel -> User ID: **********
2025-07-20 22:03:35,045 - services.user_service - INFO - Created new user: 5794528683 (Tarun)
2025-07-20 22:03:36,196 - handlers.user_handlers - ERROR - Error sending new user notification: Forbidden: bot is not a member of the channel chat
2025-07-20 22:03:37,937 - handlers.user_handlers - INFO - Notification sent to referrer ********** about new user Tarun (ID: 5794528683): success
2025-07-20 22:03:46,832 - handlers.session_handlers - INFO - === SESSION MESSAGE HANDLER for user 7150545924, step: redeem_gift_code ===
2025-07-20 22:03:46,832 - handlers.session_handlers - INFO - Session data: {'_id': ObjectId('687d1a4c7104627ee2034a03'), 'user_id': 7150545924, 'created_at': 1753029195, 'data': {}, 'step': 'redeem_gift_code', 'updated_at': 1753029195}
2025-07-20 22:03:46,832 - handlers.session_handlers - INFO - Message has text: True
2025-07-20 22:03:46,832 - handlers.session_handlers - INFO - Message has photo: False
2025-07-20 22:03:46,832 - handlers.session_handlers - INFO - Message has video: False
2025-07-20 22:03:46,834 - handlers.session_handlers - INFO - Message has document: False
2025-07-20 22:03:46,834 - handlers.session_handlers - INFO - Message text: 'B7XKQ9E2T6LZ1FVC34YM'
2025-07-20 22:03:52,852 - services.referral_service - INFO - Referral reward processed: 5713630710 earned ₹1 for referring Abhi
2025-07-20 22:03:57,830 - services.referral_service - INFO - Referral reward processed: ********** earned ₹5 for referring Ankit Sour
2025-07-20 22:04:43,473 - services.user_service - INFO - Created new user: 7091297448 (Ankit)
2025-07-20 22:04:44,680 - handlers.user_handlers - ERROR - Error sending new user notification: Forbidden: bot is not a member of the channel chat
2025-07-20 22:04:46,008 - handlers.user_handlers - INFO - Notification sent to referrer 6376146181 about new user Ankit (ID: 7091297448): success
2025-07-20 22:04:54,284 - handlers.user_handlers - INFO - Custom referral parameter used: from_channel -> User ID: **********
2025-07-20 22:05:15,519 - services.referral_service - INFO - Referral reward processed: 6376146181 earned ₹4 for referring Ankit
2025-07-20 22:06:16,487 - handlers.callback_handlers - INFO - Message contains media, sending new text message instead of editing
2025-07-20 22:06:34,677 - handlers.session_handlers - INFO - === SESSION MESSAGE HANDLER for user 5895006710, step: submit_task_screenshot ===
2025-07-20 22:06:34,679 - handlers.session_handlers - INFO - Session data: {'_id': ObjectId('687d1b027104627ee2034a06'), 'user_id': 5895006710, 'created_at': 1753029377, 'data': {'task_id': 'task_1752995334.fd08d118'}, 'step': 'submit_task_screenshot', 'updated_at': 1753029377}
2025-07-20 22:06:34,679 - handlers.session_handlers - INFO - Message has text: False
2025-07-20 22:06:34,679 - handlers.session_handlers - INFO - Message has photo: True
2025-07-20 22:06:34,679 - handlers.session_handlers - INFO - Message has video: False
2025-07-20 22:06:34,680 - handlers.session_handlers - INFO - Message has document: False
2025-07-20 22:06:34,680 - handlers.session_handlers - INFO - Message text: 'None'
2025-07-20 22:06:35,917 - services.task_service - INFO - Created new task submission: submission_1753029395.17d79dea
2025-07-20 22:06:48,004 - handlers.user_handlers - INFO - Custom referral parameter used: Gift -> User ID: **********
2025-07-20 22:06:53,611 - handlers.callback_handlers - ERROR - Error in smart navigation: Message is not modified: specified new message content and reply markup are exactly the same as a current content and reply markup of the message
2025-07-20 22:07:11,865 - handlers.user_handlers - INFO - Custom referral parameter used: Gift -> User ID: **********
2025-07-20 22:08:03,992 - handlers.user_handlers - INFO - Custom referral parameter used: from_channel -> User ID: **********
2025-07-20 22:08:46,274 - handlers.user_handlers - INFO - Custom referral parameter used: from_channel -> User ID: **********
2025-07-20 22:08:57,353 - handlers.user_handlers - INFO - Custom referral parameter used: from_channel -> User ID: **********
2025-07-20 22:10:11,084 - handlers.session_handlers - INFO - === SESSION MESSAGE HANDLER for user **********, step: link_fixed_amount_step1 ===
2025-07-20 22:10:11,085 - handlers.session_handlers - INFO - Session data: {'_id': ObjectId('687d1ae57104627ee2034a05'), 'user_id': **********, 'created_at': 1753029593, 'data': {}, 'step': 'link_fixed_amount_step1', 'updated_at': 1753029593}
2025-07-20 22:10:11,085 - handlers.session_handlers - INFO - Message has text: True
2025-07-20 22:10:11,085 - handlers.session_handlers - INFO - Message has photo: False
2025-07-20 22:10:11,085 - handlers.session_handlers - INFO - Message has video: False
2025-07-20 22:10:11,086 - handlers.session_handlers - INFO - Message has document: False
2025-07-20 22:10:11,086 - handlers.session_handlers - INFO - Message text: '1'
2025-07-20 22:10:15,298 - handlers.session_handlers - INFO - === SESSION MESSAGE HANDLER for user **********, step: link_fixed_amount_step2 ===
2025-07-20 22:10:15,298 - handlers.session_handlers - INFO - Session data: {'_id': ObjectId('687d1ae57104627ee2034a05'), 'user_id': **********, 'created_at': 1753029593, 'data': {'amount_type': 'fixed', 'amount_value': 1.0}, 'step': 'link_fixed_amount_step2', 'updated_at': 1753029611}
2025-07-20 22:10:15,299 - handlers.session_handlers - INFO - Message has text: True
2025-07-20 22:10:15,299 - handlers.session_handlers - INFO - Message has photo: False
2025-07-20 22:10:15,299 - handlers.session_handlers - INFO - Message has video: False
2025-07-20 22:10:15,299 - handlers.session_handlers - INFO - Message has document: False
2025-07-20 22:10:15,300 - handlers.session_handlers - INFO - Message text: '1'
2025-07-20 22:10:24,641 - handlers.user_handlers - INFO - User ********** redeemed link code 9LYZGBUR for ₹1.0
2025-07-20 22:12:54,035 - utils.helpers - WARNING - Sync is_admin() called from async context for user ********** - consider using is_admin_async()
2025-07-20 22:12:54,036 - services.admin_service - ERROR - Error getting admin settings for **********: Task <Task pending name='Task-110' coro=<_check_admin_in_new_loop() running at C:\Users\<USER>\Desktop\referbot\python\utils\helpers.py:535> cb=[_run_until_complete_cb() at C:\Python312\Lib\asyncio\base_events.py:182]> got Future <Future pending cb=[_chain_future.<locals>._call_check_cancel() at C:\Python312\Lib\asyncio\futures.py:391]> attached to a different loop
2025-07-20 22:14:37,680 - services.user_service - INFO - Created new user: 6041462550 (Aayat)
2025-07-20 22:14:38,818 - handlers.user_handlers - ERROR - Error sending new user notification: Forbidden: bot is not a member of the channel chat
2025-07-20 22:14:40,100 - handlers.user_handlers - INFO - Notification sent to referrer 1297436752 about new user Aayat (ID: 6041462550): success
2025-07-20 22:15:34,993 - handlers.user_handlers - INFO - Custom referral parameter used: from_channel -> User ID: **********
2025-07-20 22:15:55,656 - handlers.user_handlers - ERROR - Error showing main menu: Message to edit not found
2025-07-20 22:15:55,863 - handlers.callback_handlers - ERROR - Error in _handle_check_subscription: Message to edit not found
2025-07-20 22:16:13,205 - __main__ - INFO - Received signal 2, initiating shutdown...
2025-07-20 22:16:13,206 - __main__ - INFO - Shutting down bot...
2025-07-20 22:16:13,407 - apscheduler.scheduler - INFO - Scheduler has been shut down
2025-07-20 22:16:13,450 - config.database - INFO - Disconnected from MongoDB
2025-07-20 22:16:13,451 - __main__ - INFO - Bot shutdown completed
