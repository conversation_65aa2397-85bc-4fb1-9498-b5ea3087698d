#!/usr/bin/env python3
"""
Final bulk fix for all remaining is_admin() calls
"""

import os

# Change to the correct directory
os.chdir('handlers')

# Read the file
with open('admin_handlers.py', 'r', encoding='utf-8') as f:
    content = f.read()

# Count original occurrences
original_count = content.count('if not is_admin(')
print(f"Found {original_count} remaining 'if not is_admin(' patterns")

# Replace all remaining patterns
content = content.replace('if not is_admin(', 'if not await is_admin_async(')

# Count after replacement
new_count = content.count('if not await is_admin_async(')
print(f"Now have {new_count} 'if not await is_admin_async(' patterns")

# Write back
with open('admin_handlers.py', 'w', encoding='utf-8') as f:
    f.write(content)

print(f"✅ Fixed {original_count} admin check calls")
