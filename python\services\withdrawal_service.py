"""
Withdrawal service for managing withdrawals and account information
Maintains identical functionality to PHP version
"""

import logging
import random
import requests
from typing import Optional, Dict, Any, List
from telegram import Bo<PERSON>

from config.database import get_collection, COLLECTIONS
from config.settings import settings
from models.withdrawal import WithdrawalModel, AccountInfoModel
from utils.helpers import (
    get_current_timestamp, 
    get_current_date, 
    send_safe_message, 
    get_all_admin_ids,
    is_valid_email,
    is_valid_mobile_number,
    is_valid_ifsc_code,
    is_valid_account_number
)

logger = logging.getLogger(__name__)

class WithdrawalService:
    """Service for withdrawal-related operations"""

    def __init__(self):
        self.bot = Bot(settings.BOT_TOKEN)
        self._pending_withdrawals_cache = None
        self._cache_timestamp = 0
        self._cache_duration = 30  # Cache for 30 seconds
    
    async def process_withdrawal_request(
        self, 
        user_id: int, 
        amount: int, 
        withdrawal_method: str = 'bank'
    ) -> Dict[str, Any]:
        """Process withdrawal request (matching PHP logic exactly)"""
        try:
            from services.user_service import UserService
            from services.admin_service import AdminService
            
            user_service = UserService()
            admin_service = AdminService()
            
            # Get user data
            user = await user_service.get_user(user_id)
            if not user:
                return {'success': False, 'error': 'User not found'}
            
            # Validate withdrawal amount
            user_balance = user.get('balance', 0)
            validation = WithdrawalModel.validate_withdrawal_amount(amount, user_balance, settings.MIN_WITHDRAWAL_AMOUNT)
            
            if not validation['valid']:
                return {'success': False, 'error': validation['errors'][0]}
            
            # Check if user already has pending withdrawal
            if user.get('withdraw_under_review', 0) > 0:
                return {'success': False, 'error': 'You already have a pending withdrawal request'}
            
            # Validate account information
            account_info = user.get('account_info', {})
            if withdrawal_method == 'usdt':
                account_validation = AccountInfoModel.validate_usdt_account_info(account_info)
            else:
                account_validation = AccountInfoModel.validate_bank_account_info(account_info)
            
            if not account_validation['valid']:
                return {'success': False, 'error': 'Incomplete account information', 'missing_fields': account_validation['missing_fields']}
            
            # Get withdrawal settings and calculate tax
            withdrawal_settings = await admin_service.get_withdrawal_settings()
            tax_calculation = WithdrawalModel.calculate_withdrawal_tax(amount, withdrawal_settings)
            
            # Check if withdrawals are enabled
            if not withdrawal_settings.get('enabled', True):
                return {'success': False, 'error': 'Withdrawals are currently disabled'}
            
            # Deduct amount from user balance and set under review
            if not await user_service.update_user_balance(user_id, amount, 'subtract'):
                return {'success': False, 'error': 'Failed to process withdrawal'}
            
            if not await user_service.set_withdrawal_under_review(user_id, amount):
                # Rollback balance deduction
                await user_service.update_user_balance(user_id, amount, 'add')
                return {'success': False, 'error': 'Failed to process withdrawal'}
            
            # Create withdrawal record
            date = get_current_date()
            withdrawal_record = WithdrawalModel.create_withdrawal_record(
                user_id=user_id,
                amount=amount,
                status="Under review",
                method=withdrawal_method,
                date=date
            )

            # Add to user's withdrawal reports
            await user_service.add_withdrawal_report(user_id, withdrawal_record)

            # Save to central withdrawals collection for statistics
            await self._save_withdrawal_to_database(user_id, amount, withdrawal_method, tax_calculation, "pending")
            
            # Notify all admins with tax calculation
            await self._notify_admins_new_withdrawal(user, amount, withdrawal_method, tax_calculation)
            
            # Format confirmation message
            confirmation_message = WithdrawalModel.format_withdrawal_confirmation_message(
                user, amount, tax_calculation, date
            )
            
            return {
                'success': True,
                'message': confirmation_message,
                'tax_calculation': tax_calculation
            }
            
        except Exception as e:
            logger.error(f"Error processing withdrawal request: {e}")
            return {'success': False, 'error': 'Internal error occurred'}
    
    async def approve_withdrawal(self, user_id: int, admin_id: int) -> bool:
        """Approve withdrawal request (matching PHP logic exactly)"""
        try:
            from services.user_service import UserService
            user_service = UserService()
            
            # Get user data
            user = await user_service.get_user(user_id)
            if not user:
                logger.error(f"User {user_id} not found for withdrawal approval")
                return False
            
            amount = user.get('withdraw_under_review', 0)
            if amount <= 0:
                logger.error(f"No pending withdrawal for user {user_id}")
                return False
            
            # Update user data: add to successful withdrawals, clear under review
            if not await user_service.complete_withdrawal(user_id, 'Passed'):
                return False
            
            # Update withdrawal record status
            date = get_current_date()
            await user_service.update_withdrawal_record_status(user_id, amount, 'Passed')
            
            # Notify user
            approval_message = WithdrawalModel.format_withdrawal_approval_message(user, amount, date)
            await send_safe_message(self.bot, user_id, approval_message, parse_mode='HTML')
            
            logger.info(f"Withdrawal approved: User {user_id}, Amount ₹{amount}")
            return True
            
        except Exception as e:
            logger.error(f"Error approving withdrawal for user {user_id}: {e}")
            return False
    
    async def reject_withdrawal(self, user_id: int, admin_id: int) -> bool:
        """Reject withdrawal request with enhanced error handling"""
        try:
            from services.user_service import UserService
            user_service = UserService()

            logger.info(f"Starting withdrawal rejection for user {user_id} by admin {admin_id}")

            # Get user data
            user = await user_service.get_user(user_id)
            if not user:
                logger.error(f"User {user_id} not found for withdrawal rejection")
                return False

            amount = user.get('withdraw_under_review', 0)
            if amount <= 0:
                logger.error(f"No pending withdrawal for user {user_id} (amount: {amount})")
                return False

            logger.info(f"Rejecting withdrawal: User {user_id}, Amount ₹{amount}")

            # Update user data: return money to balance, clear under review
            try:
                if not await user_service.complete_withdrawal(user_id, 'Failed'):
                    logger.error(f"Failed to complete withdrawal rejection for user {user_id}")
                    return False
                logger.info(f"Successfully updated user balance for rejection: User {user_id}")
            except Exception as e:
                logger.error(f"Error completing withdrawal rejection for user {user_id}: {e}")
                return False

            # Update withdrawal record status
            try:
                date = get_current_date()
                await user_service.update_withdrawal_record_status(user_id, amount, 'Failed')
                logger.info(f"Successfully updated withdrawal record status for user {user_id}")
            except Exception as e:
                logger.error(f"Error updating withdrawal record status for user {user_id}: {e}")
                # Continue even if this fails, as the main operation succeeded

            # Notify user
            try:
                rejection_message = WithdrawalModel.format_withdrawal_rejection_message(user, amount, date)
                await send_safe_message(self.bot, user_id, rejection_message, parse_mode='HTML')
                logger.info(f"Successfully sent rejection notification to user {user_id}")
            except Exception as e:
                logger.error(f"Error sending rejection notification to user {user_id}: {e}")
                # Continue even if notification fails, as the main operation succeeded

            logger.info(f"Withdrawal rejected successfully: User {user_id}, Amount ₹{amount}")
            return True

        except Exception as e:
            logger.error(f"Unexpected error rejecting withdrawal for user {user_id}: {e}")
            return False
    
    async def _notify_admins_new_withdrawal(
        self,
        user: Dict[str, Any],
        amount: int,
        withdrawal_method: str,
        tax_calculation: Dict[str, Any] = None
    ) -> None:
        """Notify configured admin about new withdrawal request with tax information"""
        try:
            from config.settings import settings

            # Use configured withdrawal notification admin ID, fallback to all admins if not configured
            withdrawal_admin_id = settings.WITHDRAWAL_NOTIFICATION_ADMIN_ID
            if withdrawal_admin_id and withdrawal_admin_id != 0:
                admin_ids = [withdrawal_admin_id]
            else:
                # Fallback to all admins if not configured
                admin_ids = get_all_admin_ids()

            admin_message = WithdrawalModel.format_admin_notification_message(
                user, amount, withdrawal_method, tax_calculation
            )
            
            from telegram import InlineKeyboardButton, InlineKeyboardMarkup
            
            admin_keyboard = InlineKeyboardMarkup([
                [
                    InlineKeyboardButton('✅ Approve', callback_data=f"approve_withdrawal_{user['user_id']}"),
                    InlineKeyboardButton('❌ Reject', callback_data=f"reject_withdrawal_{user['user_id']}")
                ]
            ])
            
            for admin_id in admin_ids:
                try:
                    await send_safe_message(
                        self.bot,
                        admin_id,
                        admin_message,
                        reply_markup=admin_keyboard,
                        parse_mode='HTML'
                    )
                except Exception as e:
                    logger.error(f"Failed to notify admin {admin_id}: {e}")
            
        except Exception as e:
            logger.error(f"Error notifying admins about new withdrawal: {e}")
    
    async def get_withdrawal_amounts(self, user_balance: int) -> List[int]:
        """Get available withdrawal amounts based on user balance"""
        all_amounts = WithdrawalModel.get_withdrawal_amounts()
        return [amount for amount in all_amounts if amount <= user_balance]
    
    async def check_duplicate_account_number(self, account_number: str, exclude_user_id: int = None) -> bool:
        """Check if account number already exists for another user"""
        try:
            from services.user_service import UserService
            user_service = UserService()

            # Get all users
            all_users = await user_service.get_all_users()

            for user in all_users:
                # Skip the current user (allow self-updates)
                if exclude_user_id and user.get('user_id') == exclude_user_id:
                    continue

                # Check if this user has the same account number
                user_account_info = user.get('account_info', {})
                user_account_number = user_account_info.get('account_number', '').strip()

                if user_account_number and user_account_number == account_number.strip():
                    return True  # Duplicate found

            return False  # No duplicate found

        except Exception as e:
            logger.error(f"Error checking duplicate account number: {e}")
            return False  # Assume no duplicate on error to avoid blocking legitimate users

    async def update_account_info(self, user_id: int, field: str, value: str) -> bool:
        """Update account information field"""
        try:
            from services.user_service import UserService
            user_service = UserService()

            # Validate field
            allowed_fields = ['name', 'ifsc', 'email', 'account_number', 'mobile_number', 'usdt_address', 'binance_id', 'withdrawal_method']
            if field not in allowed_fields:
                return False

            # Field-specific validation
            if field == 'email' and not is_valid_email(value):
                return False
            elif field == 'mobile_number' and not is_valid_mobile_number(value):
                return False
            elif field == 'ifsc' and not is_valid_ifsc_code(value):
                return False
            elif field == 'account_number' and not is_valid_account_number(value):
                return False

            # Check for duplicate account number (only for account_number field)
            if field == 'account_number':
                if await self.check_duplicate_account_number(value, exclude_user_id=user_id):
                    return False  # Duplicate found, don't update

            return await user_service.update_account_info(user_id, field, value)

        except Exception as e:
            logger.error(f"Error updating account info for user {user_id}: {e}")
            return False
    
    async def send_otp(self, mobile_number: str) -> Optional[str]:
        """Send OTP to mobile number with improved error handling"""
        try:
            from services.admin_service import AdminService
            admin_service = AdminService()

            # Try to get OTP API key from admin settings first, then fallback to environment
            otp_api_key = await admin_service.get_admin_setting('otp_website_api_key')

            if not otp_api_key:
                # Fallback to environment variable
                otp_api_key = settings.OTP_API_KEY
                logger.info("Using OTP API key from environment variable")
            else:
                logger.info("Using OTP API key from admin settings")

            if not otp_api_key:
                logger.error("OTP API key not configured in admin settings or environment")
                return None

            # Validate and format mobile number for the API
            if not mobile_number.startswith('+'):
                logger.error(f"Invalid mobile number format: {mobile_number} (missing country code)")
                return None

            # Convert international format to API format
            # API expects format without + and country code for Indian numbers
            if mobile_number.startswith('+91'):
                # For Indian numbers, remove +91 prefix
                api_phone_number = mobile_number[3:]  # Remove +91
                logger.info(f"Converted {mobile_number} to API format: {api_phone_number}")
            else:
                # For other countries, remove + but keep country code
                api_phone_number = mobile_number[1:]  # Remove +
                logger.info(f"Converted {mobile_number} to API format: {api_phone_number}")

            # Validate the converted number
            if len(api_phone_number) < 10:
                logger.error(f"Invalid phone number length after conversion: {api_phone_number}")
                return None

            # Generate 4-digit OTP
            otp = str(random.randint(1000, 9999))

            # Prepare API URL with converted phone number
            api_url = f"{settings.OTP_API_URL}?API={otp_api_key}&PHONE={api_phone_number}&OTP={otp}"
            logger.info(f"Sending OTP to {mobile_number} (API format: {api_phone_number}) using URL: {settings.OTP_API_URL}")

            try:
                response = requests.get(api_url, timeout=30)
                logger.info(f"OTP API response for {mobile_number}: Status {response.status_code}, Content: {response.text}")

                # Enhanced response analysis
                response_text = response.text.lower() if response.text else ""

                # Check for explicit failure indicators in response
                failure_indicators = ['error', 'failed', 'invalid', 'false', 'blocked', 'rejected', 'unauthorized']
                success_indicators = ['success', 'sent', 'delivered', 'ok', 'true']

                has_failure = any(indicator in response_text for indicator in failure_indicators)
                has_success = any(indicator in response_text for indicator in success_indicators)

                # Log detailed analysis
                logger.info(f"OTP API analysis for {mobile_number} (API format: {api_phone_number}): Status={response.status_code}, "
                           f"HasFailure={has_failure}, HasSuccess={has_success}, "
                           f"ResponseLength={len(response.text)}")

                # Check if the response indicates success
                if response.status_code == 200:
                    if has_failure:
                        logger.warning(f"OTP API returned 200 but response contains failure indicators: {response.text}")
                        logger.warning(f"This suggests API call succeeded but SMS delivery may have failed")
                        return None
                    else:
                        # Log successful OTP API call (note: this doesn't guarantee SMS delivery)
                        logger.info(f"OTP API call successful for {mobile_number}. OTP: {otp}")
                        logger.info(f"Note: SMS delivery may take 1-5 minutes depending on carrier")
                        return otp
                else:
                    logger.error(f"OTP API returned error status {response.status_code}: {response.text}")
                    return None

            except requests.Timeout:
                logger.error(f"Timeout while sending OTP to {mobile_number}")
                return None
            except requests.ConnectionError:
                logger.error(f"Connection error while sending OTP to {mobile_number}")
                return None
            except requests.RequestException as e:
                logger.error(f"Request exception while sending OTP to {mobile_number}: {e}")
                return None

        except Exception as e:
            logger.error(f"Unexpected error sending OTP to {mobile_number}: {e}")
            return None
    
    async def validate_otp(self, entered_otp: str, expected_otp: str, otp_timestamp: int) -> bool:
        """Validate OTP (matching PHP logic exactly)"""
        try:
            # Check OTP expiry (5 minutes = 300 seconds)
            current_time = get_current_timestamp()
            if current_time - otp_timestamp > 300:
                return False
            
            # Check OTP match
            return entered_otp == expected_otp
            
        except Exception as e:
            logger.error(f"Error validating OTP: {e}")
            return False
    
    async def get_withdrawal_statistics(self) -> Dict[str, Any]:
        """Get withdrawal statistics"""
        try:
            from services.user_service import UserService
            user_service = UserService()
            
            all_users = await user_service.get_all_users()
            
            total_successful_withdrawals = sum(user.get('successful_withdraw', 0) for user in all_users)
            total_pending_withdrawals = sum(user.get('withdraw_under_review', 0) for user in all_users)
            users_with_withdrawals = len([user for user in all_users if user.get('successful_withdraw', 0) > 0])
            users_with_pending = len([user for user in all_users if user.get('withdraw_under_review', 0) > 0])
            
            return {
                'total_successful_withdrawals': total_successful_withdrawals,
                'total_pending_withdrawals': total_pending_withdrawals,
                'users_with_withdrawals': users_with_withdrawals,
                'users_with_pending': users_with_pending
            }
            
        except Exception as e:
            logger.error(f"Error getting withdrawal statistics: {e}")
            return {
                'total_successful_withdrawals': 0,
                'total_pending_withdrawals': 0,
                'users_with_withdrawals': 0,
                'users_with_pending': 0
            }
    
    async def get_pending_withdrawals_fast(self) -> List[Dict[str, Any]]:
        """Fast implementation bypassing potential bottlenecks"""
        try:
            users_collection = await get_collection(COLLECTIONS['users'])

            # Simple, fast query
            cursor = users_collection.find(
                {"withdraw_under_review": {"$gt": 0}},
                {
                    "user_id": 1,
                    "first_name": 1,
                    "username": 1,
                    "withdraw_under_review": 1,
                    "account_info.withdrawal_method": 1,
                    "account_info.account_number": 1,
                    "account_info.usdt_address": 1
                }
            ).sort("withdraw_under_review", -1)

            users_with_pending = await cursor.to_list(length=None)

            # Minimal processing
            pending_withdrawals = []
            for user in users_with_pending:
                account_info = user.get('account_info', {})

                pending_withdrawals.append({
                    'user_id': user['user_id'],
                    'first_name': str(user.get('first_name', 'Unknown')).replace('<', '').replace('>', ''),
                    'username': str(user.get('username', '')).replace('<', '').replace('>', ''),
                    'amount': user.get('withdraw_under_review', 0),
                    'withdrawal_method': account_info.get('withdrawal_method', 'bank'),
                    'account_info': account_info
                })

            return pending_withdrawals

        except Exception as e:
            logger.error(f"Error in get_pending_withdrawals_fast: {e}")
            return []

    async def get_pending_withdrawals(self) -> List[Dict[str, Any]]:
        """Get all users with pending withdrawals with caching and timeout handling"""
        import time

        # Check cache first
        current_time = time.time()
        if (self._pending_withdrawals_cache is not None and
            current_time - self._cache_timestamp < self._cache_duration):
            logger.info(f"Returning cached pending withdrawals ({len(self._pending_withdrawals_cache)} items)")
            return self._pending_withdrawals_cache

        try:
            # Try fast implementation with timeout
            import asyncio

            result = await asyncio.wait_for(
                self.get_pending_withdrawals_fast(),
                timeout=10.0  # 10 second timeout
            )

            # Update cache
            self._pending_withdrawals_cache = result
            self._cache_timestamp = current_time

            return result

        except asyncio.TimeoutError:
            logger.error("get_pending_withdrawals timed out after 10 seconds")
            # Return cached data if available, otherwise empty list
            if self._pending_withdrawals_cache is not None:
                logger.info("Returning stale cached data due to timeout")
                return self._pending_withdrawals_cache
            return []

        except Exception as e:
            logger.error(f"Error in get_pending_withdrawals: {e}")
            # Return cached data if available, otherwise empty list
            if self._pending_withdrawals_cache is not None:
                logger.info("Returning cached data due to error")
                return self._pending_withdrawals_cache
            return []

    async def _save_withdrawal_to_database(self, user_id: int, amount: int, withdrawal_method: str, tax_calculation: Dict[str, Any], status: str = "pending") -> bool:
        """Save withdrawal record to central database for statistics tracking"""
        try:
            from config.database import get_collection, COLLECTIONS
            from utils.helpers import generate_withdrawal_id

            withdrawals_collection = await get_collection(COLLECTIONS['withdrawals'])

            # Get user info for record
            from services.user_service import UserService
            user_service = UserService()
            user = await user_service.get_user(user_id)

            if not user:
                logger.error(f"User {user_id} not found for withdrawal record")
                return False

            # Create comprehensive withdrawal record
            withdrawal_record = {
                "withdrawal_id": generate_withdrawal_id(),
                "user_id": user_id,
                "username": user.get('username', ''),
                "first_name": user.get('first_name', 'Unknown'),
                "amount": amount,
                "amount_with_tax": tax_calculation.get('final_amount', amount),
                "tax_deducted": tax_calculation.get('tax_deducted', 0),
                "withdrawal_method": withdrawal_method,
                "status": status,
                "created_at": get_current_timestamp(),
                "updated_at": get_current_timestamp(),
                "processed_at": None,
                "admin_note": "",
                "account_info": user.get('bank_details', {}) if withdrawal_method == 'bank' else {'usdt_address': user.get('usdt_address', '')}
            }

            result = await withdrawals_collection.insert_one(withdrawal_record)

            if result.inserted_id:
                logger.info(f"Withdrawal record saved for user {user_id}: {withdrawal_record['withdrawal_id']}")
                return True
            else:
                logger.error(f"Failed to save withdrawal record for user {user_id}")
                return False

        except Exception as e:
            logger.error(f"Error saving withdrawal to database: {e}")
            return False

    async def update_withdrawal_status(self, withdrawal_id: str, status: str, admin_note: str = "") -> bool:
        """Update withdrawal status in database"""
        try:
            from config.database import get_collection, COLLECTIONS

            withdrawals_collection = await get_collection(COLLECTIONS['withdrawals'])

            update_data = {
                "status": status,
                "updated_at": get_current_timestamp(),
                "admin_note": admin_note
            }

            if status in ["completed", "approved"]:
                update_data["processed_at"] = get_current_timestamp()

            result = await withdrawals_collection.update_one(
                {"withdrawal_id": withdrawal_id},
                {"$set": update_data}
            )

            return result.modified_count > 0

        except Exception as e:
            logger.error(f"Error updating withdrawal status: {e}")
            return False
